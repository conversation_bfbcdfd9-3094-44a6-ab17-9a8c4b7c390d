apply plugin: 'com.android.library'

android {
    compileSdkVersion 33
    buildToolsVersion "29.0.3"

    defaultConfig {
        minSdkVersion 9
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.2.0'
}

//apply plugin: 'com.android.library'
//apply plugin: 'com.github.dcendents.android-maven'
//apply plugin: 'com.jfrog.bintray'
//
//version = "1.6.0" // #修改# // 这里是aar的版本号
//
//android {
//  compileSdkVersion 25
//  buildToolsVersion "25.0.2"
//  resourcePrefix 'stickerview_'
//
//  defaultConfig {
//    minSdkVersion 9
//    targetSdkVersion 25
//    versionCode 1
//    versionName "1.0"
//
//    testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
//  }
//  buildTypes {
//    release {
//      minifyEnabled false
//      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//    }
//  }
//
//  lintOptions {
//    abortOnError false
//  }
//}
//
//dependencies {
//  compile fileTree(include: ['*.jar'], dir: 'libs')
//  compile "com.android.support:appcompat-v7:$rootProject.ext.supportLibraryVersion"
//}
//
//def siteUrl = 'https://github.com/wuapnjie/StickerView'
//// #修改# // 项目的主页地址，我这里是我的PickerView项目在github的链接地址
//def gitUrl = 'https://github.com/wuapnjie/StickerView.git'
//// #修改# // 项目 git 地址，我这里同样是用Github上的git地址
//group =
//    "com.flying.xiaopo" // #修改# // 组名称，这个相当于依赖的时候 compile 'com.bigkoo:pickerview:1.0.1' “:”号前面的前缀
//
//install {
//  repositories.mavenInstaller {
//    // This generates POM.xml with proper parameters
//    pom {
//      project {
//        packaging 'aar'
//        name 'Sticker support For Android' // #修改# // 标题
//        url siteUrl
//        // Set your license
//        licenses {
//          license {
//            name 'The Apache Software License, Version 2.0'
//            url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
//          }
//        }
//        developers {
//          developer {
//            id 'wuapnjie'
//            // #修改# // 你的userid，昵称
//            name 'wupanjie' // #修改# // 用户名
//            email '<EMAIL>' // #修改# // 邮箱
//          }
//        }
//        scm {
//          connection gitUrl
//          developerConnection gitUrl
//          url siteUrl
//        }
//      }
//    }
//  }
//}
//
//task sourcesJar(type: Jar) {
//  from android.sourceSets.main.java.srcDirs
//  classifier = 'sources'
//}
//
//task javadoc(type: Javadoc) {
//  source = android.sourceSets.main.java.srcDirs
//  classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
//}
//
//task javadocJar(type: Jar, dependsOn: javadoc) {
//  classifier = 'javadoc'
//  from javadoc.destinationDir
//}
//
//artifacts {
//  archives javadocJar
//  archives sourcesJar
//}
//
//Properties properties = new Properties()
//properties.load(project.rootProject.file('local.properties').newDataInputStream())
//bintray {
//  user = properties.getProperty("bintray.user")
//  key = properties.getProperty("bintray.apikey")
//  // 上面两个 user和key 需要留意一下，在local.properites 里面配置的
//  configurations = ['archives']
//  pkg {
//    repo = "maven"
//    name = "stickerview"
//    // #修改# //  在 jcenter 上面的项目名字
//    websiteUrl = siteUrl
//    vcsUrl = gitUrl
//    licenses = ["Apache-2.0"]
//    publish = true
//  }
//}

tasks.withType(Javadoc) {
  options.addStringOption('Xdoclint:none', '-quiet')
  options.addStringOption('encoding', 'UTF-8')
  options.addStringOption('charSet', 'UTF-8')
}

