package com.xiaopo.flying.sticker;

import android.view.MotionEvent;

/**
 * <AUTHOR>
 */

public class DeleteIconEvent implements StickerIconEvent {
  public void onActionDown(StickerView stickerView, Sticker sticker, MotionEvent motionEvent) {
  }

  public void onActionMove(StickerView stickerView, MotionEvent motionEvent) {
  }

  public void onActionUp(StickerView stickerView, MotionEvent motionEvent) {
    stickerView.removeCurrentSticker();
  }
}
