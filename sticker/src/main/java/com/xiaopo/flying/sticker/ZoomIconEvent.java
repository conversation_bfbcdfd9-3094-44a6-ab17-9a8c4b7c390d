package com.xiaopo.flying.sticker;

import android.view.MotionEvent;

/**
 * <AUTHOR>
 */

public class ZoomIconEvent implements StickerIconEvent {
    public void onActionDown(StickerView stickerView, Sticker sticker, MotionEvent motionEvent) {
    }

    public void onActionMove(StickerView stickerView, MotionEvent motionEvent) {
        stickerView.zoomAndRotateCurrentSticker(motionEvent);
    }

    public void onActionUp(StickerView stickerView, MotionEvent motionEvent) {
        if (stickerView.getOnStickerOperationListener() != null && stickerView.getCurrentSticker() != null) {
            stickerView.getOnStickerOperationListener().onStickerZoomFinished(stickerView.getCurrentSticker());
        }
    }
}
