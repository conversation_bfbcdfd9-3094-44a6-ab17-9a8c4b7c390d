<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <application
        android:allowBackup="true"
        android:largeHeap="true"
        android:hardwareAccelerated="true"
        android:label="@string/app_name" >
        <activity
            android:name=".ScanActivity"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name" >
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.scanlibrary.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
    </application>

</manifest>
