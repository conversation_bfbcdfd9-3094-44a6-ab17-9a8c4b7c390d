<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical">

        <Button
            android:id="@+id/original"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:drawableTop="@drawable/ic_image"
            android:text="Original"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/magicColor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:drawableTop="@drawable/ic_enhamce_image"
            android:singleLine="true"
            android:text="Magic color"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/grayMode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:drawableTop="@drawable/ic_gray"
            android:singleLine="true"
            android:text="Gray Mode"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/BWMode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:drawableTop="@drawable/ic_black_and_white"
            android:singleLine="true"
            android:text="B and W"
            android:textColor="@android:color/white" />
    </LinearLayout>

    <Button
        android:id="@+id/doneButton"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/bottom_background_color"
        android:orientation="horizontal"
        android:padding="@dimen/bottom_bar_padding"
        android:text="@string/done" />


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/doneButton"
        android:layout_below="@id/topBar"
        android:layout_gravity="center"
        android:layout_margin="@dimen/scanPadding">

        <ImageView
            android:id="@+id/scannedImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/scanPadding" />
    </FrameLayout>


</RelativeLayout>