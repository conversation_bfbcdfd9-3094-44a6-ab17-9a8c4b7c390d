<?xml version="1.0"?>
<!----------------------------------------------------------------------------
 A frontal cat face detector using the full set of Haar features, i.e.
 horizontal, vertical, and diagonal features.

 Contributed by <PERSON> (<EMAIL>).

 More information can be found in the following publications and
 presentations:

 <PERSON>. OpenCV for Secret Agents (book). Packt Publishing, January
   2015.
 <PERSON>. "Training Detectors and Recognizers in Python and OpenCV"
   (tutorial). ISMAR 2014. September 9, 2014.
   http://nummist.com/opencv/Howse_ISMAR_20140909.pdf
 <PERSON>. "Training Intelligent Camera Systems with Python and OpenCV"
   (webcast). O’Reilly Media. June 17, 2014.
   http://www.oreilly.com/pub/e/3077

 Build scripts and demo applications can be found in the following repository:
 https://bitbucket.org/<PERSON>_<PERSON><PERSON>/angora-blue

 KNOWN LIMITATIONS:

 Sometimes, the detector mistakenly thinks that a human face is a cat face. In
 situations where either a human or a cat might be encountered, use both a
 human face detector and a cat face detector. Then, if a detected human face
 and a detected cat face intersect, reject the cat face.

 An upright subject is assumed. In situations where the cat's face might be
 sideways or upside down (e.g. the cat is rolling over), try various rotations
 of the input image.

 //////////////////////////////////////////////////////////////////////////
 | Contributors License Agreement
 | IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
 |   By downloading, copying, installing or using the software you agree
 |   to this license.
 |   If you do not agree to this license, do not download, install,
 |   copy or use the software.
 |
 | Copyright (c) 2014, Joseph Howse (Nummist Media Corporation Limited,
 | Halifax, Nova Scotia, Canada). All rights reserved.
 |
 | Redistribution and use in source and binary forms, with or without
 | modification, are permitted provided that the following conditions are
 | met:
 |
 |    * Redistributions of source code must retain the above copyright
 |       notice, this list of conditions and the following disclaimer.
 |    * Redistributions in binary form must reproduce the above
 |      copyright notice, this list of conditions and the following
 |      disclaimer in the documentation and/or other materials provided
 |      with the distribution.
 |    * The name of Contributor may not used to endorse or promote products
 |      derived from this software without specific prior written permission.
 |
 | THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 | "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 | LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 | A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 | CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 | EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 | PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 | PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 | LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 | NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 | SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  Back to
 | Top
 //////////////////////////////////////////////////////////////////////////
 ---------------------------------------------------------------------------->
<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>24</height>
  <width>24</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9900001287460327e-01</minHitRate>
    <maxFalseAlarm>5.0000000000000000e-01</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-01</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount>
    <featSize>1</featSize>
    <mode>ALL</mode></featureParams>
  <stageNum>15</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>28</maxWeakCount>
      <stageThreshold>-2.0972909927368164e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 388 -1.4772760681807995e-02</internalNodes>
          <leafValues>
            8.4035199880599976e-01 -1.2701500952243805e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 736 4.5831585302948952e-03</internalNodes>
          <leafValues>
            -2.3791725933551788e-01 6.1978793144226074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 -1.5044892206788063e-02</internalNodes>
          <leafValues>
            5.7160794734954834e-01 -2.0493283867835999e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 437 -1.5646889805793762e-02</internalNodes>
          <leafValues>
            7.6283878087997437e-01 -1.6358052194118500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 432 3.0781796202063560e-02</internalNodes>
          <leafValues>
            -1.8158669769763947e-01 7.5050812959671021e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 1.8483418971300125e-02</internalNodes>
          <leafValues>
            -2.0087972283363342e-01 5.2843624353408813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 680 1.3191045261919498e-02</internalNodes>
          <leafValues>
            -1.5244702994823456e-01 5.8166426420211792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 5.3334265947341919e-02</internalNodes>
          <leafValues>
            -1.6860350966453552e-01 7.1358704566955566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 8.3916599396616220e-04</internalNodes>
          <leafValues>
            -2.1746076643466949e-01 4.2143425345420837e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 1.7697989940643311e-02</internalNodes>
          <leafValues>
            -1.3514791429042816e-01 6.1385941505432129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 -2.8310909867286682e-02</internalNodes>
          <leafValues>
            5.3606474399566650e-01 -1.5554395318031311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 4.1034919559024274e-04</internalNodes>
          <leafValues>
            -2.8903219103813171e-01 3.1018218398094177e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 537 3.9831817150115967e-02</internalNodes>
          <leafValues>
            -1.8419378995895386e-01 4.3500679731369019e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 -5.2749719470739365e-03</internalNodes>
          <leafValues>
            -8.7773287296295166e-01 1.1703799664974213e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 -3.6777660250663757e-02</internalNodes>
          <leafValues>
            4.1285938024520874e-01 -2.1606418490409851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 9.4376102089881897e-02</internalNodes>
          <leafValues>
            -1.0109311342239380e-01 6.0879749059677124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 564 -1.6132533550262451e-02</internalNodes>
          <leafValues>
            5.1245921850204468e-01 -1.5503944456577301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 507 -6.9251265376806259e-03</internalNodes>
          <leafValues>
            4.2284211516380310e-01 -1.5949958562850952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 882 -8.4776207804679871e-03</internalNodes>
          <leafValues>
            4.0007081627845764e-01 -1.6089719533920288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 -9.0452972799539566e-03</internalNodes>
          <leafValues>
            -7.6785671710968018e-01 9.3979701399803162e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 3.0019454658031464e-02</internalNodes>
          <leafValues>
            -1.3505084812641144e-01 4.7249373793601990e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 804 3.6142929457128048e-03</internalNodes>
          <leafValues>
            8.1217512488365173e-02 -7.7168470621109009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 806 -4.7642881982028484e-03</internalNodes>
          <leafValues>
            -7.8209573030471802e-01 6.2777772545814514e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 816 3.0351843684911728e-02</internalNodes>
          <leafValues>
            -1.1295587569475174e-01 5.8056473731994629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 -5.9288680553436279e-02</internalNodes>
          <leafValues>
            5.5029523372650146e-01 -1.1994160711765289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 2.2238820791244507e-02</internalNodes>
          <leafValues>
            -1.4121483266353607e-01 4.5770901441574097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 -4.1477128863334656e-02</internalNodes>
          <leafValues>
            5.7035386562347412e-01 -1.0164763778448105e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 710 -1.1866136919707060e-03</internalNodes>
          <leafValues>
            3.4064662456512451e-01 -1.6186751425266266e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>30</maxWeakCount>
      <stageThreshold>-1.5367478132247925e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 654 8.4006171673536301e-03</internalNodes>
          <leafValues>
            -1.0249307751655579e-01 7.6660197973251343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 -1.0703811421990395e-02</internalNodes>
          <leafValues>
            6.9929075241088867e-01 -1.6515852510929108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 6.3192001543939114e-03</internalNodes>
          <leafValues>
            -2.2415910661220551e-01 4.3903940916061401e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 651 2.0642649382352829e-02</internalNodes>
          <leafValues>
            -1.8032769858837128e-01 5.8923733234405518e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 4.0111690759658813e-03</internalNodes>
          <leafValues>
            -2.4117745459079742e-01 5.4825514554977417e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 173 2.0640484988689423e-02</internalNodes>
          <leafValues>
            -2.4204613268375397e-01 4.0820708870887756e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 -9.2923976480960846e-03</internalNodes>
          <leafValues>
            3.6315548419952393e-01 -2.0153710246086121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 3.5129714757204056e-02</internalNodes>
          <leafValues>
            -1.3475686311721802e-01 6.5953320264816284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 2.0278891548514366e-02</internalNodes>
          <leafValues>
            -1.0143157839775085e-01 5.9142571687698364e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 737 6.4985908102244139e-04</internalNodes>
          <leafValues>
            -1.9716840982437134e-01 3.4134888648986816e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -6.6223340108990669e-03</internalNodes>
          <leafValues>
            -6.9885939359664917e-01 9.7095526754856110e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 7.4231177568435669e-03</internalNodes>
          <leafValues>
            9.8552420735359192e-02 -6.5358603000640869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 692 -3.0081106349825859e-02</internalNodes>
          <leafValues>
            4.5352721214294434e-01 -1.4968612790107727e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 -6.0633812099695206e-02</internalNodes>
          <leafValues>
            6.5072047710418701e-01 -9.9382333457469940e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 -5.1941806450486183e-03</internalNodes>
          <leafValues>
            3.9397239685058594e-01 -1.6142791509628296e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 6.0986238531768322e-03</internalNodes>
          <leafValues>
            8.6411900818347931e-02 -7.3878693580627441e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 -8.2402750849723816e-03</internalNodes>
          <leafValues>
            -7.4236625432968140e-01 6.7853815853595734e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 908 -3.0396101996302605e-02</internalNodes>
          <leafValues>
            4.9337482452392578e-01 -1.3200622797012329e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 5.1566954702138901e-02</internalNodes>
          <leafValues>
            -1.3631668686866760e-01 4.2621469497680664e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 -9.3598978128284216e-04</internalNodes>
          <leafValues>
            3.2463693618774414e-01 -2.0737074315547943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 905 7.0394594222307205e-03</internalNodes>
          <leafValues>
            8.9326366782188416e-02 -6.1088448762893677e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 -6.5201576799154282e-03</internalNodes>
          <leafValues>
            3.7555626034736633e-01 -1.5273806452751160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 3.9127394556999207e-03</internalNodes>
          <leafValues>
            8.3254240453243256e-02 -7.3406547307968140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 9.5308097079396248e-03</internalNodes>
          <leafValues>
            -1.7045913636684418e-01 3.2230368256568909e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 1.5843525528907776e-02</internalNodes>
          <leafValues>
            -1.4033445715904236e-01 3.9502236247062683e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 555 5.9641832485795021e-03</internalNodes>
          <leafValues>
            6.4340040087699890e-02 -8.5145986080169678e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 -4.8106643371284008e-03</internalNodes>
          <leafValues>
            -6.9040679931640625e-01 6.5658122301101685e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 -9.3304895563051105e-04</internalNodes>
          <leafValues>
            4.1242164373397827e-01 -1.4879603683948517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 779 4.1272714734077454e-03</internalNodes>
          <leafValues>
            -1.2624038755893707e-01 4.6513134241104126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 2.2929732222110033e-03</internalNodes>
          <leafValues>
            1.0915581136941910e-01 -5.1519250869750977e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>44</maxWeakCount>
      <stageThreshold>-1.6336240768432617e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 334 2.5117650628089905e-02</internalNodes>
          <leafValues>
            3.5861257463693619e-02 8.1681501865386963e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 459 -6.6137146204710007e-03</internalNodes>
          <leafValues>
            4.6177890896797180e-01 -2.3009553551673889e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 -3.1423813197761774e-03</internalNodes>
          <leafValues>
            4.0471413731575012e-01 -2.0868653059005737e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 -3.7308693863451481e-03</internalNodes>
          <leafValues>
            3.2831424474716187e-01 -2.6703229546546936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 7.8482955694198608e-02</internalNodes>
          <leafValues>
            -1.5199472010135651e-01 4.6242395043373108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 772 -2.6338286697864532e-03</internalNodes>
          <leafValues>
            3.1739279627799988e-01 -2.3944588005542755e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 9.2347152531147003e-02</internalNodes>
          <leafValues>
            -1.5557752549648285e-01 6.0793381929397583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 -1.6786376014351845e-02</internalNodes>
          <leafValues>
            5.2824962139129639e-01 -1.1138658970594406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 -3.8150474429130554e-02</internalNodes>
          <leafValues>
            4.3382674455642700e-01 -1.4826944470405579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 4.3135927990078926e-03</internalNodes>
          <leafValues>
            1.1878431588411331e-01 -5.8886390924453735e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 7.1479372680187225e-02</internalNodes>
          <leafValues>
            -1.0972832888364792e-01 5.7183718681335449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 7.3613431304693222e-03</internalNodes>
          <leafValues>
            9.7729764878749847e-02 -6.5627050399780273e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 5.1306895911693573e-02</internalNodes>
          <leafValues>
            -1.6079875826835632e-01 4.0451571345329285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 -4.7303801402449608e-03</internalNodes>
          <leafValues>
            -6.5826851129531860e-01 8.7291486561298370e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 -1.8283914541825652e-03</internalNodes>
          <leafValues>
            3.7762144207954407e-01 -1.4564067125320435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 5.1737688481807709e-03</internalNodes>
          <leafValues>
            8.7748050689697266e-02 -6.2685465812683105e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 647 -3.2173446379601955e-03</internalNodes>
          <leafValues>
            -7.3641878366470337e-01 5.7915702462196350e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 -5.4384516552090645e-03</internalNodes>
          <leafValues>
            4.2479231953620911e-01 -1.2763169407844543e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 2.6621888391673565e-03</internalNodes>
          <leafValues>
            -2.1836103498935699e-01 3.1271252036094666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 1.2338031083345413e-02</internalNodes>
          <leafValues>
            7.9128213226795197e-02 -8.1891500949859619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 -1.0976660996675491e-02</internalNodes>
          <leafValues>
            2.9887822270393372e-01 -1.8205311894416809e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 1.4158659614622593e-03</internalNodes>
          <leafValues>
            8.9180864393711090e-02 -5.9163159132003784e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 -2.0067330449819565e-02</internalNodes>
          <leafValues>
            2.6213398575782776e-01 -1.7981344461441040e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 3.1120348721742630e-03</internalNodes>
          <leafValues>
            8.4207154810428619e-02 -5.7088595628738403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 -9.9351592361927032e-03</internalNodes>
          <leafValues>
            -7.2243571281433105e-01 5.1867216825485229e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 1.3314767275005579e-03</internalNodes>
          <leafValues>
            -1.7091234028339386e-01 2.5805294513702393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 3.5102412104606628e-02</internalNodes>
          <leafValues>
            -1.1150742322206497e-01 4.2247176170349121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 780 2.4332102388143539e-02</internalNodes>
          <leafValues>
            -1.2760649621486664e-01 3.5613566637039185e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 3.4916624426841736e-03</internalNodes>
          <leafValues>
            7.4707798659801483e-02 -6.2106835842132568e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 3.1960286200046539e-02</internalNodes>
          <leafValues>
            -8.5123799741268158e-02 5.5780071020126343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 2.5646466761827469e-02</internalNodes>
          <leafValues>
            9.6616283059120178e-02 -4.8778736591339111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 4.8584854230284691e-03</internalNodes>
          <leafValues>
            5.4295353591442108e-02 -6.2732213735580444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 525 -4.3544219806790352e-03</internalNodes>
          <leafValues>
            -5.7990497350692749e-01 5.8335512876510620e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 1.5392700443044305e-03</internalNodes>
          <leafValues>
            -1.0273179411888123e-01 4.0286800265312195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 -3.5907807759940624e-03</internalNodes>
          <leafValues>
            -5.7972615957260132e-01 7.4733175337314606e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 742 -2.6264857500791550e-02</internalNodes>
          <leafValues>
            3.9446443319320679e-01 -1.1581628769636154e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 458 1.6059044748544693e-02</internalNodes>
          <leafValues>
            -1.0167770087718964e-01 3.6267307400703430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 -4.1905373334884644e-02</internalNodes>
          <leafValues>
            4.7364938259124756e-01 -8.8032789528369904e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 2.0880695432424545e-02</internalNodes>
          <leafValues>
            -1.2106557935476303e-01 3.8552695512771606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 959 3.2229241915047169e-03</internalNodes>
          <leafValues>
            6.9974288344383240e-02 -6.0391223430633545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 7.0135584101080894e-03</internalNodes>
          <leafValues>
            -1.0977950692176819e-01 3.7435680627822876e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 798 -6.5330024808645248e-03</internalNodes>
          <leafValues>
            -6.9873285293579102e-01 5.8301825076341629e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 -6.3728205859661102e-03</internalNodes>
          <leafValues>
            2.4119727313518524e-01 -1.5554191172122955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 598 -3.9798039942979813e-03</internalNodes>
          <leafValues>
            3.2675772905349731e-01 -1.1990765482187271e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>48</maxWeakCount>
      <stageThreshold>-1.6315091848373413e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 398 -1.6997709870338440e-02</internalNodes>
          <leafValues>
            7.5603079795837402e-01 1.9442643970251083e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 858 -1.5086915343999863e-02</internalNodes>
          <leafValues>
            6.3829183578491211e-01 -1.4418891072273254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 7.5988154858350754e-03</internalNodes>
          <leafValues>
            -1.6574914753437042e-01 4.6998679637908936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 -7.5363442301750183e-03</internalNodes>
          <leafValues>
            4.4424122571945190e-01 -1.8298716843128204e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 441 1.0129272937774658e-02</internalNodes>
          <leafValues>
            -2.0301033556461334e-01 5.5256271362304688e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 732 3.0099015682935715e-02</internalNodes>
          <leafValues>
            -9.0159557759761810e-02 5.2430152893066406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 880 1.0154332034289837e-02</internalNodes>
          <leafValues>
            -2.1865487098693848e-01 3.7318554520606995e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 753 -5.7211541570723057e-03</internalNodes>
          <leafValues>
            2.9541808366775513e-01 -2.3727707564830780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 -2.3081460967659950e-03</internalNodes>
          <leafValues>
            -6.5867960453033447e-01 8.6644835770130157e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 3.4120366908609867e-03</internalNodes>
          <leafValues>
            7.3793835937976837e-02 -6.1988431215286255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 1.1280932230874896e-03</internalNodes>
          <leafValues>
            -1.7844060063362122e-01 2.9092252254486084e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 252 3.4356258809566498e-02</internalNodes>
          <leafValues>
            -1.4515927433967590e-01 3.3726382255554199e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 583 4.2802840471267700e-02</internalNodes>
          <leafValues>
            -1.0719767957925797e-01 4.7673487663269043e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -2.2776997648179531e-03</internalNodes>
          <leafValues>
            3.6087805032730103e-01 -1.2924250960350037e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 6.0573252849280834e-03</internalNodes>
          <leafValues>
            6.6139653325080872e-02 -7.4114394187927246e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 -1.0843809694051743e-02</internalNodes>
          <leafValues>
            4.1086801886558533e-01 -1.3518220186233521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 2.5435941293835640e-02</internalNodes>
          <leafValues>
            -1.2997664511203766e-01 3.8705968856811523e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 1.6918467590585351e-03</internalNodes>
          <leafValues>
            9.5908589661121368e-02 -6.5462106466293335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 690 2.5078756734728813e-03</internalNodes>
          <leafValues>
            5.6727513670921326e-02 -6.1011266708374023e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 -1.0462226346135139e-02</internalNodes>
          <leafValues>
            3.6109340190887451e-01 -1.2214753031730652e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 -1.6778277233242989e-02</internalNodes>
          <leafValues>
            -5.3534448146820068e-01 8.2928635179996490e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 831 -1.5040259808301926e-02</internalNodes>
          <leafValues>
            2.8428143262863159e-01 -1.4685547351837158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 907 -6.6617773845791817e-03</internalNodes>
          <leafValues>
            -5.6624877452850342e-01 7.8970976173877716e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 695 8.1638405099511147e-03</internalNodes>
          <leafValues>
            -1.6379712522029877e-01 2.6822853088378906e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 1.9468305632472038e-02</internalNodes>
          <leafValues>
            -1.2091565877199173e-01 3.3373209834098816e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 -6.4643016085028648e-03</internalNodes>
          <leafValues>
            -6.3222587108612061e-01 6.6180422902107239e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 3.5924967378377914e-02</internalNodes>
          <leafValues>
            -1.0186699032783508e-01 4.2578670382499695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 1.6905765980482101e-02</internalNodes>
          <leafValues>
            -1.3217522203922272e-01 3.3241125941276550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 5.7176817208528519e-03</internalNodes>
          <leafValues>
            6.6569112241268158e-02 -6.5681034326553345e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 -1.0900674387812614e-03</internalNodes>
          <leafValues>
            3.9689606428146362e-01 -1.1235479265451431e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 760 -2.3833939805626869e-02</internalNodes>
          <leafValues>
            3.8570886850357056e-01 -1.0193232446908951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 -1.4802538789808750e-02</internalNodes>
          <leafValues>
            3.4205844998359680e-01 -1.4262656867504120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 3.9707131683826447e-02</internalNodes>
          <leafValues>
            -9.5635637640953064e-02 4.4075250625610352e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 547 6.4531993120908737e-03</internalNodes>
          <leafValues>
            5.7593464851379395e-02 -7.0275545120239258e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 922 -4.7811353579163551e-03</internalNodes>
          <leafValues>
            2.7453303337097168e-01 -1.3652370870113373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 3.3349171280860901e-03</internalNodes>
          <leafValues>
            5.8540347963571548e-02 -7.1738266944885254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 -1.0832921601831913e-02</internalNodes>
          <leafValues>
            -6.2031352519989014e-01 4.7055520117282867e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 696 -3.5385387018322945e-03</internalNodes>
          <leafValues>
            2.7126258611679077e-01 -1.3402579724788666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 785 -2.1408915519714355e-02</internalNodes>
          <leafValues>
            3.6707195639610291e-01 -1.0640451312065125e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 617 6.6339373588562012e-02</internalNodes>
          <leafValues>
            -1.0504902899265289e-01 3.3936560153961182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 2.2766939364373684e-03</internalNodes>
          <leafValues>
            7.2598882019519806e-02 -5.3970605134963989e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 626 1.6875732690095901e-03</internalNodes>
          <leafValues>
            8.7735749781131744e-02 -4.0284165740013123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 623 8.4530832245945930e-03</internalNodes>
          <leafValues>
            -9.3997113406658173e-02 4.1698867082595825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 5.5649573914706707e-03</internalNodes>
          <leafValues>
            -8.7597280740737915e-02 3.8827970623970032e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 -3.5470342263579369e-03</internalNodes>
          <leafValues>
            3.3585703372955322e-01 -1.3658957183361053e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 954 4.2132395319640636e-03</internalNodes>
          <leafValues>
            7.2930902242660522e-02 -5.1745194196701050e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 3.4532562131062150e-04</internalNodes>
          <leafValues>
            -1.7970138788223267e-01 2.1011430025100708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 -4.0376763790845871e-03</internalNodes>
          <leafValues>
            2.7334249019622803e-01 -1.3640886545181274e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>57</maxWeakCount>
      <stageThreshold>-1.5859905481338501e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 376 -1.5983805060386658e-02</internalNodes>
          <leafValues>
            7.4085760116577148e-01 5.4202862083911896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 654 1.4518834650516510e-02</internalNodes>
          <leafValues>
            -1.6582691669464111e-01 5.1249128580093384e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 820 -1.0167414322495461e-02</internalNodes>
          <leafValues>
            4.5000806450843811e-01 -1.3064502179622650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 493 2.7079641819000244e-02</internalNodes>
          <leafValues>
            -1.6735902428627014e-01 6.3848841190338135e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 774 1.9515088060870767e-03</internalNodes>
          <leafValues>
            -1.1798944324254990e-01 3.9749121665954590e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 1.3693260028958321e-02</internalNodes>
          <leafValues>
            -8.5312500596046448e-02 5.4718613624572754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 6.0192337259650230e-03</internalNodes>
          <leafValues>
            5.7217631489038467e-02 -6.6589832305908203e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 -5.6810788810253143e-03</internalNodes>
          <leafValues>
            -5.7089996337890625e-01 8.1808418035507202e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 4.8240914475172758e-04</internalNodes>
          <leafValues>
            -2.2115968167781830e-01 2.1608626842498779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 699 -6.6948928870260715e-03</internalNodes>
          <leafValues>
            -6.5513664484024048e-01 7.3252275586128235e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 607 -1.4181779697537422e-02</internalNodes>
          <leafValues>
            3.2152280211448669e-01 -1.5524932742118835e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 1.6027893871068954e-02</internalNodes>
          <leafValues>
            -1.3886103034019470e-01 3.2296729087829590e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 3.8102023303508759e-02</internalNodes>
          <leafValues>
            -8.4586337208747864e-02 5.0823771953582764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 627 -6.8796845152974129e-03</internalNodes>
          <leafValues>
            2.8458747267723083e-01 -1.6626659035682678e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 -5.7869823649525642e-03</internalNodes>
          <leafValues>
            -6.7561829090118408e-01 6.7125916481018066e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -1.0403458960354328e-02</internalNodes>
          <leafValues>
            -6.8244606256484985e-01 5.5120140314102173e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 7.2765655815601349e-02</internalNodes>
          <leafValues>
            -1.2289700657129288e-01 3.4603855013847351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 -5.2832886576652527e-02</internalNodes>
          <leafValues>
            3.6294373869895935e-01 -1.1915811896324158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -2.0185699686408043e-03</internalNodes>
          <leafValues>
            3.0245268344879150e-01 -1.5617357194423676e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 -2.8552478179335594e-03</internalNodes>
          <leafValues>
            -6.5382599830627441e-01 6.9692179560661316e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 -3.6076260730624199e-03</internalNodes>
          <leafValues>
            -5.5573904514312744e-01 6.0684457421302795e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 -1.2505692429840565e-03</internalNodes>
          <leafValues>
            2.2487366199493408e-01 -1.7713856697082520e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 1.6213182359933853e-02</internalNodes>
          <leafValues>
            6.7865289747714996e-02 -5.5539685487747192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 -2.7728214859962463e-02</internalNodes>
          <leafValues>
            -5.8333241939544678e-01 5.7263575494289398e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 -1.2375607620924711e-03</internalNodes>
          <leafValues>
            2.9786622524261475e-01 -1.2998357415199280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 2.4766498245298862e-03</internalNodes>
          <leafValues>
            8.8138826191425323e-02 -4.3225872516632080e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 1.0034437291324139e-03</internalNodes>
          <leafValues>
            -1.1115902662277222e-01 3.2714295387268066e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 -6.4956350252032280e-03</internalNodes>
          <leafValues>
            -7.0529288053512573e-01 6.1914756894111633e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 -1.2748142704367638e-02</internalNodes>
          <leafValues>
            3.6699298024177551e-01 -1.0546508431434631e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 494 -5.6289080530405045e-03</internalNodes>
          <leafValues>
            -8.3690512180328369e-01 4.8901058733463287e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 846 -3.0219005420804024e-03</internalNodes>
          <leafValues>
            2.2210697829723358e-01 -1.6297030448913574e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 938 2.9230308718979359e-03</internalNodes>
          <leafValues>
            8.0248229205608368e-02 -4.7586214542388916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 620 -1.6901228576898575e-02</internalNodes>
          <leafValues>
            3.2134863734245300e-01 -1.1946766823530197e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 -1.6434368444606662e-03</internalNodes>
          <leafValues>
            3.5056352615356445e-01 -1.0065372288227081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 6.0744080692529678e-03</internalNodes>
          <leafValues>
            7.7158488333225250e-02 -5.0687175989151001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 -6.2521770596504211e-02</internalNodes>
          <leafValues>
            3.5145899653434753e-01 -1.0493072122335434e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 1.4936760999262333e-03</internalNodes>
          <leafValues>
            -1.1596123874187469e-01 3.1784045696258545e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 -4.6554272994399071e-03</internalNodes>
          <leafValues>
            -5.8499008417129517e-01 6.5426386892795563e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 5.1631154492497444e-03</internalNodes>
          <leafValues>
            4.6380143612623215e-02 -6.5920770168304443e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 431 5.7699503377079964e-03</internalNodes>
          <leafValues>
            -1.0032630711793900e-01 3.4062737226486206e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 -9.6255233511328697e-03</internalNodes>
          <leafValues>
            2.2360336780548096e-01 -1.5362788736820221e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 -2.8061982244253159e-02</internalNodes>
          <leafValues>
            2.5125834345817566e-01 -1.2988410890102386e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 807 7.1229478344321251e-03</internalNodes>
          <leafValues>
            5.3426012396812439e-02 -6.4893049001693726e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 1.4473337680101395e-02</internalNodes>
          <leafValues>
            -1.2796792387962341e-01 2.5788536667823792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 -4.4487215578556061e-02</internalNodes>
          <leafValues>
            4.8267531394958496e-01 -7.5288593769073486e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 3.3553102985024452e-03</internalNodes>
          <leafValues>
            6.5222866833209991e-02 -5.3387296199798584e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 2.6522833853960037e-02</internalNodes>
          <leafValues>
            4.7097213566303253e-02 -5.7117742300033569e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 5.3791850805282593e-03</internalNodes>
          <leafValues>
            4.9663346260786057e-02 -5.0957924127578735e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 684 -8.8640749454498291e-03</internalNodes>
          <leafValues>
            3.3878505229949951e-01 -8.6965307593345642e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 2.7605522423982620e-02</internalNodes>
          <leafValues>
            4.4678669422864914e-02 -6.9978964328765869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 944 -1.1171739548444748e-02</internalNodes>
          <leafValues>
            -7.3840415477752686e-01 3.0841451138257980e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 -1.0616163490340114e-03</internalNodes>
          <leafValues>
            3.0718466639518738e-01 -9.7260892391204834e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 -7.2728879749774933e-03</internalNodes>
          <leafValues>
            -7.1966600418090820e-01 4.3096855282783508e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 924 -1.1083125136792660e-02</internalNodes>
          <leafValues>
            3.8436344265937805e-01 -8.1930950284004211e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 -7.1662524715065956e-03</internalNodes>
          <leafValues>
            2.0970225334167480e-01 -1.5484949946403503e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 8.2661323249340057e-03</internalNodes>
          <leafValues>
            5.9242360293865204e-02 -5.1503551006317139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 748 7.3844827711582184e-03</internalNodes>
          <leafValues>
            3.3728431910276413e-02 -7.2390365600585938e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>61</maxWeakCount>
      <stageThreshold>-1.5647197961807251e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 443 -7.5000515207648277e-03</internalNodes>
          <leafValues>
            6.9735616445541382e-01 -7.0126228965818882e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -2.5447460357099771e-03</internalNodes>
          <leafValues>
            5.4742383956909180e-01 -1.3766847550868988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 6.3486215658485889e-03</internalNodes>
          <leafValues>
            -1.5121677517890930e-01 6.0269719362258911e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 860 2.5572092272341251e-03</internalNodes>
          <leafValues>
            -2.1577885746955872e-01 3.9897701144218445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 510 -6.5959235653281212e-03</internalNodes>
          <leafValues>
            3.2995587587356567e-01 -2.0266638696193695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 857 1.5824008733034134e-02</internalNodes>
          <leafValues>
            -1.4384938776493073e-01 5.4570239782333374e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 3.4904260188341141e-02</internalNodes>
          <leafValues>
            -1.0439507663249969e-01 5.3645384311676025e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 7.4804951436817646e-03</internalNodes>
          <leafValues>
            -1.7777608335018158e-01 3.0247840285301208e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -5.1247365772724152e-02</internalNodes>
          <leafValues>
            5.7459318637847900e-01 -1.0999230295419693e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 629 -5.0416901707649231e-02</internalNodes>
          <leafValues>
            4.6202743053436279e-01 -8.9995197951793671e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 1.2860384769737720e-02</internalNodes>
          <leafValues>
            -1.5580976009368896e-01 2.6711037755012512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 2.3457493633031845e-02</internalNodes>
          <leafValues>
            5.4399158805608749e-02 -7.5605469942092896e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 -3.4703868441283703e-03</internalNodes>
          <leafValues>
            2.3663245141506195e-01 -1.6793093085289001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 4.2368983849883080e-03</internalNodes>
          <leafValues>
            6.2613829970359802e-02 -6.2294322252273560e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 727 -8.7803313508629799e-03</internalNodes>
          <leafValues>
            3.0670607089996338e-01 -1.2937802076339722e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 7.3221437633037567e-03</internalNodes>
          <leafValues>
            8.7675094604492188e-02 -4.8196199536323547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 5.7109566405415535e-03</internalNodes>
          <leafValues>
            -1.6586679220199585e-01 2.4693855643272400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 -1.0330275399610400e-03</internalNodes>
          <leafValues>
            2.8573888540267944e-01 -1.2924034893512726e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 -4.1551878675818443e-03</internalNodes>
          <leafValues>
            -6.7751622200012207e-01 5.5871110409498215e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 -8.7040066719055176e-03</internalNodes>
          <leafValues>
            2.6861536502838135e-01 -1.4459304511547089e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 3.9226360619068146e-02</internalNodes>
          <leafValues>
            -1.0390799492597580e-01 3.5152482986450195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 -4.7440445050597191e-03</internalNodes>
          <leafValues>
            2.5985953211784363e-01 -1.4647118747234344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 -3.9255903102457523e-03</internalNodes>
          <leafValues>
            -5.1697838306427002e-01 7.4196860194206238e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 -1.0538822039961815e-02</internalNodes>
          <leafValues>
            3.7007546424865723e-01 -1.1099486052989960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 -6.4126476645469666e-03</internalNodes>
          <leafValues>
            -6.8768596649169922e-01 6.5695002675056458e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 614 -6.6444426774978638e-03</internalNodes>
          <leafValues>
            2.3941572010517120e-01 -1.5043427050113678e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 784 3.6154527217149734e-02</internalNodes>
          <leafValues>
            -9.1301433742046356e-02 4.3764653801918030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 2.0614354871213436e-03</internalNodes>
          <leafValues>
            -1.0028914362192154e-01 3.4935840964317322e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 -2.6316416915506124e-03</internalNodes>
          <leafValues>
            -4.7376596927642822e-01 7.9684391617774963e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 -6.9046420976519585e-03</internalNodes>
          <leafValues>
            -5.4207211732864380e-01 5.3584035485982895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 792 -7.9165250062942505e-03</internalNodes>
          <leafValues>
            2.3244017362594604e-01 -1.4517860114574432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 6.6963117569684982e-03</internalNodes>
          <leafValues>
            5.6810487061738968e-02 -6.3260114192962646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 -1.8640372902154922e-02</internalNodes>
          <leafValues>
            2.3912836611270905e-01 -1.4180511236190796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 -1.2744618579745293e-03</internalNodes>
          <leafValues>
            3.5026183724403381e-01 -9.4078496098518372e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 -6.6458717919886112e-03</internalNodes>
          <leafValues>
            -6.4737498760223389e-01 5.4980348795652390e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 5.5033955723047256e-03</internalNodes>
          <leafValues>
            3.6262378096580505e-02 -7.0103096961975098e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 -6.0990457423031330e-03</internalNodes>
          <leafValues>
            3.1506294012069702e-01 -1.0298713296651840e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 955 -9.5215532928705215e-04</internalNodes>
          <leafValues>
            -3.3243876695632935e-01 9.4536833465099335e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 -4.8709083348512650e-03</internalNodes>
          <leafValues>
            -5.5286788940429688e-01 4.8858009278774261e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 -1.9530812278389931e-03</internalNodes>
          <leafValues>
            2.6475632190704346e-01 -1.2214422971010208e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 852 -1.6108162701129913e-02</internalNodes>
          <leafValues>
            2.4747616052627563e-01 -1.4181286096572876e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 -3.3051617443561554e-02</internalNodes>
          <leafValues>
            4.7526669502258301e-01 -6.3493676483631134e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 -2.0969051867723465e-02</internalNodes>
          <leafValues>
            3.7475255131721497e-01 -8.7978623807430267e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 862 -1.1976938694715500e-03</internalNodes>
          <leafValues>
            2.8161275386810303e-01 -1.0515356063842773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 3.0867555178701878e-03</internalNodes>
          <leafValues>
            6.1260223388671875e-02 -5.0152593851089478e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 5.4881181567907333e-03</internalNodes>
          <leafValues>
            5.0317917019128799e-02 -5.4196691513061523e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 -3.6943652667105198e-03</internalNodes>
          <leafValues>
            -5.8759558200836182e-01 4.4150535017251968e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 2.3760091513395309e-02</internalNodes>
          <leafValues>
            -1.0199809074401855e-01 3.0310767889022827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 -2.1728422492742538e-02</internalNodes>
          <leafValues>
            3.1364366412162781e-01 -1.0069291293621063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 -2.5134570896625519e-02</internalNodes>
          <leafValues>
            -5.1455682516098022e-01 5.5909216403961182e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 2.5713320821523666e-02</internalNodes>
          <leafValues>
            -1.2262356281280518e-01 2.5486063957214355e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 3.4806665498763323e-03</internalNodes>
          <leafValues>
            4.3244410306215286e-02 -7.0197206735610962e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 -3.8689947687089443e-03</internalNodes>
          <leafValues>
            -5.8558273315429688e-01 4.0547560900449753e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 -1.0773935355246067e-03</internalNodes>
          <leafValues>
            3.0767098069190979e-01 -9.5467783510684967e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 2.9714959673583508e-03</internalNodes>
          <leafValues>
            -7.1951679885387421e-02 3.9655500650405884e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 -8.8994875550270081e-03</internalNodes>
          <leafValues>
            -6.3042962551116943e-01 4.7020766884088516e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 588 6.7773810587823391e-03</internalNodes>
          <leafValues>
            -7.5779460370540619e-02 3.6968868970870972e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -8.9795496314764023e-03</internalNodes>
          <leafValues>
            2.2023639082908630e-01 -1.3685037195682526e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 -1.4680022373795509e-02</internalNodes>
          <leafValues>
            2.9656830430030823e-01 -9.4061806797981262e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 -9.5530468970537186e-03</internalNodes>
          <leafValues>
            2.9208987951278687e-01 -1.0542043298482895e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 -1.4967216411605477e-03</internalNodes>
          <leafValues>
            2.1045146882534027e-01 -1.3942880928516388e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>67</maxWeakCount>
      <stageThreshold>-1.5504211187362671e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 454 -1.0960219427943230e-02</internalNodes>
          <leafValues>
            6.5447217226028442e-01 5.0713799893856049e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 864 -1.1516783386468887e-02</internalNodes>
          <leafValues>
            4.6027910709381104e-01 -1.5864185988903046e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 3.3400617539882660e-03</internalNodes>
          <leafValues>
            -2.1552324295043945e-01 3.4321418404579163e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 -3.8353595882654190e-03</internalNodes>
          <leafValues>
            3.3496814966201782e-01 -2.3464411497116089e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 -5.9523088857531548e-03</internalNodes>
          <leafValues>
            2.8272038698196411e-01 -2.0106634497642517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 788 -1.3916005846112967e-03</internalNodes>
          <leafValues>
            3.4103384613990784e-01 -1.5115562081336975e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 -2.2318472620099783e-03</internalNodes>
          <leafValues>
            3.1695351004600525e-01 -1.6021527349948883e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 8.3244532346725464e-02</internalNodes>
          <leafValues>
            -8.9079469442367554e-02 5.3136157989501953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 832 4.8591636121273041e-02</internalNodes>
          <leafValues>
            -1.0279218852519989e-01 3.9601847529411316e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 9.5052458345890045e-03</internalNodes>
          <leafValues>
            5.5167526006698608e-02 -7.4528604745864868e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 612 -3.7635704502463341e-03</internalNodes>
          <leafValues>
            -6.5434825420379639e-01 5.7055845856666565e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 1.7548685718793422e-04</internalNodes>
          <leafValues>
            -2.3401582241058350e-01 1.6428647935390472e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 -5.4875545203685760e-02</internalNodes>
          <leafValues>
            2.5605452060699463e-01 -1.5396752953529358e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 -6.1811439692974091e-02</internalNodes>
          <leafValues>
            4.2922756075859070e-01 -8.8362246751785278e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 1.3979763025417924e-03</internalNodes>
          <leafValues>
            -7.3071695864200592e-02 4.5563563704490662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 -5.5876211263239384e-04</internalNodes>
          <leafValues>
            2.9506489634513855e-01 -1.2031728774309158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 -4.3148966506123543e-03</internalNodes>
          <leafValues>
            -5.7403188943862915e-01 6.6134005784988403e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 -5.3551131859421730e-03</internalNodes>
          <leafValues>
            -5.2288484573364258e-01 6.9182172417640686e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 -3.6877401173114777e-02</internalNodes>
          <leafValues>
            3.3143782615661621e-01 -1.2777587771415710e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 4.1612848639488220e-02</internalNodes>
          <leafValues>
            -1.4963860809803009e-01 2.9361200332641602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 6.6619878634810448e-04</internalNodes>
          <leafValues>
            -1.8005952239036560e-01 2.1171462535858154e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 2.2130949422717094e-02</internalNodes>
          <leafValues>
            5.7936761528253555e-02 -5.9410941600799561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 469 1.2182788923382759e-02</internalNodes>
          <leafValues>
            -1.1940150707960129e-01 2.9696035385131836e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 2.8001153841614723e-03</internalNodes>
          <leafValues>
            -1.4503511786460876e-01 2.2681860625743866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 945 2.8729443438351154e-03</internalNodes>
          <leafValues>
            6.5800048410892487e-02 -4.9987852573394775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 -5.0598740577697754e-01</internalNodes>
          <leafValues>
            6.1999630928039551e-01 -5.6771270930767059e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 -9.6983816474676132e-03</internalNodes>
          <leafValues>
            3.2087686657905579e-01 -9.7375035285949707e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 497 3.0797901563346386e-03</internalNodes>
          <leafValues>
            -1.2448154389858246e-01 2.5827226042747498e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 -1.5816848026588559e-03</internalNodes>
          <leafValues>
            4.1313612461090088e-01 -9.0998791158199310e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 -9.5680113881826401e-03</internalNodes>
          <leafValues>
            -7.0231872797012329e-01 5.5185325443744659e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 3.1543439254164696e-03</internalNodes>
          <leafValues>
            4.8739165067672729e-02 -5.4295998811721802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 -7.8436743933707476e-04</internalNodes>
          <leafValues>
            3.0286926031112671e-01 -1.0217373818159103e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 4.3993473052978516e-01</internalNodes>
          <leafValues>
            -4.0746804326772690e-02 7.4451828002929688e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 -3.5277460701763630e-03</internalNodes>
          <leafValues>
            -4.5241990685462952e-01 7.3149621486663818e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 468 8.3879064768552780e-03</internalNodes>
          <leafValues>
            3.3294096589088440e-02 -7.2618806362152100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 9.9020544439554214e-04</internalNodes>
          <leafValues>
            -1.0716802626848221e-01 2.8766462206840515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 -2.9110300820320845e-03</internalNodes>
          <leafValues>
            2.8586754202842712e-01 -1.1559913307428360e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 -5.1425592973828316e-03</internalNodes>
          <leafValues>
            -6.1106848716735840e-01 4.6430643647909164e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 1.7738080024719238e-01</internalNodes>
          <leafValues>
            -7.0887565612792969e-02 4.1917768120765686e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 -1.1850059032440186e-02</internalNodes>
          <leafValues>
            3.1747487187385559e-01 -1.0234380513429642e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 529 -1.0039219632744789e-02</internalNodes>
          <leafValues>
            2.0376846194267273e-01 -1.4407536387443542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 1.9497467204928398e-02</internalNodes>
          <leafValues>
            4.7855406999588013e-02 -5.7639378309249878e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 5.5466167628765106e-02</internalNodes>
          <leafValues>
            -7.4624486267566681e-02 4.1103851795196533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 -3.9324127137660980e-03</internalNodes>
          <leafValues>
            -5.5429047346115112e-01 5.0012629479169846e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 662 -8.9037272846326232e-04</internalNodes>
          <leafValues>
            2.0536813139915466e-01 -1.3225764036178589e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 4.6562731266021729e-02</internalNodes>
          <leafValues>
            -9.1293826699256897e-02 2.9735872149467468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 548 -1.5474244952201843e-01</internalNodes>
          <leafValues>
            -8.6353981494903564e-01 3.4763321280479431e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 2.4359119124710560e-03</internalNodes>
          <leafValues>
            5.9721726924180984e-02 -4.1146609187126160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 1.3576634228229523e-02</internalNodes>
          <leafValues>
            -9.4201639294624329e-02 3.0719256401062012e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 456 -9.8991915583610535e-03</internalNodes>
          <leafValues>
            2.6257899403572083e-01 -9.5683693885803223e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 479 -3.0812243930995464e-03</internalNodes>
          <leafValues>
            2.1774919331073761e-01 -1.1509665846824646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 -1.9958070479333401e-03</internalNodes>
          <leafValues>
            3.5399836301803589e-01 -7.2066798806190491e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 919 3.0025159940123558e-03</internalNodes>
          <leafValues>
            4.2854189872741699e-02 -6.0343819856643677e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 836 -1.1377423070371151e-03</internalNodes>
          <leafValues>
            2.0119668543338776e-01 -1.2889184057712555e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 3.7626659031957388e-03</internalNodes>
          <leafValues>
            4.6901285648345947e-02 -5.7279956340789795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 724 -3.9297584444284439e-03</internalNodes>
          <leafValues>
            3.5875543951988220e-01 -7.3908790946006775e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 885 -2.5286607444286346e-02</internalNodes>
          <leafValues>
            3.5751584172248840e-01 -6.7418299615383148e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 749 -3.9539579302072525e-03</internalNodes>
          <leafValues>
            2.0609807968139648e-01 -1.4521332085132599e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 911 -5.7711899280548096e-03</internalNodes>
          <leafValues>
            -5.2898341417312622e-01 4.7179169952869415e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 701 -3.3264106605201960e-03</internalNodes>
          <leafValues>
            2.2409056127071381e-01 -1.1467082053422928e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 703 6.1332453042268753e-02</internalNodes>
          <leafValues>
            3.7662509828805923e-02 -7.1230965852737427e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 -1.4834193512797356e-02</internalNodes>
          <leafValues>
            -5.5149120092391968e-01 3.9850924164056778e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 -1.0773484408855438e-01</internalNodes>
          <leafValues>
            -4.9263399839401245e-01 4.9221854656934738e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 -6.5140398219227791e-03</internalNodes>
          <leafValues>
            -6.7663580179214478e-01 3.3736269921064377e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 -2.4242542684078217e-02</internalNodes>
          <leafValues>
            -6.9796782732009888e-01 2.7110328897833824e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 -1.7814380116760731e-03</internalNodes>
          <leafValues>
            2.9062062501907349e-01 -8.0135107040405273e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 801 4.7076190821826458e-03</internalNodes>
          <leafValues>
            3.7101462483406067e-02 -6.7659527063369751e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>80</maxWeakCount>
      <stageThreshold>-1.5639265775680542e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 561 -4.3974462896585464e-03</internalNodes>
          <leafValues>
            7.1587848663330078e-01 8.4948554635047913e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 1.8174322322010994e-03</internalNodes>
          <leafValues>
            -1.7691791057586670e-01 4.3212845921516418e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 -5.5131856352090836e-03</internalNodes>
          <leafValues>
            4.5490756630897522e-01 -1.4189453423023224e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 1.1955455876886845e-02</internalNodes>
          <leafValues>
            -2.0918996632099152e-01 4.3714949488639832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 -3.4246121067553759e-03</internalNodes>
          <leafValues>
            3.5878163576126099e-01 -1.5395726263523102e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 -4.3844994157552719e-02</internalNodes>
          <leafValues>
            4.3700152635574341e-01 -1.2767519056797028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 839 -6.3555962406098843e-03</internalNodes>
          <leafValues>
            2.9515129327774048e-01 -1.6441816091537476e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 534 -2.6965860743075609e-03</internalNodes>
          <leafValues>
            1.9414065778255463e-01 -2.0237676799297333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 460 -1.3908351771533489e-02</internalNodes>
          <leafValues>
            2.5903883576393127e-01 -1.5587335824966431e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 -1.4053133316338062e-02</internalNodes>
          <leafValues>
            5.4957073926925659e-01 -8.1244736909866333e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 918 -5.3004794754087925e-03</internalNodes>
          <leafValues>
            -5.8528614044189453e-01 7.3730856180191040e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 -3.1702318228781223e-03</internalNodes>
          <leafValues>
            1.9391658902168274e-01 -1.8859483301639557e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 -4.5197797007858753e-03</internalNodes>
          <leafValues>
            -5.4193162918090820e-01 5.9990171343088150e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 -5.4979130625724792e-02</internalNodes>
          <leafValues>
            4.7812277078628540e-01 -6.7818723618984222e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 8.1017805496230721e-04</internalNodes>
          <leafValues>
            -9.5595575869083405e-02 3.1569144129753113e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 841 -2.8012446127831936e-03</internalNodes>
          <leafValues>
            -4.9929830431938171e-01 6.8443782627582550e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 833 -6.0123754665255547e-03</internalNodes>
          <leafValues>
            3.8719713687896729e-01 -8.9717194437980652e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 3.6350139416754246e-03</internalNodes>
          <leafValues>
            6.6413506865501404e-02 -4.8185935616493225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 -1.3544321991503239e-03</internalNodes>
          <leafValues>
            2.5372084975242615e-01 -1.2267075479030609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 827 -3.1020103488117456e-03</internalNodes>
          <leafValues>
            2.7389132976531982e-01 -1.1379010230302811e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 767 1.5349574387073517e-03</internalNodes>
          <leafValues>
            -1.0861707478761673e-01 2.8958576917648315e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 6.4284317195415497e-03</internalNodes>
          <leafValues>
            5.9735804796218872e-02 -5.6773471832275391e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 -8.9295972138643265e-03</internalNodes>
          <leafValues>
            -6.7040807008743286e-01 3.8283705711364746e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 764 -1.7896143253892660e-03</internalNodes>
          <leafValues>
            2.1716582775115967e-01 -1.4856858551502228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 523 6.2072295695543289e-03</internalNodes>
          <leafValues>
            -8.8489770889282227e-02 3.3571973443031311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 526 1.9034199649468064e-03</internalNodes>
          <leafValues>
            6.7696519196033478e-02 -4.5386880636215210e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 524 -2.1953256800770760e-03</internalNodes>
          <leafValues>
            -4.2716285586357117e-01 6.5683454275131226e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 5.3394897840917110e-03</internalNodes>
          <leafValues>
            -1.0160661488771439e-01 2.9586192965507507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 2.0328685641288757e-03</internalNodes>
          <leafValues>
            -9.5586851239204407e-02 3.0505907535552979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 4.3488927185535431e-03</internalNodes>
          <leafValues>
            7.9764112830162048e-02 -3.8651520013809204e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 518 -4.0209172293543816e-03</internalNodes>
          <leafValues>
            3.0502754449844360e-01 -8.9127995073795319e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 -4.8844739794731140e-02</internalNodes>
          <leafValues>
            4.2813199758529663e-01 -6.7647248506546021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 360 6.9915860891342163e-02</internalNodes>
          <leafValues>
            -8.3985306322574615e-02 3.2602414488792419e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 535 4.9919363111257553e-02</internalNodes>
          <leafValues>
            -7.2998367249965668e-02 4.6169292926788330e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 3.0068659689277411e-03</internalNodes>
          <leafValues>
            5.5069219321012497e-02 -6.1965858936309814e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 1.7419853247702122e-03</internalNodes>
          <leafValues>
            -8.2020215690135956e-02 3.6006987094879150e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 -4.1090780869126320e-03</internalNodes>
          <leafValues>
            -5.2174150943756104e-01 5.6436747312545776e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 665 -4.5902859419584274e-03</internalNodes>
          <leafValues>
            2.2742575407028198e-01 -1.2061754614114761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 -2.8690489009022713e-02</internalNodes>
          <leafValues>
            -3.5808193683624268e-01 7.5659923255443573e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 4.1000463068485260e-02</internalNodes>
          <leafValues>
            -6.2531292438507080e-02 4.5491513609886169e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 -4.2531453073024750e-03</internalNodes>
          <leafValues>
            -6.1825770139694214e-01 4.9510892480611801e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 -1.4550488442182541e-02</internalNodes>
          <leafValues>
            -7.7191162109375000e-01 2.5961024686694145e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 -7.4881664477288723e-04</internalNodes>
          <leafValues>
            2.8547286987304688e-01 -8.8079601526260376e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 337 1.3214498758316040e-03</internalNodes>
          <leafValues>
            -7.6633729040622711e-02 3.7057441473007202e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 5.3907292895019054e-03</internalNodes>
          <leafValues>
            4.9379035830497742e-02 -5.7583230733871460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 838 -2.4113813415169716e-03</internalNodes>
          <leafValues>
            2.0829583704471588e-01 -1.3430447876453400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 1.1464932933449745e-02</internalNodes>
          <leafValues>
            4.9434442073106766e-02 -6.5971946716308594e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 486 -1.3421529904007912e-02</internalNodes>
          <leafValues>
            -7.4524301290512085e-01 2.5121277198195457e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 2.8626890853047371e-03</internalNodes>
          <leafValues>
            6.1505425721406937e-02 -3.5306212306022644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 1.1442219838500023e-02</internalNodes>
          <leafValues>
            5.3547207266092300e-02 -4.0409806370735168e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 538 5.7390062138438225e-03</internalNodes>
          <leafValues>
            -9.7106143832206726e-02 2.4726873636245728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 485 -1.3369014486670494e-02</internalNodes>
          <leafValues>
            3.4151887893676758e-01 -9.5434606075286865e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 611 3.3499381970614195e-03</internalNodes>
          <leafValues>
            4.9335762858390808e-02 -4.9601793289184570e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 1.2374855577945709e-02</internalNodes>
          <leafValues>
            -5.7205036282539368e-02 5.0581747293472290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 500 2.7577474713325500e-02</internalNodes>
          <leafValues>
            -8.7777808308601379e-02 2.9972809553146362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 2.1137124300003052e-01</internalNodes>
          <leafValues>
            -6.2023047357797623e-02 4.0350961685180664e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 -3.5103857517242432e-02</internalNodes>
          <leafValues>
            2.9696103930473328e-01 -8.2776337862014771e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 -2.8949903789907694e-03</internalNodes>
          <leafValues>
            -3.7250569462776184e-01 6.6616289317607880e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 3.6181407049298286e-03</internalNodes>
          <leafValues>
            -9.3407385051250458e-02 2.5402054190635681e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 445 1.1848636902868748e-02</internalNodes>
          <leafValues>
            -8.2135058939456940e-02 2.9939675331115723e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 -1.0207802988588810e-02</internalNodes>
          <leafValues>
            2.1719035506248474e-01 -1.1622364073991776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 3.0942540615797043e-02</internalNodes>
          <leafValues>
            4.5177377760410309e-02 -5.0523322820663452e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 -3.1778869032859802e-01</internalNodes>
          <leafValues>
            -8.8358139991760254e-01 2.1195204928517342e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 513 -1.0037058964371681e-02</internalNodes>
          <leafValues>
            -5.8986192941665649e-01 3.0863022431731224e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 9.1691948473453522e-03</internalNodes>
          <leafValues>
            -6.4322948455810547e-02 3.7386918067932129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 658 1.3658402487635612e-02</internalNodes>
          <leafValues>
            -1.0158041864633560e-01 2.6782375574111938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 2.7183804195374250e-03</internalNodes>
          <leafValues>
            5.5635135620832443e-02 -3.9148023724555969e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 1.3893332798033953e-03</internalNodes>
          <leafValues>
            -1.2399668246507645e-01 1.7072468996047974e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 -1.8156928941607475e-02</internalNodes>
          <leafValues>
            -6.3868224620819092e-01 3.4263785928487778e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 6.4471308141946793e-03</internalNodes>
          <leafValues>
            1.8927905708551407e-02 -8.5299736261367798e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 1.4844031073153019e-02</internalNodes>
          <leafValues>
            -1.2482391297817230e-01 1.7663741111755371e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -1.9531816244125366e-02</internalNodes>
          <leafValues>
            3.1519362330436707e-01 -8.2499116659164429e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 2.2133110091090202e-02</internalNodes>
          <leafValues>
            -9.0632885694503784e-02 2.2813312709331512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 -5.8851181529462337e-03</internalNodes>
          <leafValues>
            2.0633347332477570e-01 -1.1403661221265793e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 1.4685674104839563e-03</internalNodes>
          <leafValues>
            -6.8528242409229279e-02 3.6765024065971375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 856 -5.6167589500546455e-03</internalNodes>
          <leafValues>
            2.1468248963356018e-01 -1.1975194513797760e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 530 -1.0915055871009827e-01</internalNodes>
          <leafValues>
            4.9222618341445923e-01 -4.6790093183517456e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 638 -4.3774135410785675e-03</internalNodes>
          <leafValues>
            2.6555654406547546e-01 -8.1355758011341095e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 -5.5184490047395229e-03</internalNodes>
          <leafValues>
            -4.7854590415954590e-01 4.9387764185667038e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 -1.8072805833071470e-03</internalNodes>
          <leafValues>
            -6.4401823282241821e-01 3.1085403636097908e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>91</maxWeakCount>
      <stageThreshold>-1.5610778331756592e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 821 -1.1308195069432259e-02</internalNodes>
          <leafValues>
            6.4171379804611206e-01 6.3050843775272369e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 454 -1.6007030382752419e-02</internalNodes>
          <leafValues>
            4.6389564871788025e-01 -1.2460056692361832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 1.5086517669260502e-03</internalNodes>
          <leafValues>
            -1.4270767569541931e-01 3.3293032646179199e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 -5.5297352373600006e-02</internalNodes>
          <leafValues>
            4.7708284854888916e-01 -1.1913372576236725e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 1.3316229917109013e-02</internalNodes>
          <leafValues>
            -1.6993317008018494e-01 3.8821667432785034e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 8.1390906125307083e-03</internalNodes>
          <leafValues>
            -1.5329377353191376e-01 2.8196957707405090e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 -1.5794128412380815e-03</internalNodes>
          <leafValues>
            -4.3692907691001892e-01 1.1585860699415207e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 -7.4164522811770439e-03</internalNodes>
          <leafValues>
            -7.0102095603942871e-01 3.1883224844932556e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 2.9237023554742336e-03</internalNodes>
          <leafValues>
            -1.2032959610223770e-01 3.1826072931289673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 8.3931483328342438e-02</internalNodes>
          <leafValues>
            -7.1062639355659485e-02 5.1401311159133911e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 879 5.1326253451406956e-03</internalNodes>
          <leafValues>
            -1.5315851569175720e-01 2.3686404526233673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 1.2808490544557571e-02</internalNodes>
          <leafValues>
            -1.2817305326461792e-01 2.8131189942359924e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 5.5591724812984467e-02</internalNodes>
          <leafValues>
            -9.7707554697990417e-02 3.7602767348289490e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 -2.4632480926811695e-03</internalNodes>
          <leafValues>
            -5.5093276500701904e-01 6.5490268170833588e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 -3.1798938289284706e-03</internalNodes>
          <leafValues>
            -5.6288594007492065e-01 5.3227964788675308e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 -1.4345918316394091e-03</internalNodes>
          <leafValues>
            -5.1232701539993286e-01 5.7506360113620758e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 861 1.9692238420248032e-03</internalNodes>
          <leafValues>
            -1.6736923158168793e-01 1.8856795132160187e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 -5.5519137531518936e-03</internalNodes>
          <leafValues>
            -5.2823477983474731e-01 5.2204202860593796e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 3.1600738875567913e-03</internalNodes>
          <leafValues>
            -8.0669216811656952e-02 3.6808264255523682e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 7.3207332752645016e-03</internalNodes>
          <leafValues>
            5.4134551435709000e-02 -5.3350186347961426e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 3.5804554354399443e-03</internalNodes>
          <leafValues>
            4.3425790965557098e-02 -5.3225243091583252e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 -3.4314931835979223e-03</internalNodes>
          <leafValues>
            3.3590000867843628e-01 -7.8590616583824158e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 958 6.8407682701945305e-03</internalNodes>
          <leafValues>
            4.5175880193710327e-02 -6.1210322380065918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 957 -4.1379006579518318e-03</internalNodes>
          <leafValues>
            -5.3888756036758423e-01 4.4202055782079697e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 1.3155659660696983e-02</internalNodes>
          <leafValues>
            -7.9103693366050720e-02 3.3573821187019348e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 3.1921269837766886e-03</internalNodes>
          <leafValues>
            5.7063572108745575e-02 -5.0549602508544922e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 906 2.3588772863149643e-02</internalNodes>
          <leafValues>
            -8.3102487027645111e-02 3.0777907371520996e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 -6.1599123291671276e-03</internalNodes>
          <leafValues>
            1.6074487566947937e-01 -1.5700389444828033e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 6.4594233408570290e-03</internalNodes>
          <leafValues>
            -9.5241472125053406e-02 3.0516397953033447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 -2.0677673164755106e-03</internalNodes>
          <leafValues>
            -5.0822901725769043e-01 5.9834387153387070e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 2.0666117779910564e-03</internalNodes>
          <leafValues>
            -7.2401538491249084e-02 3.5851547122001648e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 5.3266487084329128e-03</internalNodes>
          <leafValues>
            5.4928623139858246e-02 -4.8396179080009460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 -7.7358852140605450e-03</internalNodes>
          <leafValues>
            -4.8261037468910217e-01 4.4207476079463959e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 909 7.9007837921380997e-03</internalNodes>
          <leafValues>
            -9.4954080879688263e-02 2.7517431974411011e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 470 4.0566008538007736e-03</internalNodes>
          <leafValues>
            4.3646700680255890e-02 -5.8921122550964355e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 3.5946490243077278e-03</internalNodes>
          <leafValues>
            -1.3110473752021790e-01 1.8304315209388733e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 -2.6677086949348450e-02</internalNodes>
          <leafValues>
            2.8980365395545959e-01 -8.3346247673034668e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 5.4062507115304470e-03</internalNodes>
          <leafValues>
            -9.1766953468322754e-02 2.9673796892166138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 -5.1793372258543968e-03</internalNodes>
          <leafValues>
            -6.1112493276596069e-01 4.3332517147064209e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -9.1262701898813248e-03</internalNodes>
          <leafValues>
            2.1878185868263245e-01 -1.1105874925851822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 9.5943324267864227e-03</internalNodes>
          <leafValues>
            -8.7977558374404907e-02 3.2010060548782349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 668 -5.0256419926881790e-03</internalNodes>
          <leafValues>
            2.1040959656238556e-01 -1.0872460156679153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 681 3.0062482692301273e-03</internalNodes>
          <leafValues>
            -1.0699967294931412e-01 2.9316556453704834e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 1.1852329596877098e-02</internalNodes>
          <leafValues>
            3.9550069719552994e-02 -6.0533910989761353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 533 -5.2753865718841553e-02</internalNodes>
          <leafValues>
            2.6370123028755188e-01 -9.2691496014595032e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 3.8847257383167744e-03</internalNodes>
          <leafValues>
            6.4825706183910370e-02 -4.1523045301437378e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 -2.6287192013114691e-03</internalNodes>
          <leafValues>
            -5.0846499204635620e-01 4.2991567403078079e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 2.2053448483347893e-03</internalNodes>
          <leafValues>
            -1.0581049323081970e-01 2.3079065978527069e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 552 -3.7466879002749920e-03</internalNodes>
          <leafValues>
            -5.2957397699356079e-01 4.6158149838447571e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 501 -2.9029445722699165e-03</internalNodes>
          <leafValues>
            -4.1290035843849182e-01 5.1479596644639969e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 3.7801317870616913e-02</internalNodes>
          <leafValues>
            -1.0680335760116577e-01 2.2418104112148285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 -8.4752835333347321e-02</internalNodes>
          <leafValues>
            -7.8421443700790405e-01 3.0642487108707428e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 619 9.6596507355570793e-03</internalNodes>
          <leafValues>
            -9.7389675676822662e-02 2.4497544765472412e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 7.2564175352454185e-03</internalNodes>
          <leafValues>
            -9.8195895552635193e-02 2.9686492681503296e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 -1.0029030963778496e-02</internalNodes>
          <leafValues>
            -5.6505876779556274e-01 4.1911888867616653e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 540 -9.7039304673671722e-03</internalNodes>
          <leafValues>
            2.1148304641246796e-01 -1.0376640409231186e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 425 -1.8890092615038157e-03</internalNodes>
          <leafValues>
            2.2384525835514069e-01 -1.0597650706768036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 587 -1.4225458726286888e-03</internalNodes>
          <leafValues>
            2.1189780533313751e-01 -1.1053096503019333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 4.3249968439340591e-03</internalNodes>
          <leafValues>
            4.1859358549118042e-02 -5.3136503696441650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 418 -2.5746987666934729e-03</internalNodes>
          <leafValues>
            -3.3480682969093323e-01 6.2653385102748871e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 822 1.9772505387663841e-02</internalNodes>
          <leafValues>
            -5.8535441756248474e-02 3.9204162359237671e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 -9.6579845994710922e-03</internalNodes>
          <leafValues>
            2.2001895308494568e-01 -9.4623439013957977e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 1.5255061443895102e-03</internalNodes>
          <leafValues>
            -8.3463013172149658e-02 3.1410121917724609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 6.2276879325509071e-03</internalNodes>
          <leafValues>
            5.6847181171178818e-02 -4.1030114889144897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 9.0132874902337790e-04</internalNodes>
          <leafValues>
            -8.4593303501605988e-02 2.7151137590408325e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 953 -4.6218577772378922e-03</internalNodes>
          <leafValues>
            -4.0008178353309631e-01 5.6437231600284576e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 730 -1.5077156014740467e-02</internalNodes>
          <leafValues>
            2.3747061192989349e-01 -9.1518931090831757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 2.1273698657751083e-02</internalNodes>
          <leafValues>
            5.3466927260160446e-02 -4.5392176508903503e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 -6.0164434835314751e-03</internalNodes>
          <leafValues>
            2.3596890270709991e-01 -1.2803076207637787e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 8.9327711611986160e-03</internalNodes>
          <leafValues>
            4.2524505406618118e-02 -5.8664286136627197e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 7.3118656873703003e-03</internalNodes>
          <leafValues>
            -6.9641888141632080e-02 3.3589112758636475e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 2.6424862444400787e-03</internalNodes>
          <leafValues>
            3.4661941230297089e-02 -6.7860239744186401e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 5.9287068434059620e-03</internalNodes>
          <leafValues>
            2.7298627421259880e-02 -6.5472942590713501e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 877 -7.8424429520964622e-03</internalNodes>
          <leafValues>
            -6.3548064231872559e-01 2.7554484084248543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 541 -2.1585542708635330e-03</internalNodes>
          <leafValues>
            2.2929325699806213e-01 -9.0029284358024597e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 1.5804420690983534e-03</internalNodes>
          <leafValues>
            -9.7765833139419556e-02 2.2442239522933960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 -1.2139983475208282e-02</internalNodes>
          <leafValues>
            2.9934337735176086e-01 -7.2730682790279388e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 2.2737295366823673e-03</internalNodes>
          <leafValues>
            4.6053361147642136e-02 -4.7994795441627502e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 2.0574156660586596e-03</internalNodes>
          <leafValues>
            -4.9065478146076202e-02 4.6873793005943298e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 5.9092035517096519e-03</internalNodes>
          <leafValues>
            3.3293012529611588e-02 -6.2421238422393799e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 -2.0584808662533760e-02</internalNodes>
          <leafValues>
            2.6486057043075562e-01 -7.6441936194896698e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 1.1120462790131569e-02</internalNodes>
          <leafValues>
            4.4590156525373459e-02 -5.2196294069290161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 -3.4440308809280396e-03</internalNodes>
          <leafValues>
            1.5679638087749481e-01 -1.3589784502983093e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 963 -1.0556755587458611e-02</internalNodes>
          <leafValues>
            3.0609151721000671e-01 -7.2761178016662598e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 8.1238988786935806e-03</internalNodes>
          <leafValues>
            4.4047191739082336e-02 -4.8989585041999817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -1.1493992060422897e-02</internalNodes>
          <leafValues>
            2.0072945952415466e-01 -1.1034463346004486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 586 2.3690698668360710e-02</internalNodes>
          <leafValues>
            -1.2550449371337891e-01 1.8665367364883423e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 1.5682559460401535e-02</internalNodes>
          <leafValues>
            -7.4671298265457153e-02 2.8130453824996948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 -3.3444758504629135e-02</internalNodes>
          <leafValues>
            2.6843747496604919e-01 -8.3811916410923004e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 610 3.0884463340044022e-02</internalNodes>
          <leafValues>
            -9.9225074052810669e-02 2.2484876215457916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 -4.0955815464258194e-02</internalNodes>
          <leafValues>
            1.8551258742809296e-01 -1.1869347840547562e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>90</maxWeakCount>
      <stageThreshold>-1.4453492164611816e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 735 -4.5189578086137772e-03</internalNodes>
          <leafValues>
            6.7139738798141479e-01 9.2261902987957001e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 4.2574461549520493e-03</internalNodes>
          <leafValues>
            -1.1547925323247910e-01 4.7692731022834778e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -3.0073656234890223e-03</internalNodes>
          <leafValues>
            3.6661648750305176e-01 -1.3400055468082428e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 2.0850619673728943e-01</internalNodes>
          <leafValues>
            -1.7360156774520874e-01 2.8776788711547852e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 -3.3409267663955688e-02</internalNodes>
          <leafValues>
            4.2965263128280640e-01 -1.1280254274606705e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 444 8.2403160631656647e-03</internalNodes>
          <leafValues>
            -1.3494767248630524e-01 3.0936670303344727e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 -5.4142652079463005e-03</internalNodes>
          <leafValues>
            -5.0563532114028931e-01 6.3294559717178345e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 691 -1.3661640696227551e-02</internalNodes>
          <leafValues>
            2.6760646700859070e-01 -1.3282431662082672e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 -6.6441677510738373e-02</internalNodes>
          <leafValues>
            4.2027309536933899e-01 -9.1117665171623230e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 -3.6823814734816551e-03</internalNodes>
          <leafValues>
            -6.0496860742568970e-01 6.3766337931156158e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 3.5007880069315434e-03</internalNodes>
          <leafValues>
            5.9523750096559525e-02 -5.4523044824600220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 1.5307647408917546e-03</internalNodes>
          <leafValues>
            -1.1713726073503494e-01 3.1415259838104248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 769 -1.7609039321541786e-02</internalNodes>
          <leafValues>
            3.9622062444686890e-01 -8.1705585122108459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 2.3612366989254951e-02</internalNodes>
          <leafValues>
            -1.1964736133813858e-01 2.8404179215431213e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 -1.2014270760118961e-02</internalNodes>
          <leafValues>
            2.7746838331222534e-01 -1.1446747928857803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 -4.6456828713417053e-03</internalNodes>
          <leafValues>
            -5.3870040178298950e-01 5.2098110318183899e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 447 5.3105363622307777e-03</internalNodes>
          <leafValues>
            -1.0284136235713959e-01 3.0061340332031250e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 -3.2729478552937508e-03</internalNodes>
          <leafValues>
            1.9203263521194458e-01 -1.6125205159187317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 9.8467990756034851e-03</internalNodes>
          <leafValues>
            4.8938397318124771e-02 -5.1129150390625000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 3.2083394471555948e-03</internalNodes>
          <leafValues>
            -8.5019417107105255e-02 3.4343490004539490e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 5.5270073935389519e-03</internalNodes>
          <leafValues>
            6.3495978713035583e-02 -4.8666983842849731e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 -2.3475135676562786e-03</internalNodes>
          <leafValues>
            2.5843459367752075e-01 -1.1678623408079147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 689 -1.5939555596560240e-03</internalNodes>
          <leafValues>
            -3.2783389091491699e-01 8.0364800989627838e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 -4.1191074997186661e-03</internalNodes>
          <leafValues>
            -5.5736887454986572e-01 4.6545837074518204e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 1.7747837118804455e-03</internalNodes>
          <leafValues>
            -7.7934704720973969e-02 3.3011713624000549e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 -1.6894178697839379e-03</internalNodes>
          <leafValues>
            2.2780518233776093e-01 -1.1316975951194763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 -2.0341284107416868e-03</internalNodes>
          <leafValues>
            -3.8829386234283447e-01 6.9249257445335388e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 655 -3.4458152949810028e-03</internalNodes>
          <leafValues>
            -4.0543556213378906e-01 5.8193698525428772e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 -9.3588102608919144e-03</internalNodes>
          <leafValues>
            3.1281456351280212e-01 -7.8269012272357941e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 -4.9023423343896866e-03</internalNodes>
          <leafValues>
            -4.0507251024246216e-01 6.6911309957504272e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -4.8415181040763855e-01</internalNodes>
          <leafValues>
            6.5363335609436035e-01 -4.0620740503072739e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 2.6781920343637466e-02</internalNodes>
          <leafValues>
            -1.0990447551012039e-01 2.1767459809780121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 -9.4280913472175598e-03</internalNodes>
          <leafValues>
            -7.4746483564376831e-01 2.9869174584746361e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 492 -7.7869845554232597e-03</internalNodes>
          <leafValues>
            3.0222293734550476e-01 -8.3480700850486755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 3.5958206281065941e-03</internalNodes>
          <leafValues>
            -8.2547821104526520e-02 2.9035624861717224e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 5.8124819770455360e-03</internalNodes>
          <leafValues>
            -9.7843483090400696e-02 2.6563084125518799e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 -6.3764736987650394e-03</internalNodes>
          <leafValues>
            -4.6169018745422363e-01 5.5747114121913910e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 -7.2099521756172180e-02</internalNodes>
          <leafValues>
            -7.3345041275024414e-01 2.8517069295048714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 -8.2340046763420105e-02</internalNodes>
          <leafValues>
            -6.5312498807907104e-01 2.9036073014140129e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 -1.5001616440713406e-02</internalNodes>
          <leafValues>
            -6.3826096057891846e-01 3.2474573701620102e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 1.1907238513231277e-03</internalNodes>
          <leafValues>
            -7.9208493232727051e-02 2.9137271642684937e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 594 3.1184246763586998e-03</internalNodes>
          <leafValues>
            4.7656070441007614e-02 -4.7487255930900574e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 603 8.2192681729793549e-03</internalNodes>
          <leafValues>
            2.6732290163636208e-02 -7.3682332038879395e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 7.1536734700202942e-02</internalNodes>
          <leafValues>
            -6.6174156963825226e-02 3.4596624970436096e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 707 -1.3247081078588963e-02</internalNodes>
          <leafValues>
            2.2122915089130402e-01 -1.1525890231132507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 6.4605651423335075e-03</internalNodes>
          <leafValues>
            5.1374625414609909e-02 -4.2834889888763428e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 -1.9957395270466805e-03</internalNodes>
          <leafValues>
            2.8387853503227234e-01 -8.6039707064628601e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 2.5912611745297909e-03</internalNodes>
          <leafValues>
            6.0468357056379318e-02 -3.9721179008483887e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 716 1.9276916980743408e-02</internalNodes>
          <leafValues>
            -8.2993559539318085e-02 3.0764940381050110e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 -4.9096969887614250e-03</internalNodes>
          <leafValues>
            2.7268949151039124e-01 -8.6130671203136444e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 3.6836266517639160e-03</internalNodes>
          <leafValues>
            5.9468954801559448e-02 -3.9446946978569031e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 520 3.0758073553442955e-03</internalNodes>
          <leafValues>
            -9.7390249371528625e-02 2.4346484243869781e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 522 -4.2319851927459240e-03</internalNodes>
          <leafValues>
            3.0930569767951965e-01 -7.9394333064556122e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 7.2837145999073982e-03</internalNodes>
          <leafValues>
            4.7933470457792282e-02 -4.8675662279129028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -6.2543689273297787e-03</internalNodes>
          <leafValues>
            -4.8621338605880737e-01 4.2082890868186951e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 -1.1530111078172922e-03</internalNodes>
          <leafValues>
            2.6954403519630432e-01 -8.6280718445777893e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 560 4.0323145687580109e-02</internalNodes>
          <leafValues>
            -9.4530344009399414e-02 2.3481069505214691e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -1.2282184325158596e-02</internalNodes>
          <leafValues>
            -7.2538161277770996e-01 3.0701907351613045e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 678 3.7427488714456558e-02</internalNodes>
          <leafValues>
            2.9119925573468208e-02 -6.4663606882095337e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 2.5721127167344093e-03</internalNodes>
          <leafValues>
            4.9338530749082565e-02 -4.0851938724517822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 -2.0106829702854156e-02</internalNodes>
          <leafValues>
            -6.2753307819366455e-01 3.1198438256978989e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 2.5536534376442432e-03</internalNodes>
          <leafValues>
            2.3092683404684067e-02 -7.4033683538436890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 -1.3628168962895870e-03</internalNodes>
          <leafValues>
            3.4928113222122192e-01 -6.5548241138458252e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 -9.9153746850788593e-04</internalNodes>
          <leafValues>
            2.2522389888763428e-01 -9.1688700020313263e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 965 5.0148647278547287e-03</internalNodes>
          <leafValues>
            3.9175543934106827e-02 -5.5856782197952271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 853 9.1358053032308817e-04</internalNodes>
          <leafValues>
            -1.2273798882961273e-01 1.8954706192016602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 762 1.6373313963413239e-02</internalNodes>
          <leafValues>
            3.9829690009355545e-02 -5.7986593246459961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 1.4575564302504063e-02</internalNodes>
          <leafValues>
            -8.6225226521492004e-02 2.4198558926582336e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 967 5.1754433661699295e-04</internalNodes>
          <leafValues>
            8.9498788118362427e-02 -2.1777628362178802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 -1.1098009534180164e-02</internalNodes>
          <leafValues>
            2.2925806045532227e-01 -8.3799630403518677e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 2.4133864790201187e-02</internalNodes>
          <leafValues>
            -1.0102383792400360e-01 2.0877565443515778e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 -3.7758771330118179e-02</internalNodes>
          <leafValues>
            4.3367731571197510e-01 -5.1683723926544189e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 9.0820249170064926e-03</internalNodes>
          <leafValues>
            4.0468864142894745e-02 -5.4790335893630981e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 -3.8959060329943895e-03</internalNodes>
          <leafValues>
            -3.4300115704536438e-01 5.6340463459491730e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 -1.0297749191522598e-02</internalNodes>
          <leafValues>
            2.9084947705268860e-01 -8.1125274300575256e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 8.3358399569988251e-03</internalNodes>
          <leafValues>
            -6.9515161216259003e-02 3.0880457162857056e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 805 4.1338298469781876e-02</internalNodes>
          <leafValues>
            3.2240319997072220e-02 -6.5160977840423584e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 2.6844158768653870e-02</internalNodes>
          <leafValues>
            -6.5987348556518555e-02 3.1071534752845764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 -4.4573536142706871e-03</internalNodes>
          <leafValues>
            -3.4222671389579773e-01 6.0962244868278503e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 -5.6259175762534142e-03</internalNodes>
          <leafValues>
            1.9679838418960571e-01 -9.9301390349864960e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 -3.4068014472723007e-02</internalNodes>
          <leafValues>
            -5.7343089580535889e-01 3.5370521247386932e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 -4.1321285068988800e-02</internalNodes>
          <leafValues>
            -5.4799556732177734e-01 3.2511439174413681e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 -4.5842211693525314e-03</internalNodes>
          <leafValues>
            2.0696444809436798e-01 -9.3100592494010925e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 705 -8.6525538936257362e-03</internalNodes>
          <leafValues>
            -5.2304923534393311e-01 4.0334302932024002e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 -8.1807989627122879e-03</internalNodes>
          <leafValues>
            3.0393254756927490e-01 -6.9615311920642853e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 867 -6.2429648824036121e-03</internalNodes>
          <leafValues>
            -5.0806474685668945e-01 4.2720243334770203e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 660 -4.3397732079029083e-03</internalNodes>
          <leafValues>
            -4.7173827886581421e-01 3.7593103945255280e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 -2.4694669991731644e-03</internalNodes>
          <leafValues>
            3.4972354769706726e-01 -6.2289424240589142e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 4.6105296351015568e-03</internalNodes>
          <leafValues>
            4.8353113234043121e-02 -3.9337757229804993e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 1.5546558424830437e-03</internalNodes>
          <leafValues>
            -8.5152842104434967e-02 2.4539804458618164e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.6321692466735840e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 443 -9.9800406023859978e-03</internalNodes>
          <leafValues>
            6.5110075473785400e-01 7.0068746805191040e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 1.2785504572093487e-02</internalNodes>
          <leafValues>
            -1.5662825107574463e-01 4.5551964640617371e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 4.0613190503790975e-04</internalNodes>
          <leafValues>
            -2.3326659202575684e-01 2.4264821410179138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 -4.4671623036265373e-03</internalNodes>
          <leafValues>
            3.0027064681053162e-01 -1.7738959193229675e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 733 7.1196053177118301e-03</internalNodes>
          <leafValues>
            -1.3141728937625885e-01 4.3263924121856689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 855 7.1185962297022343e-03</internalNodes>
          <leafValues>
            -1.5669579803943634e-01 3.3418005704879761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 725 4.5672003179788589e-03</internalNodes>
          <leafValues>
            -1.2860487401485443e-01 3.1136614084243774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 859 -1.0435921140015125e-03</internalNodes>
          <leafValues>
            1.9418887794017792e-01 -1.7872068285942078e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 -8.6360834538936615e-03</internalNodes>
          <leafValues>
            -6.0729598999023438e-01 3.6422688513994217e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 685 6.4469715580344200e-03</internalNodes>
          <leafValues>
            -1.7270094156265259e-01 1.8412014842033386e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 920 -3.2128435559570789e-03</internalNodes>
          <leafValues>
            -5.6947451829910278e-01 5.5858459323644638e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 2.9547505080699921e-02</internalNodes>
          <leafValues>
            5.4511282593011856e-02 -4.9024525284767151e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 -5.3524523973464966e-03</internalNodes>
          <leafValues>
            -4.3886002898216248e-01 5.9159737080335617e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 2.2656358778476715e-03</internalNodes>
          <leafValues>
            -1.4958912134170532e-01 1.9237321615219116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 -1.7663020640611649e-02</internalNodes>
          <leafValues>
            3.4963962435722351e-01 -9.8792962729930878e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 -1.4769199490547180e-01</internalNodes>
          <leafValues>
            3.8789209723472595e-01 -7.4754111468791962e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 961 -1.2649353593587875e-02</internalNodes>
          <leafValues>
            3.6447465419769287e-01 -7.8627258539199829e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 -1.6667589545249939e-01</internalNodes>
          <leafValues>
            3.1024694442749023e-01 -9.8567992448806763e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 823 1.5327525325119495e-03</internalNodes>
          <leafValues>
            -8.7889634072780609e-02 3.3739477396011353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 -4.9399482086300850e-03</internalNodes>
          <leafValues>
            -6.0582613945007324e-01 4.7072298824787140e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 663 1.6168793663382530e-03</internalNodes>
          <leafValues>
            -1.1102212965488434e-01 2.4625547230243683e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -7.9764677211642265e-03</internalNodes>
          <leafValues>
            -4.5467814803123474e-01 5.6168641895055771e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 803 2.1164701320230961e-03</internalNodes>
          <leafValues>
            3.9522409439086914e-02 -5.8244407176971436e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 1.2181616621091962e-03</internalNodes>
          <leafValues>
            -1.1960548907518387e-01 1.8955740332603455e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 6.2020774930715561e-02</internalNodes>
          <leafValues>
            -9.2262148857116699e-02 2.4536235630512238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 1.5368127264082432e-02</internalNodes>
          <leafValues>
            -7.4950158596038818e-02 3.8813439011573792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 631 -3.6116694100201130e-03</internalNodes>
          <leafValues>
            -5.8402395248413086e-01 4.3880671262741089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 -5.4048337042331696e-03</internalNodes>
          <leafValues>
            2.7466323971748352e-01 -8.3315111696720123e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 -5.7541755959391594e-03</internalNodes>
          <leafValues>
            -4.8696601390838623e-01 5.0719954073429108e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 868 -8.8466441957280040e-04</internalNodes>
          <leafValues>
            1.4997816085815430e-01 -1.4873522520065308e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 419 -1.3690529391169548e-02</internalNodes>
          <leafValues>
            -4.2396122217178345e-01 5.1716264337301254e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 -9.2098396271467209e-03</internalNodes>
          <leafValues>
            -6.8742758035659790e-01 2.8357446193695068e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 -1.0102453641593456e-02</internalNodes>
          <leafValues>
            3.0423650145530701e-01 -7.5886160135269165e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 -2.1283417008817196e-03</internalNodes>
          <leafValues>
            2.2551217675209045e-01 -9.5488928258419037e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 675 2.2938135080039501e-03</internalNodes>
          <leafValues>
            3.2833088189363480e-02 -6.4737302064895630e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -2.0679826848208904e-03</internalNodes>
          <leafValues>
            2.9072764515876770e-01 -8.1707596778869629e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 495 1.8802012782543898e-03</internalNodes>
          <leafValues>
            -1.0236340761184692e-01 2.4278828501701355e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 566 -1.5465463511645794e-03</internalNodes>
          <leafValues>
            -3.8903787732124329e-01 5.6320030242204666e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 630 6.3281953334808350e-03</internalNodes>
          <leafValues>
            -9.9905796349048615e-02 2.2087195515632629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 704 3.3235952258110046e-02</internalNodes>
          <leafValues>
            5.0302099436521530e-02 -4.9443060159683228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 -1.9428483210504055e-03</internalNodes>
          <leafValues>
            -6.7564088106155396e-01 2.7948424220085144e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 1.1729343095794320e-03</internalNodes>
          <leafValues>
            -1.1950153112411499e-01 1.8506029248237610e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 5.4220901802182198e-03</internalNodes>
          <leafValues>
            5.0924405455589294e-02 -4.3448522686958313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 3.0700212810188532e-03</internalNodes>
          <leafValues>
            -7.6845921576023102e-02 2.6929470896720886e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 2.6065693236887455e-03</internalNodes>
          <leafValues>
            5.5169116705656052e-02 -3.9985677599906921e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 2.6848190464079380e-03</internalNodes>
          <leafValues>
            2.7681041508913040e-02 -6.6643798351287842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 471 4.9525322392582893e-03</internalNodes>
          <leafValues>
            -7.8715771436691284e-02 2.6918828487396240e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 466 -1.2217788025736809e-02</internalNodes>
          <leafValues>
            2.5042393803596497e-01 -9.2959709465503693e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 -9.7617935389280319e-03</internalNodes>
          <leafValues>
            -5.8083361387252808e-01 4.1861489415168762e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 -3.0802208930253983e-03</internalNodes>
          <leafValues>
            -5.4920911788940430e-01 3.1410869210958481e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 499 8.8869817554950714e-03</internalNodes>
          <leafValues>
            -5.7799737900495529e-02 3.5997068881988525e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 383 -1.6894126310944557e-03</internalNodes>
          <leafValues>
            1.5611077845096588e-01 -1.3526220619678497e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 1.1576576158404350e-02</internalNodes>
          <leafValues>
            2.6843478903174400e-02 -7.7421426773071289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 -1.0315187275409698e-02</internalNodes>
          <leafValues>
            -3.7911209464073181e-01 4.7785960137844086e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 9.3458819901570678e-04</internalNodes>
          <leafValues>
            -1.0482961684465408e-01 1.9531208276748657e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 -2.1154026035219431e-03</internalNodes>
          <leafValues>
            3.2437980175018311e-01 -7.0380724966526031e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 7.0915305987000465e-03</internalNodes>
          <leafValues>
            3.1504381448030472e-02 -7.1498668193817139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 968 1.5262098750099540e-03</internalNodes>
          <leafValues>
            4.3178513646125793e-02 -4.1175857186317444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 -2.6456830091774464e-03</internalNodes>
          <leafValues>
            -6.6830241680145264e-01 2.7078842744231224e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 722 1.3623384293168783e-03</internalNodes>
          <leafValues>
            -9.6260324120521545e-02 1.9846718013286591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 -3.4019351005554199e-03</internalNodes>
          <leafValues>
            1.3638894259929657e-01 -1.4331445097923279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 -4.2538799345493317e-02</internalNodes>
          <leafValues>
            -6.8543094396591187e-01 2.7219040319323540e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 572 5.1494771614670753e-03</internalNodes>
          <leafValues>
            -8.3506844937801361e-02 2.4553795158863068e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 509 -1.3525998219847679e-03</internalNodes>
          <leafValues>
            2.0083853602409363e-01 -1.1441762000322342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 889 -4.7460300847887993e-03</internalNodes>
          <leafValues>
            -5.8234161138534546e-01 3.3193428069353104e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 -1.8419034779071808e-02</internalNodes>
          <leafValues>
            2.5098413228988647e-01 -7.8263558447360992e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -3.6029946058988571e-03</internalNodes>
          <leafValues>
            2.5004243850708008e-01 -7.8106440603733063e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 1.0855928063392639e-02</internalNodes>
          <leafValues>
            3.8721837103366852e-02 -5.0439488887786865e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 3.1823229044675827e-02</internalNodes>
          <leafValues>
            2.8006808832287788e-02 -6.5438795089721680e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 9.0156495571136475e-02</internalNodes>
          <leafValues>
            3.6854032427072525e-02 -4.6750095486640930e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 8.1275019329041243e-04</internalNodes>
          <leafValues>
            -8.1435337662696838e-02 2.3081797361373901e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 843 -2.2238264791667461e-03</internalNodes>
          <leafValues>
            -4.6506562829017639e-01 4.2775552719831467e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 8.5265887901186943e-03</internalNodes>
          <leafValues>
            -9.9068634212017059e-02 1.8661868572235107e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 5.0247795879840851e-03</internalNodes>
          <leafValues>
            3.5677276551723480e-02 -5.6390231847763062e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 1.2137264013290405e-02</internalNodes>
          <leafValues>
            2.3600205779075623e-02 -6.9947057962417603e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 687 5.8652815641835332e-04</internalNodes>
          <leafValues>
            -1.0546504706144333e-01 1.8301849067211151e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 783 -1.2803040444850922e-02</internalNodes>
          <leafValues>
            2.4169570207595825e-01 -9.3701913952827454e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 1.9234570208936930e-03</internalNodes>
          <leafValues>
            4.1152808815240860e-02 -5.3527724742889404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 -5.1119372248649597e-02</internalNodes>
          <leafValues>
            2.0418803393840790e-01 -1.0016205906867981e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 2.0427668467164040e-02</internalNodes>
          <leafValues>
            3.8303721696138382e-02 -5.6752574443817139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 916 1.0702429572120309e-03</internalNodes>
          <leafValues>
            -1.1941519379615784e-01 1.5449772775173187e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 6.3908234238624573e-02</internalNodes>
          <leafValues>
            -8.0574154853820801e-02 2.4723786115646362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 -8.9891534298658371e-03</internalNodes>
          <leafValues>
            2.7232396602630615e-01 -7.9600028693675995e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 -7.4440538883209229e-03</internalNodes>
          <leafValues>
            1.7151834070682526e-01 -1.1733634769916534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 9.3747628852725029e-03</internalNodes>
          <leafValues>
            -5.8857422322034836e-02 3.3993646502494812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 3.9659179747104645e-03</internalNodes>
          <leafValues>
            3.7981141358613968e-02 -5.3772449493408203e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 698 -2.7701430954039097e-03</internalNodes>
          <leafValues>
            2.1686923503875732e-01 -9.1246932744979858e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 592 -3.0083605088293552e-03</internalNodes>
          <leafValues>
            -5.1033967733383179e-01 3.8351774215698242e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 5.2285091951489449e-03</internalNodes>
          <leafValues>
            6.6746503114700317e-02 -2.5510028004646301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 2.6635453104972839e-03</internalNodes>
          <leafValues>
            -8.8651068508625031e-02 2.1260604262351990e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 -3.4417591989040375e-02</internalNodes>
          <leafValues>
            -6.6850775480270386e-01 3.1463332474231720e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 550 -9.7544072195887566e-04</internalNodes>
          <leafValues>
            2.0566202700138092e-01 -1.0702812671661377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 1.6097709536552429e-01</internalNodes>
          <leafValues>
            -4.7826759517192841e-02 3.8993999361991882e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 1.0004348587244749e-03</internalNodes>
          <leafValues>
            -1.0796900838613510e-01 2.1362201869487762e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 420 4.0136128664016724e-03</internalNodes>
          <leafValues>
            -8.1376187503337860e-02 2.2793699800968170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 562 3.2076346687972546e-03</internalNodes>
          <leafValues>
            -1.0031759738922119e-01 2.2700363397598267e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 -4.1395910084247589e-03</internalNodes>
          <leafValues>
            -4.5727050304412842e-01 4.4953804463148117e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 595 4.9559203907847404e-03</internalNodes>
          <leafValues>
            3.0349666252732277e-02 -5.6468343734741211e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 1.9516122993081808e-03</internalNodes>
          <leafValues>
            -8.4078930318355560e-02 2.1666139364242554e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 2.8487551957368851e-02</internalNodes>
          <leafValues>
            -5.8517321944236755e-02 3.7208831310272217e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.5313543081283569e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 865 -1.1964191682636738e-02</internalNodes>
          <leafValues>
            6.5611332654953003e-01 8.7084025144577026e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 570 -3.6326241679489613e-03</internalNodes>
          <leafValues>
            3.0753159523010254e-01 -2.0207116007804871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 739 1.6091932775452733e-03</internalNodes>
          <leafValues>
            -2.1601480245590210e-01 2.4574394524097443e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 -1.0807140171527863e-01</internalNodes>
          <leafValues>
            5.8551537990570068e-01 -8.0984398722648621e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 6.6386028192937374e-03</internalNodes>
          <leafValues>
            -2.3309321701526642e-01 2.5712442398071289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 6.6436373163014650e-04</internalNodes>
          <leafValues>
            -1.7002807557582855e-01 2.2093741595745087e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 5.9623841661959887e-04</internalNodes>
          <leafValues>
            -2.1071858704090118e-01 1.5259474515914917e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 -2.1322746761143208e-03</internalNodes>
          <leafValues>
            -3.6114820837974548e-01 7.9649142920970917e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 -6.9360136985778809e-03</internalNodes>
          <leafValues>
            -5.2729552984237671e-01 5.9225857257843018e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 7.2746481746435165e-03</internalNodes>
          <leafValues>
            -1.5284915268421173e-01 2.0500071346759796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 793 -2.4507325142621994e-03</internalNodes>
          <leafValues>
            -4.4374018907546997e-01 6.2125567346811295e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 -1.1941835284233093e-01</internalNodes>
          <leafValues>
            5.9646230936050415e-01 -5.0393357872962952e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 2.4319710209965706e-03</internalNodes>
          <leafValues>
            -1.4628271758556366e-01 2.2020979225635529e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 4.0825735777616501e-03</internalNodes>
          <leafValues>
            4.6519946306943893e-02 -6.0437613725662231e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 3.3597379922866821e-02</internalNodes>
          <leafValues>
            5.5770415812730789e-02 -4.4832473993301392e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -4.1159454733133316e-02</internalNodes>
          <leafValues>
            -4.2802116274833679e-01 5.6050233542919159e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 -3.1343686860054731e-03</internalNodes>
          <leafValues>
            -4.5189481973648071e-01 4.5713383704423904e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 -3.9362995885312557e-03</internalNodes>
          <leafValues>
            2.6333171129226685e-01 -8.6672604084014893e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 -3.4228354692459106e-02</internalNodes>
          <leafValues>
            2.8555384278297424e-01 -8.0961830914020538e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 2.3194378241896629e-02</internalNodes>
          <leafValues>
            -1.0957508534193039e-01 2.6531192660331726e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 1.1790241114795208e-03</internalNodes>
          <leafValues>
            -1.2578412890434265e-01 2.2350181639194489e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 757 2.4525973945856094e-02</internalNodes>
          <leafValues>
            3.9447281509637833e-02 -6.4369696378707886e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 -5.5821083486080170e-02</internalNodes>
          <leafValues>
            3.8404938578605652e-01 -6.1516307294368744e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 8.5053090006113052e-03</internalNodes>
          <leafValues>
            -1.1682828515768051e-01 2.0639540255069733e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -4.6913616359233856e-02</internalNodes>
          <leafValues>
            -3.3303919434547424e-01 6.8057745695114136e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 -6.0622256249189377e-02</internalNodes>
          <leafValues>
            3.0634361505508423e-01 -8.0411903560161591e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 962 3.3126091584563255e-03</internalNodes>
          <leafValues>
            6.4039744436740875e-02 -3.6264923214912415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 923 -6.2155202031135559e-03</internalNodes>
          <leafValues>
            2.6324889063835144e-01 -8.5208639502525330e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 840 -3.6174536217004061e-03</internalNodes>
          <leafValues>
            -6.3895624876022339e-01 3.7891831248998642e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 -3.0694848392158747e-03</internalNodes>
          <leafValues>
            2.7301403880119324e-01 -8.3168596029281616e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 -1.6663860296830535e-03</internalNodes>
          <leafValues>
            -4.9059715867042542e-01 4.4817935675382614e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 1.6716115176677704e-02</internalNodes>
          <leafValues>
            6.0621056705713272e-02 -3.4729966521263123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 1.3494723476469517e-02</internalNodes>
          <leafValues>
            3.1516350805759430e-02 -6.2451899051666260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 1.9665439613163471e-03</internalNodes>
          <leafValues>
            -8.6126960813999176e-02 2.5302976369857788e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 -2.8690965846180916e-02</internalNodes>
          <leafValues>
            2.9214075207710266e-01 -6.8187572062015533e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 430 5.4161301814019680e-03</internalNodes>
          <leafValues>
            -8.5594080388545990e-02 2.6200750470161438e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 -3.6381594836711884e-02</internalNodes>
          <leafValues>
            -5.9561169147491455e-01 3.6925114691257477e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 421 -6.7269792780280113e-03</internalNodes>
          <leafValues>
            -7.9720497131347656e-01 2.4374464526772499e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 -7.4932668358087540e-03</internalNodes>
          <leafValues>
            -8.1190264225006104e-01 1.9826157018542290e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 -5.5202767252922058e-03</internalNodes>
          <leafValues>
            1.8447315692901611e-01 -1.1608960479497910e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 869 2.9039490036666393e-03</internalNodes>
          <leafValues>
            6.3957199454307556e-02 -3.0787152051925659e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 2.4206712841987610e-01</internalNodes>
          <leafValues>
            -3.4878797829151154e-02 5.9678316116333008e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 5.8509008958935738e-03</internalNodes>
          <leafValues>
            -8.4465004503726959e-02 2.3755706846714020e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 943 1.1404031887650490e-02</internalNodes>
          <leafValues>
            -6.2884598970413208e-02 3.3538460731506348e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 -6.4798449166119099e-03</internalNodes>
          <leafValues>
            2.6907229423522949e-01 -8.0378860235214233e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 -1.5257325768470764e-01</internalNodes>
          <leafValues>
            3.7274152040481567e-01 -5.3593669086694717e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 -7.8410096466541290e-03</internalNodes>
          <leafValues>
            3.5559067130088806e-01 -6.0485389083623886e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 895 4.6420615399256349e-04</internalNodes>
          <leafValues>
            -1.3953977823257446e-01 1.4700867235660553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 -2.7928948402404785e-03</internalNodes>
          <leafValues>
            -3.7794330716133118e-01 5.3649291396141052e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 761 -6.2361196614801884e-03</internalNodes>
          <leafValues>
            2.3622865974903107e-01 -8.5419490933418274e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 758 -1.0482727549970150e-02</internalNodes>
          <leafValues>
            -4.9808895587921143e-01 4.5149747282266617e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 728 5.1559107378125191e-03</internalNodes>
          <leafValues>
            -8.4864191710948944e-02 2.6940858364105225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 854 1.3875829055905342e-02</internalNodes>
          <leafValues>
            -6.8634092807769775e-02 3.0264788866043091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 549 2.0931879989802837e-03</internalNodes>
          <leafValues>
            -8.1713855266571045e-02 2.2649072110652924e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 580 2.2430280223488808e-03</internalNodes>
          <leafValues>
            3.2463703304529190e-02 -6.4346092939376831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 9.5147592946887016e-03</internalNodes>
          <leafValues>
            2.9569771140813828e-02 -5.5541282892227173e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 -1.5026458539068699e-02</internalNodes>
          <leafValues>
            2.9199507832527161e-01 -6.7493163049221039e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 576 -3.0254235025495291e-03</internalNodes>
          <leafValues>
            -3.6120423674583435e-01 5.3320098668336868e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 543 -3.7556474562734365e-03</internalNodes>
          <leafValues>
            2.4651855230331421e-01 -7.6555579900741577e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 5.2875939756631851e-02</internalNodes>
          <leafValues>
            -6.2019556760787964e-02 3.0396047234535217e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 915 -6.4307113643735647e-04</internalNodes>
          <leafValues>
            1.4872814714908600e-01 -1.2868101894855499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 -2.7651647105813026e-02</internalNodes>
          <leafValues>
            -4.7678905725479126e-01 4.2087376117706299e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 673 -2.5210313033312559e-03</internalNodes>
          <leafValues>
            -6.7401230335235596e-01 2.5271434336900711e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 -1.7504607094451785e-03</internalNodes>
          <leafValues>
            -6.6526460647583008e-01 2.3153429850935936e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 -1.2022716924548149e-02</internalNodes>
          <leafValues>
            -6.5252929925918579e-01 2.4157531559467316e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 -1.6721414402127266e-02</internalNodes>
          <leafValues>
            2.0145316421985626e-01 -8.8073857128620148e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 664 -6.1156335286796093e-03</internalNodes>
          <leafValues>
            2.3662807047367096e-01 -8.2179009914398193e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 726 4.1717891581356525e-03</internalNodes>
          <leafValues>
            3.0463650822639465e-02 -6.4868044853210449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 1.0461646597832441e-03</internalNodes>
          <leafValues>
            -1.0331799834966660e-01 1.7261520028114319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 -7.5713603291660547e-04</internalNodes>
          <leafValues>
            2.2451940178871155e-01 -9.4167023897171021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 -5.1019098609685898e-03</internalNodes>
          <leafValues>
            1.7748890817165375e-01 -1.0525784641504288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 951 5.8564399369060993e-03</internalNodes>
          <leafValues>
            2.7441246435046196e-02 -6.8668657541275024e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 3.8241008296608925e-03</internalNodes>
          <leafValues>
            -6.8950459361076355e-02 2.7575340867042542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 1.1478068307042122e-02</internalNodes>
          <leafValues>
            3.9409350603818893e-02 -5.4299759864807129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 -1.4753835275769234e-03</internalNodes>
          <leafValues>
            -4.5533245801925659e-01 3.6614906042814255e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 9.4340764917433262e-04</internalNodes>
          <leafValues>
            6.0544602572917938e-02 -2.9981470108032227e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 -3.6590839736163616e-03</internalNodes>
          <leafValues>
            2.3720666766166687e-01 -8.0204457044601440e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -3.1423069536685944e-02</internalNodes>
          <leafValues>
            -7.1167147159576416e-01 2.8132835403084755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 3.5741357132792473e-03</internalNodes>
          <leafValues>
            1.7519874498248100e-02 -8.3257293701171875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 585 -6.8605719134211540e-03</internalNodes>
          <leafValues>
            -6.9204151630401611e-01 2.0872814580798149e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 902 -8.8996449485421181e-03</internalNodes>
          <leafValues>
            1.8208101391792297e-01 -9.8315775394439697e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 6.5573323518037796e-03</internalNodes>
          <leafValues>
            -7.4690498411655426e-02 2.5062057375907898e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 1.4275535941123962e-02</internalNodes>
          <leafValues>
            2.7385318651795387e-02 -6.8721151351928711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 -2.1887188777327538e-03</internalNodes>
          <leafValues>
            -4.7836220264434814e-01 3.3499576151371002e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 1.9212976098060608e-02</internalNodes>
          <leafValues>
            5.0344366580247879e-02 -3.3104887604713440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 521 1.0552004911005497e-02</internalNodes>
          <leafValues>
            -9.7779601812362671e-02 2.0047651231288910e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 883 -4.9180793575942516e-04</internalNodes>
          <leafValues>
            1.4232192933559418e-01 -1.3171885907649994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 964 3.2934427261352539e-02</internalNodes>
          <leafValues>
            2.9324809089303017e-02 -5.8789533376693726e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 -1.9249429460614920e-03</internalNodes>
          <leafValues>
            1.5106591582298279e-01 -1.2231025844812393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 729 -4.4266097247600555e-03</internalNodes>
          <leafValues>
            2.6083472371101379e-01 -7.3457822203636169e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 666 -3.5673312842845917e-02</internalNodes>
          <leafValues>
            -6.6919100284576416e-01 2.9960991814732552e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 602 -8.5658043622970581e-02</internalNodes>
          <leafValues>
            -5.1538497209548950e-01 2.9327427968382835e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 -3.5126551985740662e-02</internalNodes>
          <leafValues>
            2.3199184238910675e-01 -7.9689562320709229e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 5.8309547603130341e-03</internalNodes>
          <leafValues>
            3.0431609600782394e-02 -6.1856180429458618e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 -4.4557070359587669e-03</internalNodes>
          <leafValues>
            2.6333442330360413e-01 -7.0679754018783569e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 1.2730281800031662e-02</internalNodes>
          <leafValues>
            -6.6051281988620758e-02 3.1401801109313965e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 498 -4.6875262632966042e-03</internalNodes>
          <leafValues>
            1.9294278323650360e-01 -1.0184600949287415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 -2.9066612478345633e-03</internalNodes>
          <leafValues>
            1.9944235682487488e-01 -9.2275582253932953e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 482 -3.1784668099135160e-03</internalNodes>
          <leafValues>
            -4.1009154915809631e-01 4.5929219573736191e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 618 -3.7226981949061155e-03</internalNodes>
          <leafValues>
            2.2945560514926910e-01 -7.6773315668106079e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.6428810358047485e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 777 3.3433635253459215e-03</internalNodes>
          <leafValues>
            7.8417956829071045e-02 6.1746358871459961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 4.1375826112926006e-03</internalNodes>
          <leafValues>
            -1.0486003011465073e-01 4.9714615941047668e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 512 -5.4358085617423058e-03</internalNodes>
          <leafValues>
            2.9817372560501099e-01 -1.8134090304374695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 5.7432046160101891e-03</internalNodes>
          <leafValues>
            -2.1621760725975037e-01 2.8316897153854370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 -2.2321434225887060e-03</internalNodes>
          <leafValues>
            2.0751045644283295e-01 -1.5781952440738678e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 1.4339581131935120e-02</internalNodes>
          <leafValues>
            -9.7252883017063141e-02 4.0868076682090759e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 -1.1081973090767860e-02</internalNodes>
          <leafValues>
            3.3920571208000183e-01 -1.0915800184011459e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 1.4586398378014565e-02</internalNodes>
          <leafValues>
            -8.6110286414623260e-02 3.1800067424774170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 369 -1.6978669445961714e-03</internalNodes>
          <leafValues>
            2.5036969780921936e-01 -1.2437737733125687e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 708 1.2829914689064026e-02</internalNodes>
          <leafValues>
            5.1119163632392883e-02 -5.4143118858337402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 675 -1.6196181531995535e-03</internalNodes>
          <leafValues>
            -5.3631567955017090e-01 4.6280529350042343e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 -2.3190132342278957e-03</internalNodes>
          <leafValues>
            -4.3289941549301147e-01 6.0517251491546631e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 3.5206928849220276e-02</internalNodes>
          <leafValues>
            -9.1207198798656464e-02 3.1217202544212341e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 3.1395033001899719e-03</internalNodes>
          <leafValues>
            -7.8746505081653595e-02 3.6259949207305908e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 1.1297637596726418e-02</internalNodes>
          <leafValues>
            7.4857383966445923e-02 -3.7226554751396179e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 786 4.6712577342987061e-02</internalNodes>
          <leafValues>
            -9.2495582997798920e-02 2.9710096120834351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 -6.8688929080963135e-02</internalNodes>
          <leafValues>
            -4.8226192593574524e-01 4.9391020089387894e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 969 -3.5051193553954363e-03</internalNodes>
          <leafValues>
            -4.8506668210029602e-01 4.1722387075424194e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 694 1.1046628467738628e-02</internalNodes>
          <leafValues>
            -5.4003205150365829e-02 4.2598679661750793e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 -2.0021714270114899e-02</internalNodes>
          <leafValues>
            -4.3884435296058655e-01 4.9609564244747162e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 1.1850561946630478e-02</internalNodes>
          <leafValues>
            -7.8088991343975067e-02 2.7602908015251160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 4.1900265961885452e-02</internalNodes>
          <leafValues>
            -9.9447727203369141e-02 2.0865923166275024e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 590 -2.9064953327178955e-02</internalNodes>
          <leafValues>
            2.9170644283294678e-01 -7.3766425251960754e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 -2.6618237607181072e-03</internalNodes>
          <leafValues>
            -3.2550674676895142e-01 7.5278282165527344e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 3.2252955716103315e-03</internalNodes>
          <leafValues>
            -6.9696784019470215e-02 3.1337058544158936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 582 7.8913231845945120e-04</internalNodes>
          <leafValues>
            6.9301478564739227e-02 -3.2840612530708313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 6.6613638773560524e-03</internalNodes>
          <leafValues>
            4.5366134494543076e-02 -4.4648596644401550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 721 1.9416993018239737e-03</internalNodes>
          <leafValues>
            -1.3086521625518799e-01 1.5360382199287415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 633 6.0106818564236164e-03</internalNodes>
          <leafValues>
            4.8219148069620132e-02 -4.1355597972869873e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 -6.8208400625735521e-04</internalNodes>
          <leafValues>
            1.6169321537017822e-01 -1.2492433935403824e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 756 1.0027646087110043e-02</internalNodes>
          <leafValues>
            3.0267864465713501e-02 -6.4555937051773071e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 948 -6.4138583838939667e-03</internalNodes>
          <leafValues>
            -6.9905740022659302e-01 2.3570762947201729e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 4.4392300769686699e-03</internalNodes>
          <leafValues>
            -5.8655023574829102e-02 3.3790254592895508e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 3.8361353799700737e-03</internalNodes>
          <leafValues>
            3.1548105180263519e-02 -6.6295272111892700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 -1.0007477831095457e-03</internalNodes>
          <leafValues>
            2.2688214480876923e-01 -8.8843353092670441e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 928 4.7894790768623352e-03</internalNodes>
          <leafValues>
            3.0278960242867470e-02 -6.1569523811340332e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 5.6278239935636520e-03</internalNodes>
          <leafValues>
            -7.8841529786586761e-02 2.4433708190917969e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 942 -7.3229577392339706e-03</internalNodes>
          <leafValues>
            3.3265948295593262e-01 -6.7663870751857758e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 -4.5515028759837151e-03</internalNodes>
          <leafValues>
            -4.6472606062889099e-01 4.3983772397041321e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 1.8201436614617705e-03</internalNodes>
          <leafValues>
            -8.1468850374221802e-02 2.3861412703990936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 -1.1703525483608246e-01</internalNodes>
          <leafValues>
            -6.7043519020080566e-01 2.9206298291683197e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 -5.0897812470793724e-03</internalNodes>
          <leafValues>
            5.1766836643218994e-01 -4.1514929383993149e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 -8.7873879820108414e-03</internalNodes>
          <leafValues>
            -5.2038532495498657e-01 4.0998894721269608e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 -5.6120483204722404e-03</internalNodes>
          <leafValues>
            -7.3375105857849121e-01 2.1229419857263565e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 1.0250378400087357e-02</internalNodes>
          <leafValues>
            2.5667199864983559e-02 -6.1355572938919067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 -4.8573492094874382e-03</internalNodes>
          <leafValues>
            1.6895917057991028e-01 -1.1120435595512390e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 2.5750461965799332e-02</internalNodes>
          <leafValues>
            3.5605397075414658e-02 -5.4535841941833496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 932 4.1080308146774769e-03</internalNodes>
          <leafValues>
            5.0505056977272034e-02 -3.3836016058921814e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 3.9434060454368591e-03</internalNodes>
          <leafValues>
            -8.8688671588897705e-02 2.0660057663917542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 553 6.5294457599520683e-03</internalNodes>
          <leafValues>
            -1.2931570410728455e-01 1.6514816880226135e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 931 2.2269077599048615e-02</internalNodes>
          <leafValues>
            -4.7366518527269363e-02 3.8508006930351257e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 3.5300105810165405e-03</internalNodes>
          <leafValues>
            5.3873997181653976e-02 -3.3755952119827271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 -2.4763222783803940e-02</internalNodes>
          <leafValues>
            -3.2349804043769836e-01 5.6731209158897400e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 790 3.1297234818339348e-03</internalNodes>
          <leafValues>
            -6.7191042006015778e-02 2.7804708480834961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 621 -1.7003760440275073e-03</internalNodes>
          <leafValues>
            -4.2539414763450623e-01 4.3875113129615784e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 750 8.3633856847882271e-03</internalNodes>
          <leafValues>
            3.0976327136158943e-02 -5.2819561958312988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 473 -8.7768933735787868e-04</internalNodes>
          <leafValues>
            1.9984373450279236e-01 -9.0307638049125671e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 -9.2862239107489586e-03</internalNodes>
          <leafValues>
            -6.9824051856994629e-01 2.5733994320034981e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 -1.1124708689749241e-03</internalNodes>
          <leafValues>
            1.9901444017887115e-01 -9.2606224119663239e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 575 -3.2134181819856167e-03</internalNodes>
          <leafValues>
            2.3832809925079346e-01 -7.1890726685523987e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 516 5.9113521128892899e-03</internalNodes>
          <leafValues>
            -6.7413553595542908e-02 2.7445399761199951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 -4.5188870280981064e-03</internalNodes>
          <leafValues>
            -4.0147483348846436e-01 4.4757787138223648e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 2.8254585340619087e-03</internalNodes>
          <leafValues>
            4.3743204325437546e-02 -3.7922030687332153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 637 -2.6064300909638405e-03</internalNodes>
          <leafValues>
            2.0811253786087036e-01 -8.2700952887535095e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 635 1.0516709880903363e-03</internalNodes>
          <leafValues>
            -8.9635595679283142e-02 2.6205003261566162e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 453 -1.2204772792756557e-03</internalNodes>
          <leafValues>
            2.0626722276210785e-01 -8.6367763578891754e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 5.0495425239205360e-03</internalNodes>
          <leafValues>
            4.5425735414028168e-02 -4.1301593184471130e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 -3.2243374735116959e-03</internalNodes>
          <leafValues>
            1.4690431952476501e-01 -1.2587989866733551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 -2.3263287730515003e-03</internalNodes>
          <leafValues>
            -4.1975629329681396e-01 4.7924898564815521e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 -5.4887672886252403e-03</internalNodes>
          <leafValues>
            -4.1799965500831604e-01 3.8171879947185516e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 628 4.6792116016149521e-02</internalNodes>
          <leafValues>
            -5.7823952287435532e-02 3.2188871502876282e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 2.3481161333620548e-03</internalNodes>
          <leafValues>
            -5.3754303604364395e-02 3.0415624380111694e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 -3.5063792020082474e-03</internalNodes>
          <leafValues>
            -3.8483345508575439e-01 4.7024305909872055e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 1.6757374396547675e-03</internalNodes>
          <leafValues>
            -1.3115778565406799e-01 1.6631519794464111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 1.1546656489372253e-02</internalNodes>
          <leafValues>
            -5.0141811370849609e-02 3.1787791848182678e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 504 1.8043547868728638e-02</internalNodes>
          <leafValues>
            2.5008214637637138e-02 -7.1255826950073242e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 -3.1235523521900177e-02</internalNodes>
          <leafValues>
            2.1876916289329529e-01 -7.7429860830307007e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 9.7835529595613480e-03</internalNodes>
          <leafValues>
            -5.6040864437818527e-02 2.7669593691825867e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 2.8905952349305153e-03</internalNodes>
          <leafValues>
            5.1800269633531570e-02 -3.1925117969512939e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 436 7.8792851418256760e-03</internalNodes>
          <leafValues>
            -6.0856487601995468e-02 2.8146442770957947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 717 1.3523480854928493e-02</internalNodes>
          <leafValues>
            2.4503752589225769e-02 -7.2409152984619141e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 966 3.1048720702528954e-03</internalNodes>
          <leafValues>
            4.5725930482149124e-02 -3.3751598000526428e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 808 -1.0647572576999664e-02</internalNodes>
          <leafValues>
            -5.4894274473190308e-01 2.8428088873624802e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 -4.8104384914040565e-03</internalNodes>
          <leafValues>
            2.0432402193546295e-01 -8.2248799502849579e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 2.7336277067661285e-02</internalNodes>
          <leafValues>
            -6.7068248987197876e-02 2.3702095448970795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 -1.7123305797576904e-01</internalNodes>
          <leafValues>
            5.2998173236846924e-01 -3.1578365713357925e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 781 -4.3723154813051224e-03</internalNodes>
          <leafValues>
            2.8745722770690918e-01 -5.7618625462055206e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 743 6.7968526855111122e-03</internalNodes>
          <leafValues>
            -6.3940502703189850e-02 2.5016403198242188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -1.6302993753924966e-03</internalNodes>
          <leafValues>
            -2.6190960407257080e-01 6.1918053776025772e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 1.7953850328922272e-03</internalNodes>
          <leafValues>
            -5.3176742047071457e-02 3.0019727349281311e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 6.2259500846266747e-03</internalNodes>
          <leafValues>
            3.1048897653818130e-02 -5.6447005271911621e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 -1.4370339922606945e-03</internalNodes>
          <leafValues>
            1.8240424990653992e-01 -8.9968219399452209e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 -9.0678166598081589e-03</internalNodes>
          <leafValues>
            -6.3961440324783325e-01 2.3404622450470924e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 604 -8.2531813532114029e-03</internalNodes>
          <leafValues>
            -5.9389066696166992e-01 2.3573497310280800e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 573 -2.2232248447835445e-03</internalNodes>
          <leafValues>
            1.9480818510055542e-01 -8.3372615277767181e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 -6.5612345933914185e-03</internalNodes>
          <leafValues>
            -4.2599579691886902e-01 3.7793021649122238e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 571 1.2282801326364279e-03</internalNodes>
          <leafValues>
            -8.5198581218719482e-02 1.9753733277320862e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 -1.0268764197826385e-01</internalNodes>
          <leafValues>
            -3.9634877443313599e-01 4.0690928697586060e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 711 -1.5864435583353043e-02</internalNodes>
          <leafValues>
            1.9112223386764526e-01 -8.7620854377746582e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 4.8304852680303156e-04</internalNodes>
          <leafValues>
            -1.2651769816875458e-01 1.5627197921276093e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.5192077159881592e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 563 -7.2759687900543213e-03</internalNodes>
          <leafValues>
            6.4157706499099731e-01 1.0701754689216614e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -3.8208619225770235e-03</internalNodes>
          <leafValues>
            4.9172374606132507e-01 -9.4503603875637054e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 817 3.4225741401314735e-03</internalNodes>
          <leafValues>
            -1.3255690038204193e-01 3.2899302244186401e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 3.3628481905907393e-03</internalNodes>
          <leafValues>
            -1.6529263556003571e-01 3.8905930519104004e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 4.2874794453382492e-03</internalNodes>
          <leafValues>
            6.4744032919406891e-02 -4.9827992916107178e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 -3.3541000448167324e-03</internalNodes>
          <leafValues>
            1.6614496707916260e-01 -1.8601009249687195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 683 -1.8176173325628042e-03</internalNodes>
          <leafValues>
            2.6610982418060303e-01 -1.1267235130071640e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 -2.2096108645200729e-02</internalNodes>
          <leafValues>
            3.2448813319206238e-01 -8.3371557295322418e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 5.8036940172314644e-03</internalNodes>
          <leafValues>
            -1.2335725873708725e-01 2.5443312525749207e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 4.7676274552941322e-03</internalNodes>
          <leafValues>
            -1.4914961159229279e-01 1.8570756912231445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 2.7053440135205165e-05</internalNodes>
          <leafValues>
            -2.1181178092956543e-01 1.1339543759822845e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 917 -2.5647766888141632e-03</internalNodes>
          <leafValues>
            -4.3090465664863586e-01 5.7283867150545120e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 -9.9645227193832397e-02</internalNodes>
          <leafValues>
            4.7573822736740112e-01 -5.1954109221696854e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 794 2.8423275798559189e-03</internalNodes>
          <leafValues>
            -7.9270146787166595e-02 3.1205773353576660e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 -4.7821709886193275e-03</internalNodes>
          <leafValues>
            -4.4589859247207642e-01 5.0418782979249954e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 1.6041981056332588e-02</internalNodes>
          <leafValues>
            -7.6039068400859833e-02 3.0843210220336914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 778 -3.6943168379366398e-03</internalNodes>
          <leafValues>
            2.7407246828079224e-01 -8.8150359690189362e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 8.4026419790461659e-04</internalNodes>
          <leafValues>
            -1.5873835980892181e-01 1.3953365385532379e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 -8.0992765724658966e-03</internalNodes>
          <leafValues>
            -5.3085809946060181e-01 3.9269823580980301e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 826 3.7676268257200718e-03</internalNodes>
          <leafValues>
            7.0321731269359589e-02 -3.0400907993316650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 719 -8.4449478890746832e-04</internalNodes>
          <leafValues>
            1.6032356023788452e-01 -1.2606577575206757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 1.9298447296023369e-03</internalNodes>
          <leafValues>
            -7.4880234897136688e-02 2.8130438923835754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 517 -3.1593129970133305e-03</internalNodes>
          <leafValues>
            -4.1825935244560242e-01 4.7720715403556824e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 7.7791810035705566e-03</internalNodes>
          <leafValues>
            4.1349764913320541e-02 -4.5770570635795593e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 -3.2682183664292097e-03</internalNodes>
          <leafValues>
            2.1473638713359833e-01 -9.5344312489032745e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 490 -2.2884239442646503e-03</internalNodes>
          <leafValues>
            2.4062317609786987e-01 -9.0378150343894958e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 2.5960609782487154e-03</internalNodes>
          <leafValues>
            3.4732636064291000e-02 -6.1617314815521240e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 8.0937854945659637e-03</internalNodes>
          <leafValues>
            3.9185214787721634e-02 -4.7461858391761780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 5.6226095184683800e-03</internalNodes>
          <leafValues>
            3.3066269010305405e-02 -5.4723787307739258e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 3.3066368196159601e-03</internalNodes>
          <leafValues>
            -1.0691711306571960e-01 1.8520861864089966e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 -6.9545931182801723e-03</internalNodes>
          <leafValues>
            2.8144246339797974e-01 -6.7726366221904755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 2.0960648544132710e-03</internalNodes>
          <leafValues>
            6.5764650702476501e-02 -3.1323519349098206e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 546 -2.9915343038737774e-03</internalNodes>
          <leafValues>
            2.4481751024723053e-01 -7.8186720609664917e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 9.8559018224477768e-03</internalNodes>
          <leafValues>
            2.9282161965966225e-02 -6.9216150045394897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 567 -9.9846869707107544e-03</internalNodes>
          <leafValues>
            3.1725984811782837e-01 -6.4144395291805267e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 417 4.0295444428920746e-02</internalNodes>
          <leafValues>
            -1.0545746237039566e-01 1.9351178407669067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 -4.5808870345354080e-03</internalNodes>
          <leafValues>
            -2.7045866847038269e-01 6.3011758029460907e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 -3.4120261669158936e-02</internalNodes>
          <leafValues>
            -5.0046062469482422e-01 3.3663876354694366e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 475 5.9857115149497986e-02</internalNodes>
          <leafValues>
            -6.3651382923126221e-02 2.9614394903182983e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 3.9175078272819519e-03</internalNodes>
          <leafValues>
            3.2706990838050842e-02 -5.7929420471191406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 1.1147339828312397e-02</internalNodes>
          <leafValues>
            -7.9590849578380585e-02 2.3456735908985138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 642 -2.5253929197788239e-03</internalNodes>
          <leafValues>
            -5.9936809539794922e-01 2.9250813648104668e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 578 -1.6680266708135605e-02</internalNodes>
          <leafValues>
            3.0521371960639954e-01 -5.8340109884738922e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 7.4669495224952698e-03</internalNodes>
          <leafValues>
            3.1027967110276222e-02 -6.2497019767761230e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 2.8406577184796333e-03</internalNodes>
          <leafValues>
            4.1042126715183258e-02 -4.1006734967231750e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 -4.7403648495674133e-03</internalNodes>
          <leafValues>
            2.0529302954673767e-01 -8.8142603635787964e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 700 3.7362352013587952e-03</internalNodes>
          <leafValues>
            -6.1495106667280197e-02 3.4078758955001831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 -4.4546797871589661e-02</internalNodes>
          <leafValues>
            -8.9466398954391479e-01 2.2682141512632370e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 9.0460591018199921e-03</internalNodes>
          <leafValues>
            4.8672281205654144e-02 -3.3261755108833313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 574 -3.9043920114636421e-03</internalNodes>
          <leafValues>
            2.2659721970558167e-01 -7.7551431953907013e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 1.0861126706004143e-02</internalNodes>
          <leafValues>
            -7.7613808214664459e-02 2.2056312859058380e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 -1.2531490065157413e-03</internalNodes>
          <leafValues>
            2.2868460416793823e-01 -8.1201769411563873e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 -5.6936070322990417e-03</internalNodes>
          <leafValues>
            -5.6885385513305664e-01 3.6236546933650970e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 1.4989164192229509e-03</internalNodes>
          <leafValues>
            3.5795394331216812e-02 -4.7115951776504517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 4.8389505594968796e-02</internalNodes>
          <leafValues>
            2.8578057885169983e-02 -5.6662684679031372e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 706 -6.7009456455707550e-02</internalNodes>
          <leafValues>
            3.6811140179634094e-01 -4.8919521272182465e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 -6.9928979501128197e-03</internalNodes>
          <leafValues>
            2.2268642485141754e-01 -8.1494621932506561e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 506 -5.0552012398838997e-03</internalNodes>
          <leafValues>
            -3.9911568164825439e-01 4.8226438462734222e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 577 -1.6228569438681006e-03</internalNodes>
          <leafValues>
            2.0442382991313934e-01 -9.1497577726840973e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 1.4232760295271873e-03</internalNodes>
          <leafValues>
            -8.9272662997245789e-02 1.9328704476356506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 435 1.4769298955798149e-02</internalNodes>
          <leafValues>
            4.3185703456401825e-02 -4.1162547469139099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 1.0630609467625618e-02</internalNodes>
          <leafValues>
            2.0712809637188911e-02 -7.4652433395385742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 -4.6220947988331318e-03</internalNodes>
          <leafValues>
            -2.6612642407417297e-01 6.2285874038934708e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 -2.2310419008135796e-03</internalNodes>
          <leafValues>
            -6.4442801475524902e-01 2.2920599207282066e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 -7.5279012322425842e-02</internalNodes>
          <leafValues>
            -5.4105269908905029e-01 2.7185589075088501e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 6.8284743465483189e-03</internalNodes>
          <leafValues>
            -8.5639610886573792e-02 1.9920121133327484e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 410 -7.6434519141912460e-03</internalNodes>
          <leafValues>
            2.4088086187839508e-01 -6.8174593150615692e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 -3.6089830100536346e-03</internalNodes>
          <leafValues>
            1.8979941308498383e-01 -9.1478735208511353e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 4.5644217729568481e-01</internalNodes>
          <leafValues>
            -3.5746987909078598e-02 5.1038581132888794e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 4.3285787105560303e-03</internalNodes>
          <leafValues>
            5.0642926245927811e-02 -3.4335514903068542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 -3.0497182160615921e-03</internalNodes>
          <leafValues>
            1.5943552553653717e-01 -1.0838232934474945e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 2.9164180159568787e-03</internalNodes>
          <leafValues>
            -6.2386274337768555e-02 2.6772892475128174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 536 -6.7837955430150032e-03</internalNodes>
          <leafValues>
            -2.8828456997871399e-01 5.4219286888837814e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 4.7922343946993351e-03</internalNodes>
          <leafValues>
            4.1374113410711288e-02 -3.9169260859489441e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 933 1.1295514181256294e-02</internalNodes>
          <leafValues>
            -5.8489643037319183e-02 2.8171694278717041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 -1.9917660392820835e-03</internalNodes>
          <leafValues>
            1.6076046228408813e-01 -1.1378295719623566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 613 -1.5121680917218328e-03</internalNodes>
          <leafValues>
            -4.2076098918914795e-01 3.7524472922086716e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 -2.0562859252095222e-02</internalNodes>
          <leafValues>
            1.6639313101768494e-01 -9.5253549516201019e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 709 -2.3025400936603546e-02</internalNodes>
          <leafValues>
            -3.1259611248970032e-01 5.8197792619466782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 584 -3.6875833757221699e-03</internalNodes>
          <leafValues>
            2.2967162728309631e-01 -7.0608235895633698e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 632 1.2718647718429565e-02</internalNodes>
          <leafValues>
            4.4480670243501663e-02 -4.1392150521278381e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 1.1408344144001603e-03</internalNodes>
          <leafValues>
            -8.0126009881496429e-02 2.2867898643016815e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 2.8175862506031990e-02</internalNodes>
          <leafValues>
            -7.3071323335170746e-02 2.3685938119888306e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 596 6.4620561897754669e-02</internalNodes>
          <leafValues>
            4.4116552919149399e-02 -4.2114758491516113e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 -2.5126901455223560e-03</internalNodes>
          <leafValues>
            1.7448952794075012e-01 -9.9981509149074554e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 949 -2.7390490286052227e-03</internalNodes>
          <leafValues>
            -3.7159797549247742e-01 4.5618161559104919e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 4.5697823166847229e-02</internalNodes>
          <leafValues>
            2.5755234062671661e-02 -5.6903475522994995e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 -8.8378116488456726e-03</internalNodes>
          <leafValues>
            1.5099802613258362e-01 -9.9460050463676453e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 488 3.5273849498480558e-03</internalNodes>
          <leafValues>
            4.6311099082231522e-02 -3.4411522746086121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 8.8435463840141892e-04</internalNodes>
          <leafValues>
            -6.9374859333038330e-02 2.2488924860954285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 -1.6139955259859562e-03</internalNodes>
          <leafValues>
            3.3221766352653503e-01 -5.1287319511175156e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 4.1209710761904716e-03</internalNodes>
          <leafValues>
            3.1247327104210854e-02 -5.1796287298202515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 6.8625092506408691e-02</internalNodes>
          <leafValues>
            -4.8976749181747437e-02 3.6052888631820679e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 1.9539505243301392e-02</internalNodes>
          <leafValues>
            -3.5133589059114456e-02 4.0571358799934387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 1.0195776820182800e-02</internalNodes>
          <leafValues>
            2.9883516952395439e-02 -5.4385137557983398e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 -9.7053672652691603e-04</internalNodes>
          <leafValues>
            1.7753951251506805e-01 -8.5428759455680847e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 7.0950109511613846e-03</internalNodes>
          <leafValues>
            2.6922283694148064e-02 -5.7553297281265259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 3.3868722617626190e-02</internalNodes>
          <leafValues>
            2.4893242865800858e-02 -5.1963424682617188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 8.9386161416769028e-03</internalNodes>
          <leafValues>
            -6.5781995654106140e-02 2.1598634123802185e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 934 -1.1335080489516258e-03</internalNodes>
          <leafValues>
            1.3626587390899658e-01 -1.2551343441009521e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.5342161655426025e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 442 6.4967479556798935e-03</internalNodes>
          <leafValues>
            9.2756643891334534e-02 6.3256287574768066e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 -7.0229507982730865e-03</internalNodes>
          <leafValues>
            4.7454160451889038e-01 -9.1181516647338867e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 788 -1.1249051894992590e-03</internalNodes>
          <leafValues>
            2.7701923251152039e-01 -1.5582662820816040e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 2.1593673154711723e-02</internalNodes>
          <leafValues>
            -1.2302023172378540e-01 4.3630394339561462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 2.7066322509199381e-03</internalNodes>
          <leafValues>
            -1.9653171300888062e-01 1.8972468376159668e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 1.9835126586258411e-03</internalNodes>
          <leafValues>
            -1.0700473189353943e-01 3.2027366757392883e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 -2.2188067436218262e-02</internalNodes>
          <leafValues>
            -3.4057390689849854e-01 9.7314909100532532e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 900 6.6005333792418242e-04</internalNodes>
          <leafValues>
            -1.9594040513038635e-01 1.6130633652210236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 9.8964036442339420e-04</internalNodes>
          <leafValues>
            -1.6125436127185822e-01 1.5948192775249481e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 557 5.2934061735868454e-02</internalNodes>
          <leafValues>
            -7.4111364781856537e-02 3.6531463265419006e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 9.6504454268142581e-04</internalNodes>
          <leafValues>
            -1.4388854801654816e-01 1.8791662156581879e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 -6.5498230978846550e-03</internalNodes>
          <leafValues>
            -5.1886290311813354e-01 4.9368891865015030e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 -3.1677065417170525e-03</internalNodes>
          <leafValues>
            -4.5819568634033203e-01 4.6636309474706650e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 -3.8453172892332077e-03</internalNodes>
          <leafValues>
            1.8924130499362946e-01 -1.2508736550807953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 1.0155377909541130e-02</internalNodes>
          <leafValues>
            -7.4480414390563965e-02 3.1350296735763550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 9.4711792189627886e-04</internalNodes>
          <leafValues>
            -1.0617389529943466e-01 2.3810641467571259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 -2.4116779677569866e-03</internalNodes>
          <leafValues>
            -3.9038985967636108e-01 6.1166737228631973e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -3.9347349666059017e-03</internalNodes>
          <leafValues>
            -5.7243233919143677e-01 3.4851558506488800e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 6.6558746621012688e-03</internalNodes>
          <leafValues>
            2.8953079134225845e-02 -6.2028181552886963e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 -2.2842539474368095e-03</internalNodes>
          <leafValues>
            -3.3340734243392944e-01 5.2056297659873962e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 829 7.6677929610013962e-04</internalNodes>
          <leafValues>
            -9.4001799821853638e-02 2.0127503573894501e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 -3.9554573595523834e-02</internalNodes>
          <leafValues>
            3.9717516303062439e-01 -5.0761125981807709e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 -2.2336144000291824e-02</internalNodes>
          <leafValues>
            2.0378184318542480e-01 -1.0499230772256851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 682 4.1007921099662781e-03</internalNodes>
          <leafValues>
            3.9605572819709778e-02 -5.6042844057083130e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 -6.3549563288688660e-02</internalNodes>
          <leafValues>
            5.9350001811981201e-01 -3.4742560237646103e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 3.7244942504912615e-03</internalNodes>
          <leafValues>
            -5.1448952406644821e-02 3.1930196285247803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 483 1.2377202510833740e-02</internalNodes>
          <leafValues>
            3.3052973449230194e-02 -6.5478527545928955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 499 -1.0786693543195724e-02</internalNodes>
          <leafValues>
            2.4589619040489197e-01 -7.2101429104804993e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 439 -1.4055164530873299e-02</internalNodes>
          <leafValues>
            3.5272973775863647e-01 -5.8014977723360062e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 -1.4940403401851654e-02</internalNodes>
          <leafValues>
            -4.5820471644401550e-01 4.5480247586965561e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 850 1.8285561818629503e-03</internalNodes>
          <leafValues>
            3.2248783856630325e-02 -4.9957463145256042e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 1.8330020830035210e-02</internalNodes>
          <leafValues>
            4.6777416020631790e-02 -3.7174671888351440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 579 -1.1065398575738072e-03</internalNodes>
          <leafValues>
            -3.4255436062812805e-01 4.5848693698644638e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 481 -2.7136057615280151e-03</internalNodes>
          <leafValues>
            2.4459818005561829e-01 -6.8955913186073303e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 3.5836829338222742e-03</internalNodes>
          <leafValues>
            3.9574686437845230e-02 -4.1517943143844604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 1.3739100657403469e-02</internalNodes>
          <leafValues>
            -6.2165945768356323e-02 2.8256937861442566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 -1.1374817695468664e-03</internalNodes>
          <leafValues>
            1.6939654946327209e-01 -1.0761212557554245e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 -1.1336053721606731e-03</internalNodes>
          <leafValues>
            2.4757325649261475e-01 -7.0519238710403442e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 2.4884669110178947e-03</internalNodes>
          <leafValues>
            4.3731488287448883e-02 -4.3731775879859924e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 693 9.1567426919937134e-02</internalNodes>
          <leafValues>
            -6.4225792884826660e-02 2.6794373989105225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 863 8.1775393337011337e-03</internalNodes>
          <leafValues>
            -6.9729812443256378e-02 2.7086481451988220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 539 -3.1169723719358444e-02</internalNodes>
          <leafValues>
            -5.1539027690887451e-01 4.1658539324998856e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 589 -1.8694017082452774e-02</internalNodes>
          <leafValues>
            2.1681772172451019e-01 -8.8622607290744781e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 472 5.7301642373204231e-03</internalNodes>
          <leafValues>
            -7.2939246892929077e-02 2.3475584387779236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 1.6503074439242482e-03</internalNodes>
          <leafValues>
            6.9048069417476654e-02 -2.8440928459167480e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 -7.0378202944993973e-03</internalNodes>
          <leafValues>
            -4.7214046120643616e-01 3.5182151943445206e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 -2.3838514462113380e-03</internalNodes>
          <leafValues>
            1.9753867387771606e-01 -8.9424118399620056e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 7.9844584688544273e-03</internalNodes>
          <leafValues>
            -7.4890352785587311e-02 2.3181028664112091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 1.3400901807472110e-03</internalNodes>
          <leafValues>
            -1.0847068578004837e-01 1.9428721070289612e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 -2.9489169828593731e-03</internalNodes>
          <leafValues>
            -4.0673759579658508e-01 4.7985706478357315e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 952 -5.9452969580888748e-03</internalNodes>
          <leafValues>
            -4.5123618841171265e-01 3.6464843899011612e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 2.3310778196901083e-03</internalNodes>
          <leafValues>
            -8.0826595425605774e-02 2.1532072126865387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 -1.9684966653585434e-02</internalNodes>
          <leafValues>
            2.3020596802234650e-01 -8.1534743309020996e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 -1.6875991132110357e-03</internalNodes>
          <leafValues>
            2.3354543745517731e-01 -7.0870727300643921e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 -1.5521588502451777e-03</internalNodes>
          <leafValues>
            1.7072069644927979e-01 -1.0233023017644882e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 -3.8005404174327850e-02</internalNodes>
          <leafValues>
            -6.0487842559814453e-01 2.8272373601794243e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 -2.1617640741169453e-03</internalNodes>
          <leafValues>
            -5.5644184350967407e-01 2.5939555838704109e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 653 -2.6441088411957026e-03</internalNodes>
          <leafValues>
            -4.4886007905006409e-01 3.1817246228456497e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 -7.3187379166483879e-04</internalNodes>
          <leafValues>
            2.2164805233478546e-01 -7.6915167272090912e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 -3.4191065933555365e-03</internalNodes>
          <leafValues>
            -4.4704499840736389e-01 3.5689469426870346e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 597 1.4393313322216272e-03</internalNodes>
          <leafValues>
            -8.1237293779850006e-02 2.0022353529930115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 1.0892877355217934e-02</internalNodes>
          <leafValues>
            -5.9532187879085541e-02 2.6124969124794006e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 250 2.5800592266023159e-03</internalNodes>
          <leafValues>
            5.3012363612651825e-02 -3.0210506916046143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 873 -3.7106748204678297e-03</internalNodes>
          <leafValues>
            -3.7514480948448181e-01 4.0868300944566727e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 -8.9250775054097176e-03</internalNodes>
          <leafValues>
            1.8222920596599579e-01 -8.4776028990745544e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 -4.4729106128215790e-02</internalNodes>
          <leafValues>
            2.5505220890045166e-01 -6.6192351281642914e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 -1.0527699440717697e-01</internalNodes>
          <leafValues>
            -4.8529133200645447e-01 3.4933239221572876e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 -6.5371848642826080e-02</internalNodes>
          <leafValues>
            3.7187507748603821e-01 -5.8592520654201508e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 5.1612589508295059e-02</internalNodes>
          <leafValues>
            -7.6325275003910065e-02 2.1536968648433685e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 4.2564887553453445e-03</internalNodes>
          <leafValues>
            -7.7395483851432800e-02 2.1907977759838104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 671 -3.5104658454656601e-03</internalNodes>
          <leafValues>
            -4.1962492465972900e-01 4.1911821812391281e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 -9.5827406039461493e-04</internalNodes>
          <leafValues>
            1.7163562774658203e-01 -1.0379400104284286e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 -3.3416904509067535e-02</internalNodes>
          <leafValues>
            -5.5419611930847168e-01 3.4831445664167404e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 7.4058589525520802e-03</internalNodes>
          <leafValues>
            -6.6642671823501587e-02 2.6589548587799072e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 -7.3020011186599731e-03</internalNodes>
          <leafValues>
            1.7654685676097870e-01 -8.7743707001209259e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 505 -4.5689288526773453e-03</internalNodes>
          <leafValues>
            1.9193352758884430e-01 -8.0177433788776398e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 740 -8.1814359873533249e-03</internalNodes>
          <leafValues>
            2.3826718330383301e-01 -6.4945526421070099e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 4.5117912814021111e-03</internalNodes>
          <leafValues>
            3.0824853107333183e-02 -5.3869700431823730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 502 2.8812256641685963e-03</internalNodes>
          <leafValues>
            -9.6859149634838104e-02 1.6199249029159546e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 2.4804673157632351e-03</internalNodes>
          <leafValues>
            2.1523499861359596e-02 -7.0121616125106812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 640 6.8080224096775055e-02</internalNodes>
          <leafValues>
            2.1451909095048904e-02 -6.2823659181594849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 3.8525110576301813e-03</internalNodes>
          <leafValues>
            -8.2842335104942322e-02 1.9410280883312225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 904 -2.5594229809939861e-03</internalNodes>
          <leafValues>
            1.9102296233177185e-01 -8.5406452417373657e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 5.3212735801935196e-03</internalNodes>
          <leafValues>
            -7.8593194484710693e-02 2.2497585415840149e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 3.1688171438872814e-03</internalNodes>
          <leafValues>
            3.6922473460435867e-02 -4.5733535289764404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 848 1.5939949080348015e-02</internalNodes>
          <leafValues>
            3.7862829864025116e-02 -3.9459064602851868e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 634 2.3682378232479095e-03</internalNodes>
          <leafValues>
            3.5111214965581894e-02 -4.3085646629333496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 1.2975309044122696e-02</internalNodes>
          <leafValues>
            -7.1412295103073120e-02 2.1616001427173615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 -9.9381525069475174e-03</internalNodes>
          <leafValues>
            2.7434283494949341e-01 -5.9007227420806885e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -4.4495373964309692e-02</internalNodes>
          <leafValues>
            -5.7859867811203003e-01 2.8963629156351089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 1.0330894030630589e-02</internalNodes>
          <leafValues>
            -6.5200082957744598e-02 2.6670095324516296e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 745 -4.2830477468669415e-03</internalNodes>
          <leafValues>
            -5.2546054124832153e-01 3.3751774579286575e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 899 1.5691295266151428e-02</internalNodes>
          <leafValues>
            2.9164802283048630e-02 -4.8206093907356262e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 903 -1.2077906867489219e-03</internalNodes>
          <leafValues>
            2.4997046589851379e-01 -6.1069376766681671e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 6.6226916387677193e-03</internalNodes>
          <leafValues>
            4.8671871423721313e-02 -3.4221932291984558e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 -5.1539484411478043e-03</internalNodes>
          <leafValues>
            -3.5398313403129578e-01 4.2696069926023483e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 -2.4832391645759344e-03</internalNodes>
          <leafValues>
            2.0646870136260986e-01 -7.5708754360675812e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 3.0503068119287491e-03</internalNodes>
          <leafValues>
            2.1991817280650139e-02 -6.9251579046249390e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 9.2179588973522186e-03</internalNodes>
          <leafValues>
            1.7923980951309204e-02 -7.2638368606567383e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 3.7863249890506268e-03</internalNodes>
          <leafValues>
            -6.6987045109272003e-02 2.2602997720241547e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          0 0 8 1 -1.</_>
        <_>
          4 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 6 -1.</_>
        <_>
          0 0 4 3 2.</_>
        <_>
          4 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 6 -1.</_>
        <_>
          0 0 5 3 2.</_>
        <_>
          5 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 5 -1.</_>
        <_>
          6 0 12 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 6 -1.</_>
        <_>
          0 0 8 3 2.</_>
        <_>
          8 3 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 10 -1.</_>
        <_>
          0 0 8 5 2.</_>
        <_>
          8 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          12 0 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 12 -1.</_>
        <_>
          0 0 12 6 2.</_>
        <_>
          12 6 12 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 8 10 -1.</_>
        <_>
          0 1 4 5 2.</_>
        <_>
          4 6 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 10 2 -1.</_>
        <_>
          5 1 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 10 6 -1.</_>
        <_>
          0 1 5 3 2.</_>
        <_>
          5 4 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 10 10 -1.</_>
        <_>
          0 1 5 5 2.</_>
        <_>
          5 6 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 24 2 -1.</_>
        <_>
          0 1 12 1 2.</_>
        <_>
          12 2 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 18 9 -1.</_>
        <_>
          0 5 18 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 3 6 9 -1.</_>
        <_>
          0 6 6 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 3 24 2 -1.</_>
        <_>
          0 3 12 1 2.</_>
        <_>
          12 4 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 16 15 -1.</_>
        <_>
          0 9 16 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 22 6 -1.</_>
        <_>
          0 6 22 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 24 6 -1.</_>
        <_>
          0 6 24 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 24 2 -1.</_>
        <_>
          0 5 12 1 2.</_>
        <_>
          12 6 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 2 -1.</_>
        <_>
          0 6 12 1 2.</_>
        <_>
          12 7 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 3 -1.</_>
        <_>
          0 7 24 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 8 2 -1.</_>
        <_>
          0 7 4 1 2.</_>
        <_>
          4 8 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 1 -1.</_>
        <_>
          8 7 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 4 -1.</_>
        <_>
          0 7 12 2 2.</_>
        <_>
          12 9 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 22 4 -1.</_>
        <_>
          0 8 22 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 4 -1.</_>
        <_>
          0 8 12 2 2.</_>
        <_>
          12 10 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 6 -1.</_>
        <_>
          0 8 12 3 2.</_>
        <_>
          12 11 12 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 8 -1.</_>
        <_>
          0 8 12 4 2.</_>
        <_>
          12 12 12 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 4 3 -1.</_>
        <_>
          0 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 4 8 -1.</_>
        <_>
          0 11 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 7 2 -1.</_>
        <_>
          0 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 20 2 -1.</_>
        <_>
          0 9 10 1 2.</_>
        <_>
          10 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 22 2 -1.</_>
        <_>
          0 9 11 1 2.</_>
        <_>
          11 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 22 4 -1.</_>
        <_>
          0 9 11 2 2.</_>
        <_>
          11 11 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 22 10 -1.</_>
        <_>
          0 9 11 5 2.</_>
        <_>
          11 14 11 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 4 -1.</_>
        <_>
          0 9 12 2 2.</_>
        <_>
          12 11 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 3 3 -1.</_>
        <_>
          0 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 2 -1.</_>
        <_>
          0 10 12 1 2.</_>
        <_>
          12 11 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 6 8 -1.</_>
        <_>
          0 11 3 4 2.</_>
        <_>
          3 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 24 2 -1.</_>
        <_>
          0 11 12 1 2.</_>
        <_>
          12 12 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 24 7 -1.</_>
        <_>
          12 11 12 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 6 10 -1.</_>
        <_>
          3 12 3 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 13 22 10 -1.</_>
        <_>
          11 13 11 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 8 6 -1.</_>
        <_>
          4 14 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 18 7 -1.</_>
        <_>
          9 15 9 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 16 10 3 -1.</_>
        <_>
          5 16 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 17 3 3 -1.</_>
        <_>
          1 17 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 17 3 4 -1.</_>
        <_>
          1 17 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 18 6 4 -1.</_>
        <_>
          3 18 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 18 10 4 -1.</_>
        <_>
          5 18 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 18 9 3 -1.</_>
        <_>
          0 19 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 18 18 6 -1.</_>
        <_>
          0 18 9 3 2.</_>
        <_>
          9 21 9 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 19 3 5 -1.</_>
        <_>
          1 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 20 24 2 -1.</_>
        <_>
          6 20 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 21 6 3 -1.</_>
        <_>
          0 22 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 6 1 -1.</_>
        <_>
          4 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 12 3 -1.</_>
        <_>
          4 0 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 8 1 -1.</_>
        <_>
          5 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 20 1 -1.</_>
        <_>
          6 0 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 14 4 -1.</_>
        <_>
          1 0 7 2 2.</_>
        <_>
          8 2 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 14 8 -1.</_>
        <_>
          1 0 7 4 2.</_>
        <_>
          8 4 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 14 10 -1.</_>
        <_>
          1 0 7 5 2.</_>
        <_>
          8 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 22 21 -1.</_>
        <_>
          12 3 11 21 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 14 12 -1.</_>
        <_>
          1 6 14 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 5 3 -1.</_>
        <_>
          1 5 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 9 3 -1.</_>
        <_>
          1 5 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 22 2 -1.</_>
        <_>
          1 4 11 1 2.</_>
        <_>
          12 5 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 22 18 -1.</_>
        <_>
          12 4 11 18 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 5 21 6 -1.</_>
        <_>
          8 7 7 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 5 22 2 -1.</_>
        <_>
          1 5 11 1 2.</_>
        <_>
          12 6 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 4 3 -1.</_>
        <_>
          1 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 22 2 -1.</_>
        <_>
          1 6 11 1 2.</_>
        <_>
          12 7 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 13 8 -1.</_>
        <_>
          1 10 13 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 6 2 -1.</_>
        <_>
          1 7 3 1 2.</_>
        <_>
          4 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 12 6 -1.</_>
        <_>
          5 9 4 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 4 9 -1.</_>
        <_>
          1 10 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 21 2 -1.</_>
        <_>
          1 8 21 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 20 4 -1.</_>
        <_>
          1 8 10 2 2.</_>
        <_>
          11 10 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 23 3 -1.</_>
        <_>
          1 9 23 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 20 2 -1.</_>
        <_>
          1 9 10 1 2.</_>
        <_>
          11 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 20 6 -1.</_>
        <_>
          1 9 10 3 2.</_>
        <_>
          11 12 10 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 22 2 -1.</_>
        <_>
          1 9 11 1 2.</_>
        <_>
          12 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 4 1 -1.</_>
        <_>
          3 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 22 2 -1.</_>
        <_>
          1 10 11 1 2.</_>
        <_>
          12 11 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 8 1 -1.</_>
        <_>
          3 12 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 8 12 -1.</_>
        <_>
          1 12 4 6 2.</_>
        <_>
          5 18 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 20 2 -1.</_>
        <_>
          1 12 10 1 2.</_>
        <_>
          11 13 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 3 11 -1.</_>
        <_>
          2 13 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 22 4 -1.</_>
        <_>
          1 13 11 2 2.</_>
        <_>
          12 15 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 19 4 -1.</_>
        <_>
          1 15 19 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 14 10 10 -1.</_>
        <_>
          1 14 5 5 2.</_>
        <_>
          6 19 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 14 22 2 -1.</_>
        <_>
          1 14 11 1 2.</_>
        <_>
          12 15 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 15 3 8 -1.</_>
        <_>
          2 15 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 16 3 1 -1.</_>
        <_>
          2 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 16 3 3 -1.</_>
        <_>
          2 16 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 17 3 1 -1.</_>
        <_>
          2 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 17 3 7 -1.</_>
        <_>
          2 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 20 1 -1.</_>
        <_>
          7 0 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 14 12 -1.</_>
        <_>
          2 0 7 6 2.</_>
        <_>
          9 6 7 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 22 1 -1.</_>
        <_>
          13 0 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 3 10 3 -1.</_>
        <_>
          2 4 10 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 3 20 2 -1.</_>
        <_>
          2 3 10 1 2.</_>
        <_>
          12 4 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 3 3 -1.</_>
        <_>
          2 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 20 2 -1.</_>
        <_>
          2 4 10 1 2.</_>
        <_>
          12 5 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 22 18 -1.</_>
        <_>
          13 4 11 18 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 1 3 -1.</_>
        <_>
          2 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 2 2 -1.</_>
        <_>
          2 5 1 1 2.</_>
        <_>
          3 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 20 2 -1.</_>
        <_>
          2 5 10 1 2.</_>
        <_>
          12 6 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 3 3 -1.</_>
        <_>
          2 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 20 2 -1.</_>
        <_>
          2 6 10 1 2.</_>
        <_>
          12 7 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 22 2 -1.</_>
        <_>
          2 6 11 1 2.</_>
        <_>
          13 7 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 22 6 -1.</_>
        <_>
          2 6 11 3 2.</_>
        <_>
          13 9 11 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 19 3 -1.</_>
        <_>
          2 7 19 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 7 3 -1.</_>
        <_>
          2 8 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 7 4 -1.</_>
        <_>
          2 8 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 21 4 -1.</_>
        <_>
          2 8 21 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 21 6 -1.</_>
        <_>
          2 9 21 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 5 2 -1.</_>
        <_>
          2 8 5 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 8 20 3 -1.</_>
        <_>
          2 9 20 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 20 8 -1.</_>
        <_>
          2 9 10 4 2.</_>
        <_>
          12 13 10 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 22 2 -1.</_>
        <_>
          2 9 11 1 2.</_>
        <_>
          13 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 19 3 -1.</_>
        <_>
          2 10 19 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 3 1 -1.</_>
        <_>
          3 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 10 4 1 -1.</_>
        <_>
          4 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 22 2 -1.</_>
        <_>
          2 10 11 1 2.</_>
        <_>
          13 11 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 3 1 -1.</_>
        <_>
          3 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 11 22 2 -1.</_>
        <_>
          2 11 11 1 2.</_>
        <_>
          13 12 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 11 2 -1.</_>
        <_>
          2 11 11 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 11 18 3 -1.</_>
        <_>
          2 12 18 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 13 20 4 -1.</_>
        <_>
          2 13 10 2 2.</_>
        <_>
          12 15 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 3 7 -1.</_>
        <_>
          3 15 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 16 4 1 -1.</_>
        <_>
          3 17 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 17 3 5 -1.</_>
        <_>
          3 17 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 0 9 16 -1.</_>
        <_>
          6 0 3 16 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 0 18 3 -1.</_>
        <_>
          3 0 9 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 2 18 2 -1.</_>
        <_>
          3 2 9 1 2.</_>
        <_>
          12 3 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 12 1 -1.</_>
        <_>
          6 3 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 20 3 -1.</_>
        <_>
          8 3 10 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 18 2 -1.</_>
        <_>
          3 3 9 1 2.</_>
        <_>
          12 4 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 1 3 -1.</_>
        <_>
          3 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 5 16 -1.</_>
        <_>
          3 8 5 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 18 8 -1.</_>
        <_>
          3 4 9 4 2.</_>
        <_>
          12 8 9 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 20 19 -1.</_>
        <_>
          13 4 10 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 20 2 -1.</_>
        <_>
          3 5 10 1 2.</_>
        <_>
          13 6 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 16 9 -1.</_>
        <_>
          3 8 16 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 20 9 -1.</_>
        <_>
          3 8 20 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 2 3 -1.</_>
        <_>
          3 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 18 2 -1.</_>
        <_>
          3 6 9 1 2.</_>
        <_>
          12 7 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 3 1 -1.</_>
        <_>
          4 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 3 2 -1.</_>
        <_>
          3 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 9 6 -1.</_>
        <_>
          6 9 3 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 20 2 -1.</_>
        <_>
          8 7 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 6 2 -1.</_>
        <_>
          3 8 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 7 3 -1.</_>
        <_>
          3 8 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 18 4 -1.</_>
        <_>
          3 7 9 2 2.</_>
        <_>
          12 9 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 19 12 -1.</_>
        <_>
          3 11 19 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 8 20 4 -1.</_>
        <_>
          3 8 10 2 2.</_>
        <_>
          13 10 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 1 -1.</_>
        <_>
          4 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 6 3 -1.</_>
        <_>
          3 10 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 16 2 -1.</_>
        <_>
          3 9 8 1 2.</_>
        <_>
          11 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 20 2 -1.</_>
        <_>
          3 9 10 1 2.</_>
        <_>
          13 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 20 12 -1.</_>
        <_>
          3 9 10 6 2.</_>
        <_>
          13 15 10 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 1 -1.</_>
        <_>
          4 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 2 -1.</_>
        <_>
          4 11 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 15 3 -1.</_>
        <_>
          3 11 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 20 8 -1.</_>
        <_>
          3 12 20 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 1 -1.</_>
        <_>
          4 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 3 -1.</_>
        <_>
          4 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 4 -1.</_>
        <_>
          4 11 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 7 -1.</_>
        <_>
          4 11 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 6 2 -1.</_>
        <_>
          3 11 3 1 2.</_>
        <_>
          6 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 8 -1.</_>
        <_>
          3 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 7 8 -1.</_>
        <_>
          3 15 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 20 2 -1.</_>
        <_>
          3 11 10 1 2.</_>
        <_>
          13 12 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 20 8 -1.</_>
        <_>
          13 11 10 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 4 8 -1.</_>
        <_>
          5 12 2 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 8 -1.</_>
        <_>
          3 16 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 18 8 -1.</_>
        <_>
          3 16 18 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 13 3 3 -1.</_>
        <_>
          4 13 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 13 19 6 -1.</_>
        <_>
          3 16 19 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 14 6 10 -1.</_>
        <_>
          3 14 3 5 2.</_>
        <_>
          6 19 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 15 6 2 -1.</_>
        <_>
          6 15 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 4 1 -1.</_>
        <_>
          4 17 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 20 3 3 -1.</_>
        <_>
          4 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 0 16 20 -1.</_>
        <_>
          4 10 16 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 2 8 13 -1.</_>
        <_>
          6 2 4 13 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 2 16 1 -1.</_>
        <_>
          4 2 8 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 3 16 8 -1.</_>
        <_>
          4 7 16 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 2 4 -1.</_>
        <_>
          4 6 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 16 2 -1.</_>
        <_>
          4 4 8 1 2.</_>
        <_>
          12 5 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 18 2 -1.</_>
        <_>
          4 4 9 1 2.</_>
        <_>
          13 5 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 10 6 -1.</_>
        <_>
          4 7 10 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 17 6 -1.</_>
        <_>
          4 7 17 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 1 3 -1.</_>
        <_>
          4 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 3 3 -1.</_>
        <_>
          5 6 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 5 17 6 -1.</_>
        <_>
          4 8 17 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 1 3 -1.</_>
        <_>
          4 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 2 3 -1.</_>
        <_>
          4 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 3 3 -1.</_>
        <_>
          4 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 4 9 -1.</_>
        <_>
          4 9 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 2 -1.</_>
        <_>
          4 6 8 1 2.</_>
        <_>
          12 7 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 6 -1.</_>
        <_>
          4 6 8 3 2.</_>
        <_>
          12 9 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 20 18 -1.</_>
        <_>
          14 6 10 18 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 15 6 -1.</_>
        <_>
          4 8 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 4 -1.</_>
        <_>
          4 7 16 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 4 -1.</_>
        <_>
          4 8 16 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 6 6 -1.</_>
        <_>
          6 9 2 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 5 4 -1.</_>
        <_>
          3 8 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 7 5 6 -1.</_>
        <_>
          4 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 6 -1.</_>
        <_>
          4 7 8 3 2.</_>
        <_>
          12 10 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 6 -1.</_>
        <_>
          4 9 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 17 6 -1.</_>
        <_>
          4 9 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 18 2 -1.</_>
        <_>
          4 8 18 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 2 2 -1.</_>
        <_>
          4 8 1 1 2.</_>
        <_>
          5 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 3 3 -1.</_>
        <_>
          5 9 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 2 3 -1.</_>
        <_>
          4 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 4 4 -1.</_>
        <_>
          3 9 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 8 14 2 -1.</_>
        <_>
          4 8 7 1 2.</_>
        <_>
          11 9 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 7 4 -1.</_>
        <_>
          3 9 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 8 16 2 -1.</_>
        <_>
          4 8 8 1 2.</_>
        <_>
          12 9 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 9 4 -1.</_>
        <_>
          4 8 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 8 19 12 -1.</_>
        <_>
          4 12 19 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 1 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 2 -1.</_>
        <_>
          4 9 1 1 2.</_>
        <_>
          5 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 2 -1.</_>
        <_>
          5 9 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 2 -1.</_>
        <_>
          5 10 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 9 4 6 -1.</_>
        <_>
          4 11 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 16 2 -1.</_>
        <_>
          4 9 8 1 2.</_>
        <_>
          12 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 2 -1.</_>
        <_>
          4 10 1 1 2.</_>
        <_>
          5 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 1 -1.</_>
        <_>
          5 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 2 -1.</_>
        <_>
          5 11 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 4 4 -1.</_>
        <_>
          3 11 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 9 4 -1.</_>
        <_>
          4 10 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 10 4 -1.</_>
        <_>
          4 10 10 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 2 -1.</_>
        <_>
          5 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 6 -1.</_>
        <_>
          5 11 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 2 -1.</_>
        <_>
          4 11 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 10 -1.</_>
        <_>
          4 16 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 7 2 -1.</_>
        <_>
          4 11 7 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 9 4 -1.</_>
        <_>
          4 11 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 1 -1.</_>
        <_>
          5 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 3 -1.</_>
        <_>
          5 12 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 2 6 -1.</_>
        <_>
          4 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 16 2 -1.</_>
        <_>
          4 12 8 1 2.</_>
        <_>
          12 13 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 4 4 -1.</_>
        <_>
          6 13 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 10 10 -1.</_>
        <_>
          4 13 5 5 2.</_>
        <_>
          9 18 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 14 4 2 -1.</_>
        <_>
          6 14 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 14 5 4 -1.</_>
        <_>
          3 15 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 15 4 3 -1.</_>
        <_>
          6 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 19 3 4 -1.</_>
        <_>
          5 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 0 14 1 -1.</_>
        <_>
          12 0 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 1 10 8 -1.</_>
        <_>
          5 5 10 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 1 12 10 -1.</_>
        <_>
          5 6 12 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 14 2 -1.</_>
        <_>
          5 3 7 1 2.</_>
        <_>
          12 4 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 7 4 -1.</_>
        <_>
          4 4 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 4 2 2 -1.</_>
        <_>
          5 4 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 4 14 6 -1.</_>
        <_>
          5 4 7 3 2.</_>
        <_>
          12 7 7 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 9 4 -1.</_>
        <_>
          4 5 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 4 18 8 -1.</_>
        <_>
          5 4 9 4 2.</_>
        <_>
          14 8 9 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 1 3 -1.</_>
        <_>
          4 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 2 -1.</_>
        <_>
          5 5 7 1 2.</_>
        <_>
          12 6 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 4 -1.</_>
        <_>
          5 5 7 2 2.</_>
        <_>
          12 7 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 6 -1.</_>
        <_>
          5 7 14 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 9 -1.</_>
        <_>
          5 8 14 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 15 8 -1.</_>
        <_>
          5 8 15 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 16 6 -1.</_>
        <_>
          5 8 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 1 3 -1.</_>
        <_>
          5 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 6 -1.</_>
        <_>
          6 9 1 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 6 7 -1.</_>
        <_>
          7 7 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 3 -1.</_>
        <_>
          5 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 6 -1.</_>
        <_>
          5 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 6 -1.</_>
        <_>
          5 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 5 4 -1.</_>
        <_>
          4 8 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 7 15 6 -1.</_>
        <_>
          5 9 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 1 3 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 2 2 -1.</_>
        <_>
          5 8 1 1 2.</_>
        <_>
          6 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 3 3 -1.</_>
        <_>
          4 9 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 4 4 -1.</_>
        <_>
          5 9 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 4 3 -1.</_>
        <_>
          4 9 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 14 2 -1.</_>
        <_>
          5 8 7 1 2.</_>
        <_>
          12 9 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 13 2 -1.</_>
        <_>
          5 9 13 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 15 4 -1.</_>
        <_>
          5 9 15 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 4 4 -1.</_>
        <_>
          7 9 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 15 2 -1.</_>
        <_>
          10 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 2 -1.</_>
        <_>
          5 9 7 1 2.</_>
        <_>
          12 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 6 -1.</_>
        <_>
          5 9 7 3 2.</_>
        <_>
          12 12 7 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 16 2 -1.</_>
        <_>
          5 9 8 1 2.</_>
        <_>
          13 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 2 2 -1.</_>
        <_>
          5 10 1 1 2.</_>
        <_>
          6 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 2 4 -1.</_>
        <_>
          6 10 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 12 4 -1.</_>
        <_>
          9 10 4 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 17 4 -1.</_>
        <_>
          5 11 17 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 3 -1.</_>
        <_>
          6 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 2 2 -1.</_>
        <_>
          5 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 9 4 -1.</_>
        <_>
          5 11 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 14 3 3 -1.</_>
        <_>
          6 15 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 15 4 2 -1.</_>
        <_>
          6 16 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 15 8 5 -1.</_>
        <_>
          7 15 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 15 5 3 -1.</_>
        <_>
          4 16 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 16 4 3 -1.</_>
        <_>
          5 17 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 16 6 2 -1.</_>
        <_>
          5 16 6 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 17 3 1 -1.</_>
        <_>
          6 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 20 3 1 -1.</_>
        <_>
          6 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 20 3 3 -1.</_>
        <_>
          6 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 20 8 3 -1.</_>
        <_>
          9 20 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 0 10 4 -1.</_>
        <_>
          6 1 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 6 11 -1.</_>
        <_>
          8 1 2 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 12 2 -1.</_>
        <_>
          6 1 6 1 2.</_>
        <_>
          12 2 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 7 8 -1.</_>
        <_>
          6 6 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 3 1 3 -1.</_>
        <_>
          5 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 4 1 -1.</_>
        <_>
          7 4 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 6 1 -1.</_>
        <_>
          8 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 3 4 -1.</_>
        <_>
          6 4 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 5 1 3 -1.</_>
        <_>
          5 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 5 3 2 -1.</_>
        <_>
          7 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 2 9 -1.</_>
        <_>
          6 8 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 12 2 -1.</_>
        <_>
          6 5 6 1 2.</_>
        <_>
          12 6 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 3 3 -1.</_>
        <_>
          5 7 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 6 12 2 -1.</_>
        <_>
          6 6 6 1 2.</_>
        <_>
          12 7 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 15 4 -1.</_>
        <_>
          6 7 15 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 2 -1.</_>
        <_>
          6 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 3 -1.</_>
        <_>
          6 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 12 2 -1.</_>
        <_>
          6 7 6 1 2.</_>
        <_>
          12 8 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 9 12 -1.</_>
        <_>
          6 13 9 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 13 15 -1.</_>
        <_>
          6 12 13 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 3 -1.</_>
        <_>
          6 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 2 -1.</_>
        <_>
          6 8 6 1 2.</_>
        <_>
          12 9 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 12 -1.</_>
        <_>
          6 11 12 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 2 -1.</_>
        <_>
          6 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 9 3 6 -1.</_>
        <_>
          7 10 1 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 3 -1.</_>
        <_>
          6 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 4 -1.</_>
        <_>
          6 10 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 1 -1.</_>
        <_>
          9 9 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          9 9 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 3 -1.</_>
        <_>
          9 9 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          10 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          6 9 6 1 2.</_>
        <_>
          12 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 1 2 -1.</_>
        <_>
          6 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 1 3 -1.</_>
        <_>
          6 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 3 1 -1.</_>
        <_>
          7 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 2 -1.</_>
        <_>
          7 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          7 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          6 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 1 -1.</_>
        <_>
          9 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 9 4 -1.</_>
        <_>
          9 10 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 4 6 -1.</_>
        <_>
          4 12 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 2 -1.</_>
        <_>
          6 10 6 1 2.</_>
        <_>
          12 11 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 3 -1.</_>
        <_>
          6 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 12 6 -1.</_>
        <_>
          9 11 6 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 13 2 -1.</_>
        <_>
          6 12 13 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 14 6 4 -1.</_>
        <_>
          5 15 6 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 15 6 3 -1.</_>
        <_>
          6 15 3 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 15 5 4 -1.</_>
        <_>
          5 16 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 16 3 3 -1.</_>
        <_>
          7 17 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 16 11 8 -1.</_>
        <_>
          6 18 11 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 17 4 2 -1.</_>
        <_>
          7 18 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 19 3 3 -1.</_>
        <_>
          7 19 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 20 3 1 -1.</_>
        <_>
          7 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 20 7 4 -1.</_>
        <_>
          6 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 10 1 -1.</_>
        <_>
          12 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 9 10 -1.</_>
        <_>
          7 5 9 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 9 12 -1.</_>
        <_>
          10 5 3 4 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 1 3 -1.</_>
        <_>
          7 3 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 1 3 -1.</_>
        <_>
          6 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 2 11 10 -1.</_>
        <_>
          7 7 11 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 3 1 3 -1.</_>
        <_>
          6 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 3 1 6 -1.</_>
        <_>
          5 5 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 4 10 2 -1.</_>
        <_>
          7 4 5 1 2.</_>
        <_>
          12 5 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 2 -1.</_>
        <_>
          8 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 2 3 -1.</_>
        <_>
          8 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 3 -1.</_>
        <_>
          8 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 6 -1.</_>
        <_>
          8 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 9 17 -1.</_>
        <_>
          10 5 3 17 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 4 4 -1.</_>
        <_>
          6 6 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 5 5 3 -1.</_>
        <_>
          7 6 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 10 2 -1.</_>
        <_>
          7 6 5 1 2.</_>
        <_>
          12 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 8 4 -1.</_>
        <_>
          6 7 8 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 4 4 -1.</_>
        <_>
          8 8 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 2 4 -1.</_>
        <_>
          7 9 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 5 6 -1.</_>
        <_>
          5 9 5 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 6 4 -1.</_>
        <_>
          6 8 6 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 6 12 -1.</_>
        <_>
          7 13 6 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 1 2.</_>
        <_>
          8 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 4 4 -1.</_>
        <_>
          8 8 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 4 -1.</_>
        <_>
          7 9 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 10 2 -1.</_>
        <_>
          7 8 5 1 2.</_>
        <_>
          12 9 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 1 2 -1.</_>
        <_>
          7 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 6 3 -1.</_>
        <_>
          9 9 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 6 10 -1.</_>
        <_>
          7 9 3 5 2.</_>
        <_>
          10 14 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 12 2 -1.</_>
        <_>
          11 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 10 2 -1.</_>
        <_>
          7 9 5 1 2.</_>
        <_>
          12 10 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 10 4 -1.</_>
        <_>
          7 9 5 2 2.</_>
        <_>
          12 11 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 3 1 -1.</_>
        <_>
          8 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 1 3 -1.</_>
        <_>
          7 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 4 3 -1.</_>
        <_>
          8 10 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 2 3 -1.</_>
        <_>
          7 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 4 4 -1.</_>
        <_>
          9 10 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 6 4 -1.</_>
        <_>
          9 10 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 6 8 -1.</_>
        <_>
          7 10 3 4 2.</_>
        <_>
          10 14 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 12 5 -1.</_>
        <_>
          11 10 4 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 10 2 -1.</_>
        <_>
          7 10 5 1 2.</_>
        <_>
          12 11 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 3 12 -1.</_>
        <_>
          8 11 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 13 9 3 -1.</_>
        <_>
          10 13 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 14 1 3 -1.</_>
        <_>
          7 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 17 3 1 -1.</_>
        <_>
          8 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 17 4 2 -1.</_>
        <_>
          8 18 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 17 5 2 -1.</_>
        <_>
          7 17 5 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 18 3 6 -1.</_>
        <_>
          8 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 3 1 -1.</_>
        <_>
          8 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 20 7 4 -1.</_>
        <_>
          7 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 5 4 -1.</_>
        <_>
          8 2 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 7 10 -1.</_>
        <_>
          8 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 10 -1.</_>
        <_>
          8 0 8 5 2.</_>
        <_>
          16 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 12 -1.</_>
        <_>
          8 0 8 6 2.</_>
        <_>
          16 6 8 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 9 10 -1.</_>
        <_>
          8 5 9 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 1 3 6 -1.</_>
        <_>
          8 1 3 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 1 8 2 -1.</_>
        <_>
          12 1 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 1 16 6 -1.</_>
        <_>
          6 3 16 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 2 8 2 -1.</_>
        <_>
          8 2 4 1 2.</_>
        <_>
          12 3 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 2 6 -1.</_>
        <_>
          6 5 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 4 3 9 -1.</_>
        <_>
          9 4 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 4 3 -1.</_>
        <_>
          8 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 8 2 -1.</_>
        <_>
          8 4 4 1 2.</_>
        <_>
          12 5 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 1 -1.</_>
        <_>
          8 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 2 -1.</_>
        <_>
          9 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 4 3 -1.</_>
        <_>
          9 5 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 4 -1.</_>
        <_>
          9 5 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 5 -1.</_>
        <_>
          9 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 6 -1.</_>
        <_>
          9 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 6 -1.</_>
        <_>
          9 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 7 -1.</_>
        <_>
          9 5 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 7 -1.</_>
        <_>
          9 5 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 4 6 -1.</_>
        <_>
          6 7 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 5 5 3 -1.</_>
        <_>
          7 6 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 2 -1.</_>
        <_>
          8 6 2 1 2.</_>
        <_>
          10 7 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 2 -1.</_>
        <_>
          8 6 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 3 -1.</_>
        <_>
          7 7 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 4 -1.</_>
        <_>
          7 7 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 5 4 -1.</_>
        <_>
          7 7 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 10 2 -1.</_>
        <_>
          8 6 10 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 3 3 -1.</_>
        <_>
          7 8 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 3 4 -1.</_>
        <_>
          7 8 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 4 2 -1.</_>
        <_>
          8 7 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 4 3 -1.</_>
        <_>
          7 8 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 5 2 -1.</_>
        <_>
          8 7 5 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 8 4 -1.</_>
        <_>
          7 8 8 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 8 2 2 -1.</_>
        <_>
          8 8 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 8 2 3 -1.</_>
        <_>
          7 9 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 8 8 5 -1.</_>
        <_>
          10 10 4 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 9 3 3 -1.</_>
        <_>
          9 9 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 10 2 -1.</_>
        <_>
          8 9 5 1 2.</_>
        <_>
          13 10 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 1 -1.</_>
        <_>
          9 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 1 -1.</_>
        <_>
          9 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 2 -1.</_>
        <_>
          9 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 4 8 -1.</_>
        <_>
          8 10 2 4 2.</_>
        <_>
          10 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 8 2 -1.</_>
        <_>
          8 10 4 1 2.</_>
        <_>
          12 11 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 6 5 -1.</_>
        <_>
          10 11 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 4 10 -1.</_>
        <_>
          8 11 2 5 2.</_>
        <_>
          10 16 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 13 7 4 -1.</_>
        <_>
          8 13 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 16 8 3 -1.</_>
        <_>
          10 16 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 16 9 3 -1.</_>
        <_>
          11 16 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 3 7 -1.</_>
        <_>
          9 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 8 2 -1.</_>
        <_>
          10 17 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 9 1 -1.</_>
        <_>
          11 17 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 9 7 -1.</_>
        <_>
          11 17 3 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 8 6 -1.</_>
        <_>
          8 20 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 9 6 -1.</_>
        <_>
          8 19 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 18 3 2 -1.</_>
        <_>
          9 19 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 18 3 6 -1.</_>
        <_>
          9 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 18 8 6 -1.</_>
        <_>
          8 20 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 3 1 -1.</_>
        <_>
          9 20 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 19 8 3 -1.</_>
        <_>
          8 20 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 8 4 -1.</_>
        <_>
          8 21 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 2 2 -1.</_>
        <_>
          9 1 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 1 -1.</_>
        <_>
          12 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 9 -1.</_>
        <_>
          9 0 3 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 2 -1.</_>
        <_>
          9 1 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 8 -1.</_>
        <_>
          9 4 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 7 4 -1.</_>
        <_>
          9 2 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 11 4 -1.</_>
        <_>
          9 1 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 2 3 -1.</_>
        <_>
          10 1 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 2 6 -1.</_>
        <_>
          9 1 1 3 2.</_>
        <_>
          10 4 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 6 2 -1.</_>
        <_>
          9 1 3 1 2.</_>
        <_>
          12 2 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 3 4 -1.</_>
        <_>
          8 2 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 2 2 4 -1.</_>
        <_>
          10 2 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 6 8 -1.</_>
        <_>
          11 2 2 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 1 6 -1.</_>
        <_>
          7 5 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 4 2 9 -1.</_>
        <_>
          10 4 1 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 8 2 -1.</_>
        <_>
          9 4 4 1 2.</_>
        <_>
          13 5 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 2 1 -1.</_>
        <_>
          9 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 4 -1.</_>
        <_>
          10 5 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 9 -1.</_>
        <_>
          10 6 1 9 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 5 4 3 -1.</_>
        <_>
          8 6 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 2 6 -1.</_>
        <_>
          7 8 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 3 3 -1.</_>
        <_>
          9 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 7 1 2 -1.</_>
        <_>
          9 7 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 2 2 -1.</_>
        <_>
          9 7 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 2 2 -1.</_>
        <_>
          9 7 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 2 4 -1.</_>
        <_>
          8 8 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 3 3 -1.</_>
        <_>
          8 8 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 8 6 5 -1.</_>
        <_>
          11 10 2 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 8 3 3 -1.</_>
        <_>
          9 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 2 7 -1.</_>
        <_>
          10 9 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 2 3 -1.</_>
        <_>
          9 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 15 2 -1.</_>
        <_>
          9 10 15 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 4 8 -1.</_>
        <_>
          10 10 2 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 11 4 3 -1.</_>
        <_>
          9 12 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 12 4 8 -1.</_>
        <_>
          9 12 2 4 2.</_>
        <_>
          11 16 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 12 9 4 -1.</_>
        <_>
          12 12 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 4 1 -1.</_>
        <_>
          11 16 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 4 -1.</_>
        <_>
          11 16 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 4 -1.</_>
        <_>
          9 17 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 17 6 2 -1.</_>
        <_>
          11 17 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 18 3 3 -1.</_>
        <_>
          10 19 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 19 3 2 -1.</_>
        <_>
          10 20 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 19 3 5 -1.</_>
        <_>
          10 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 6 4 -1.</_>
        <_>
          9 20 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 3 1 -1.</_>
        <_>
          10 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 20 5 4 -1.</_>
        <_>
          9 21 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 5 4 -1.</_>
        <_>
          9 22 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 6 3 -1.</_>
        <_>
          9 21 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 12 2 -1.</_>
        <_>
          15 20 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 7 3 -1.</_>
        <_>
          9 21 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 7 4 -1.</_>
        <_>
          9 21 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 3 3 -1.</_>
        <_>
          10 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 4 3 -1.</_>
        <_>
          10 21 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 22 3 2 -1.</_>
        <_>
          10 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 1 2 -1.</_>
        <_>
          10 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 2 -1.</_>
        <_>
          10 0 2 1 2.</_>
        <_>
          12 1 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 6 8 -1.</_>
        <_>
          12 2 2 8 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 0 6 10 -1.</_>
        <_>
          10 0 3 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 0 14 10 -1.</_>
        <_>
          10 0 7 5 2.</_>
        <_>
          17 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 1 4 1 -1.</_>
        <_>
          12 1 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 1 9 9 -1.</_>
        <_>
          13 4 3 9 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 1 12 2 -1.</_>
        <_>
          14 1 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 1 14 8 -1.</_>
        <_>
          10 1 7 4 2.</_>
        <_>
          17 5 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 2 1 6 -1.</_>
        <_>
          8 4 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 2 10 10 -1.</_>
        <_>
          10 7 10 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 2 12 -1.</_>
        <_>
          11 3 1 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 9 3 -1.</_>
        <_>
          13 4 3 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 14 2 -1.</_>
        <_>
          17 3 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 3 4 -1.</_>
        <_>
          11 4 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 3 5 -1.</_>
        <_>
          11 4 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 3 6 -1.</_>
        <_>
          11 4 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 4 10 -1.</_>
        <_>
          12 4 2 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 9 3 -1.</_>
        <_>
          13 4 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 2 6 -1.</_>
        <_>
          11 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 6 3 -1.</_>
        <_>
          10 6 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 14 9 -1.</_>
        <_>
          10 8 14 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 3 3 -1.</_>
        <_>
          11 6 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 2 4 -1.</_>
        <_>
          11 6 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 2 3 -1.</_>
        <_>
          10 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 3 3 -1.</_>
        <_>
          10 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 3 4 -1.</_>
        <_>
          10 6 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 6 6 7 -1.</_>
        <_>
          13 6 3 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 4 3 -1.</_>
        <_>
          10 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 2 3 -1.</_>
        <_>
          10 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 6 6 -1.</_>
        <_>
          12 9 2 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 7 3 3 -1.</_>
        <_>
          10 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 8 6 4 -1.</_>
        <_>
          12 10 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 8 6 10 -1.</_>
        <_>
          12 10 2 10 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 9 1 3 -1.</_>
        <_>
          9 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 7 -1.</_>
        <_>
          11 9 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 3 -1.</_>
        <_>
          9 10 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 9 8 6 -1.</_>
        <_>
          12 9 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 4 3 -1.</_>
        <_>
          10 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 1 3 -1.</_>
        <_>
          10 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 4 5 -1.</_>
        <_>
          11 11 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 10 2 3 -1.</_>
        <_>
          10 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 6 7 -1.</_>
        <_>
          12 12 2 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 10 3 3 -1.</_>
        <_>
          9 11 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 15 4 2 -1.</_>
        <_>
          11 15 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 15 6 3 -1.</_>
        <_>
          12 15 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 15 5 3 -1.</_>
        <_>
          10 16 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 18 5 4 -1.</_>
        <_>
          10 19 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 19 4 4 -1.</_>
        <_>
          10 20 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 4 -1.</_>
        <_>
          11 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 4 -1.</_>
        <_>
          10 21 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 7 4 -1.</_>
        <_>
          10 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 3 1 -1.</_>
        <_>
          11 21 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 22 3 1 -1.</_>
        <_>
          11 22 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 22 3 2 -1.</_>
        <_>
          11 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 23 3 1 -1.</_>
        <_>
          11 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 0 12 10 -1.</_>
        <_>
          11 0 6 5 2.</_>
        <_>
          17 5 6 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 3 6 -1.</_>
        <_>
          12 1 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 6 1 -1.</_>
        <_>
          13 1 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 7 10 -1.</_>
        <_>
          11 6 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 3 4 -1.</_>
        <_>
          12 2 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 2 11 -1.</_>
        <_>
          12 2 1 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 4 14 -1.</_>
        <_>
          13 2 2 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 4 22 -1.</_>
        <_>
          13 2 2 22 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 5 -1.</_>
        <_>
          12 3 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 2 3 -1.</_>
        <_>
          11 4 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 6 9 -1.</_>
        <_>
          13 6 2 3 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 3 -1.</_>
        <_>
          11 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 4 3 -1.</_>
        <_>
          11 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 4 8 -1.</_>
        <_>
          11 4 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 5 3 2 -1.</_>
        <_>
          12 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 4 6 -1.</_>
        <_>
          12 5 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 4 5 -1.</_>
        <_>
          12 6 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 2 3 -1.</_>
        <_>
          11 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 4 3 -1.</_>
        <_>
          11 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 4 8 -1.</_>
        <_>
          11 6 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 6 5 3 -1.</_>
        <_>
          11 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 6 3 -1.</_>
        <_>
          11 7 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 1 3 -1.</_>
        <_>
          11 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 2 3 -1.</_>
        <_>
          11 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 6 9 -1.</_>
        <_>
          13 7 2 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 1 3 -1.</_>
        <_>
          11 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 2 3 -1.</_>
        <_>
          11 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 6 9 -1.</_>
        <_>
          13 10 2 9 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 8 3 2 -1.</_>
        <_>
          11 9 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 3 3 -1.</_>
        <_>
          11 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 1 3 -1.</_>
        <_>
          11 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 4 -1.</_>
        <_>
          13 10 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 4 3 -1.</_>
        <_>
          11 11 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 11 1 3 -1.</_>
        <_>
          11 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 12 9 9 -1.</_>
        <_>
          14 15 3 3 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 14 4 2 -1.</_>
        <_>
          13 14 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 16 4 8 -1.</_>
        <_>
          11 18 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 18 12 6 -1.</_>
        <_>
          11 18 6 3 2.</_>
        <_>
          17 21 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 1 -1.</_>
        <_>
          12 20 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 4 -1.</_>
        <_>
          12 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 4 4 -1.</_>
        <_>
          11 22 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 1 -1.</_>
        <_>
          12 21 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 3 -1.</_>
        <_>
          12 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 22 4 2 -1.</_>
        <_>
          12 22 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 12 2 -1.</_>
        <_>
          15 0 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 6 6 -1.</_>
        <_>
          12 0 3 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 0 12 10 -1.</_>
        <_>
          12 0 6 5 2.</_>
        <_>
          18 5 6 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 4 10 -1.</_>
        <_>
          13 1 2 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 2 3 -1.</_>
        <_>
          12 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 8 3 -1.</_>
        <_>
          14 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 9 3 -1.</_>
        <_>
          12 4 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 4 2 3 -1.</_>
        <_>
          12 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 2 2 -1.</_>
        <_>
          13 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 3 2 -1.</_>
        <_>
          13 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 2 5 -1.</_>
        <_>
          13 5 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 4 6 -1.</_>
        <_>
          13 5 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 6 7 -1.</_>
        <_>
          14 5 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 3 8 -1.</_>
        <_>
          12 5 3 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 5 4 3 -1.</_>
        <_>
          12 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 1 4 -1.</_>
        <_>
          12 7 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 1 8 -1.</_>
        <_>
          12 6 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 6 2 10 -1.</_>
        <_>
          13 6 1 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 8 1 -1.</_>
        <_>
          12 6 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 7 1 3 -1.</_>
        <_>
          12 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 2 3 -1.</_>
        <_>
          12 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 4 3 -1.</_>
        <_>
          12 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 1 3 -1.</_>
        <_>
          12 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 2 3 -1.</_>
        <_>
          12 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 6 8 -1.</_>
        <_>
          12 8 3 4 2.</_>
        <_>
          15 12 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 1 3 -1.</_>
        <_>
          12 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 2 3 -1.</_>
        <_>
          12 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 4 10 -1.</_>
        <_>
          12 10 2 5 2.</_>
        <_>
          14 15 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 3 3 -1.</_>
        <_>
          12 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 4 8 -1.</_>
        <_>
          12 11 2 4 2.</_>
        <_>
          14 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 16 4 4 -1.</_>
        <_>
          14 16 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 17 6 6 -1.</_>
        <_>
          12 20 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 18 3 4 -1.</_>
        <_>
          12 19 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 19 3 4 -1.</_>
        <_>
          13 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 21 3 3 -1.</_>
        <_>
          13 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 4 2 -1.</_>
        <_>
          14 2 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 2 6 -1.</_>
        <_>
          13 2 1 3 2.</_>
        <_>
          14 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 2 10 -1.</_>
        <_>
          14 2 1 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 2 14 -1.</_>
        <_>
          14 2 1 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 8 6 -1.</_>
        <_>
          15 2 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 6 7 -1.</_>
        <_>
          15 2 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 4 8 -1.</_>
        <_>
          14 3 2 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 3 7 -1.</_>
        <_>
          14 4 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 8 2 -1.</_>
        <_>
          15 4 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 4 3 -1.</_>
        <_>
          13 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 6 6 -1.</_>
        <_>
          15 5 2 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 6 1 3 -1.</_>
        <_>
          13 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 6 6 6 -1.</_>
        <_>
          15 6 2 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 1 3 -1.</_>
        <_>
          13 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 1 8 -1.</_>
        <_>
          13 7 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 7 9 6 -1.</_>
        <_>
          16 9 3 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 6 2 -1.</_>
        <_>
          13 7 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 8 6 5 -1.</_>
        <_>
          15 8 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 8 6 8 -1.</_>
        <_>
          13 8 3 4 2.</_>
        <_>
          16 12 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 3 3 -1.</_>
        <_>
          14 9 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 2 3 -1.</_>
        <_>
          13 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 3 2 -1.</_>
        <_>
          14 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 11 4 8 -1.</_>
        <_>
          13 11 2 4 2.</_>
        <_>
          15 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 12 8 5 -1.</_>
        <_>
          15 12 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 13 7 3 -1.</_>
        <_>
          13 14 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 14 4 1 -1.</_>
        <_>
          14 14 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 17 3 7 -1.</_>
        <_>
          14 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 21 3 3 -1.</_>
        <_>
          14 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 22 3 2 -1.</_>
        <_>
          14 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 6 -1.</_>
        <_>
          14 0 5 3 2.</_>
        <_>
          19 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 10 -1.</_>
        <_>
          14 0 5 5 2.</_>
        <_>
          19 5 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 9 -1.</_>
        <_>
          11 3 10 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 1 1 8 -1.</_>
        <_>
          14 5 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 10 2 -1.</_>
        <_>
          19 2 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 3 2 8 -1.</_>
        <_>
          15 3 1 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 3 -1.</_>
        <_>
          15 4 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 4 -1.</_>
        <_>
          15 4 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 8 3 -1.</_>
        <_>
          14 5 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 3 -1.</_>
        <_>
          15 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 5 -1.</_>
        <_>
          15 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 3 5 -1.</_>
        <_>
          15 6 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 6 17 -1.</_>
        <_>
          16 6 2 17 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 8 2 -1.</_>
        <_>
          14 6 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 7 4 3 -1.</_>
        <_>
          15 7 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 7 6 6 -1.</_>
        <_>
          16 9 2 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 8 6 3 -1.</_>
        <_>
          16 9 2 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 8 6 1 -1.</_>
        <_>
          14 8 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 8 6 2 -1.</_>
        <_>
          14 8 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 9 4 1 -1.</_>
        <_>
          16 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 6 6 -1.</_>
        <_>
          12 11 6 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 1 -1.</_>
        <_>
          15 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          15 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 6 -1.</_>
        <_>
          14 10 1 3 2.</_>
        <_>
          15 13 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 15 1 2 -1.</_>
        <_>
          14 15 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 16 8 8 -1.</_>
        <_>
          14 16 4 4 2.</_>
        <_>
          18 20 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 17 9 3 -1.</_>
        <_>
          14 18 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 20 3 4 -1.</_>
        <_>
          15 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 0 3 1 -1.</_>
        <_>
          16 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 0 4 13 -1.</_>
        <_>
          16 1 2 13 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 1 3 4 -1.</_>
        <_>
          15 3 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 3 2 2 -1.</_>
        <_>
          15 3 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 2 -1.</_>
        <_>
          16 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 3 -1.</_>
        <_>
          16 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 2 7 -1.</_>
        <_>
          16 5 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 4 2 -1.</_>
        <_>
          15 5 2 1 2.</_>
        <_>
          17 6 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 5 6 -1.</_>
        <_>
          15 7 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 6 3 2 -1.</_>
        <_>
          16 7 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 6 3 5 -1.</_>
        <_>
          16 6 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 5 6 -1.</_>
        <_>
          15 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 2 2 -1.</_>
        <_>
          15 8 1 1 2.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 3 16 -1.</_>
        <_>
          15 12 3 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 4 4 -1.</_>
        <_>
          15 9 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 3 -1.</_>
        <_>
          16 10 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 9 1 10 -1.</_>
        <_>
          15 14 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 2 1 -1.</_>
        <_>
          16 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 5 -1.</_>
        <_>
          16 10 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 4 4 -1.</_>
        <_>
          14 11 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 11 7 4 -1.</_>
        <_>
          14 12 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 12 6 6 -1.</_>
        <_>
          17 14 2 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 13 3 4 -1.</_>
        <_>
          16 14 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 13 3 11 -1.</_>
        <_>
          16 13 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 13 2 3 -1.</_>
        <_>
          15 14 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 14 1 8 -1.</_>
        <_>
          15 16 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 14 8 10 -1.</_>
        <_>
          15 14 4 5 2.</_>
        <_>
          19 19 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 15 4 3 -1.</_>
        <_>
          15 16 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 16 8 2 -1.</_>
        <_>
          19 16 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 16 6 3 -1.</_>
        <_>
          15 17 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 1 -1.</_>
        <_>
          20 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 2 -1.</_>
        <_>
          20 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 6 -1.</_>
        <_>
          16 0 4 3 2.</_>
        <_>
          20 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 8 -1.</_>
        <_>
          16 0 4 4 2.</_>
        <_>
          20 4 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 16 -1.</_>
        <_>
          16 0 4 8 2.</_>
        <_>
          20 8 4 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 1 3 3 -1.</_>
        <_>
          17 2 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 1 6 2 -1.</_>
        <_>
          18 3 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 2 6 1 -1.</_>
        <_>
          18 4 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 2 4 3 -1.</_>
        <_>
          16 3 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 2 4 8 -1.</_>
        <_>
          14 4 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 6 3 3 -1.</_>
        <_>
          17 7 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 6 4 4 -1.</_>
        <_>
          18 6 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 3 -1.</_>
        <_>
          16 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 4 -1.</_>
        <_>
          16 8 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 2 -1.</_>
        <_>
          16 7 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 10 -1.</_>
        <_>
          16 7 2 5 2.</_>
        <_>
          18 12 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 2 -1.</_>
        <_>
          16 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 3 -1.</_>
        <_>
          16 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 6 -1.</_>
        <_>
          16 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 6 -1.</_>
        <_>
          16 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 6 4 -1.</_>
        <_>
          16 8 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 7 4 -1.</_>
        <_>
          16 8 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 1 2 -1.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 1 4 -1.</_>
        <_>
          16 9 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 3 3 -1.</_>
        <_>
          17 9 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 3 1 -1.</_>
        <_>
          17 9 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 8 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 4 -1.</_>
        <_>
          16 9 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 3 4 -1.</_>
        <_>
          16 9 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 3 16 -1.</_>
        <_>
          16 12 3 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 8 2 -1.</_>
        <_>
          16 8 4 1 2.</_>
        <_>
          20 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 1 3 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 3 6 -1.</_>
        <_>
          14 11 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 5 6 -1.</_>
        <_>
          14 11 5 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 6 6 -1.</_>
        <_>
          14 11 6 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 8 6 -1.</_>
        <_>
          14 11 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 10 1 2 -1.</_>
        <_>
          16 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 1 3 -1.</_>
        <_>
          16 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 3 -1.</_>
        <_>
          16 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 3 3 -1.</_>
        <_>
          16 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 5 2 -1.</_>
        <_>
          16 11 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 6 8 -1.</_>
        <_>
          16 12 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 2 -1.</_>
        <_>
          17 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 2 -1.</_>
        <_>
          16 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 2 -1.</_>
        <_>
          16 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 14 8 10 -1.</_>
        <_>
          16 14 4 5 2.</_>
        <_>
          20 19 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 16 8 2 -1.</_>
        <_>
          20 16 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 18 3 6 -1.</_>
        <_>
          17 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 18 8 3 -1.</_>
        <_>
          20 18 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 19 2 3 -1.</_>
        <_>
          15 20 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 20 1 3 -1.</_>
        <_>
          15 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 1 6 2 -1.</_>
        <_>
          17 1 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 1 -1.</_>
        <_>
          18 3 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 2 3 -1.</_>
        <_>
          17 4 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 6 3 -1.</_>
        <_>
          19 5 2 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 3 -1.</_>
        <_>
          17 4 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 7 3 -1.</_>
        <_>
          17 4 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 4 2 -1.</_>
        <_>
          18 5 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 4 2 6 -1.</_>
        <_>
          17 4 1 3 2.</_>
        <_>
          18 7 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 6 5 -1.</_>
        <_>
          19 6 2 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 4 7 16 -1.</_>
        <_>
          17 8 7 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 5 1 2 -1.</_>
        <_>
          17 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 5 3 4 -1.</_>
        <_>
          18 6 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 1 9 -1.</_>
        <_>
          17 9 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 3 4 -1.</_>
        <_>
          18 7 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 6 3 -1.</_>
        <_>
          19 8 2 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 3 3 -1.</_>
        <_>
          17 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 3 -1.</_>
        <_>
          17 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 6 -1.</_>
        <_>
          17 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 3 -1.</_>
        <_>
          18 8 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 4 5 -1.</_>
        <_>
          18 8 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 4 9 -1.</_>
        <_>
          18 8 2 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 2 -1.</_>
        <_>
          17 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          17 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 3 -1.</_>
        <_>
          17 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          17 7 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 5 2 -1.</_>
        <_>
          17 8 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 5 4 -1.</_>
        <_>
          17 8 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 2 2 -1.</_>
        <_>
          17 8 1 1 2.</_>
        <_>
          18 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 3 3 -1.</_>
        <_>
          18 9 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 6 9 -1.</_>
        <_>
          20 8 3 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 7 8 -1.</_>
        <_>
          15 10 7 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 9 3 3 -1.</_>
        <_>
          18 10 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 1 3 -1.</_>
        <_>
          17 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 1 -1.</_>
        <_>
          19 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 4 -1.</_>
        <_>
          17 11 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 2 -1.</_>
        <_>
          17 10 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 4 -1.</_>
        <_>
          17 11 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 8 -1.</_>
        <_>
          17 12 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 2 2 -1.</_>
        <_>
          18 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 2 -1.</_>
        <_>
          18 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 3 -1.</_>
        <_>
          18 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 12 3 1 -1.</_>
        <_>
          18 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 14 1 3 -1.</_>
        <_>
          17 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 14 2 2 -1.</_>
        <_>
          17 14 1 1 2.</_>
        <_>
          18 15 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 14 4 4 -1.</_>
        <_>
          19 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 4 2 -1.</_>
        <_>
          19 15 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 17 2 4 -1.</_>
        <_>
          17 17 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 4 1 -1.</_>
        <_>
          20 0 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 3 3 1 -1.</_>
        <_>
          19 3 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 4 6 5 -1.</_>
        <_>
          20 6 2 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 4 6 19 -1.</_>
        <_>
          21 4 3 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 5 1 2 -1.</_>
        <_>
          18 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 5 4 9 -1.</_>
        <_>
          19 6 2 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 4 -1.</_>
        <_>
          18 6 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 3 4 -1.</_>
        <_>
          19 7 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 4 -1.</_>
        <_>
          19 7 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 5 -1.</_>
        <_>
          19 7 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 3 -1.</_>
        <_>
          17 7 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 3 3 -1.</_>
        <_>
          18 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 3 -1.</_>
        <_>
          18 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 1 3 -1.</_>
        <_>
          18 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 4 -1.</_>
        <_>
          19 8 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 5 -1.</_>
        <_>
          19 8 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 6 -1.</_>
        <_>
          19 8 1 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 7 2 3 -1.</_>
        <_>
          18 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 2 2 -1.</_>
        <_>
          18 7 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 3 -1.</_>
        <_>
          18 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 2 2 -1.</_>
        <_>
          18 8 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 8 3 9 -1.</_>
        <_>
          15 11 3 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 8 6 5 -1.</_>
        <_>
          21 8 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 1 -1.</_>
        <_>
          19 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 6 2 -1.</_>
        <_>
          18 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 6 3 -1.</_>
        <_>
          18 10 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 1 3 -1.</_>
        <_>
          18 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 2 -1.</_>
        <_>
          18 10 1 1 2.</_>
        <_>
          19 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 3 5 -1.</_>
        <_>
          19 10 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 4 8 -1.</_>
        <_>
          18 15 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 2 6 -1.</_>
        <_>
          18 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 2 8 -1.</_>
        <_>
          18 16 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 6 2 -1.</_>
        <_>
          21 12 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 6 8 -1.</_>
        <_>
          18 12 3 4 2.</_>
        <_>
          21 16 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 14 2 4 -1.</_>
        <_>
          19 14 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 15 1 3 -1.</_>
        <_>
          18 16 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 15 4 5 -1.</_>
        <_>
          18 15 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 15 3 6 -1.</_>
        <_>
          16 17 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 17 2 4 -1.</_>
        <_>
          18 17 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 17 2 5 -1.</_>
        <_>
          18 17 1 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 18 1 3 -1.</_>
        <_>
          17 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 20 1 3 -1.</_>
        <_>
          17 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 0 3 1 -1.</_>
        <_>
          20 0 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 2 3 1 -1.</_>
        <_>
          20 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 3 3 1 -1.</_>
        <_>
          20 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 4 2 2 -1.</_>
        <_>
          19 4 1 1 2.</_>
        <_>
          20 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 4 -1.</_>
        <_>
          19 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 2 3 -1.</_>
        <_>
          19 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 5 3 1 -1.</_>
        <_>
          20 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 6 1 3 -1.</_>
        <_>
          19 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 1 6 -1.</_>
        <_>
          19 6 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 6 3 3 -1.</_>
        <_>
          19 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 8 3 3 -1.</_>
        <_>
          20 9 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 8 4 11 -1.</_>
        <_>
          21 8 2 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 9 2 1 -1.</_>
        <_>
          20 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 10 2 4 -1.</_>
        <_>
          19 12 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 11 3 13 -1.</_>
        <_>
          20 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 12 4 8 -1.</_>
        <_>
          19 12 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 13 3 8 -1.</_>
        <_>
          20 13 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 13 4 7 -1.</_>
        <_>
          19 13 2 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 15 4 5 -1.</_>
        <_>
          20 16 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 17 3 6 -1.</_>
        <_>
          20 17 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 17 3 7 -1.</_>
        <_>
          20 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 17 3 4 -1.</_>
        <_>
          18 18 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 18 1 3 -1.</_>
        <_>
          18 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 19 1 3 -1.</_>
        <_>
          18 20 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 0 2 2 -1.</_>
        <_>
          20 0 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 3 1 4 -1.</_>
        <_>
          19 4 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 4 3 1 -1.</_>
        <_>
          21 5 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 4 3 2 -1.</_>
        <_>
          21 5 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 4 2 3 -1.</_>
        <_>
          20 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 3 1 -1.</_>
        <_>
          21 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 8 1 4 -1.</_>
        <_>
          20 8 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 8 4 3 -1.</_>
        <_>
          21 9 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 8 3 4 -1.</_>
        <_>
          21 9 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 8 4 4 -1.</_>
        <_>
          21 9 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 8 4 6 -1.</_>
        <_>
          20 10 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 9 4 10 -1.</_>
        <_>
          20 9 2 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 9 4 3 -1.</_>
        <_>
          19 10 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 10 1 3 -1.</_>
        <_>
          19 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 10 4 3 -1.</_>
        <_>
          21 11 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 10 3 10 -1.</_>
        <_>
          21 10 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 12 4 8 -1.</_>
        <_>
          21 13 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 13 3 8 -1.</_>
        <_>
          21 13 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 15 4 5 -1.</_>
        <_>
          21 16 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 15 4 4 -1.</_>
        <_>
          22 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 17 3 5 -1.</_>
        <_>
          21 17 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 18 3 1 -1.</_>
        <_>
          21 18 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 18 1 3 -1.</_>
        <_>
          19 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 19 3 4 -1.</_>
        <_>
          21 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 20 1 3 -1.</_>
        <_>
          19 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 2 3 16 -1.</_>
        <_>
          21 2 3 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 3 3 2 -1.</_>
        <_>
          22 4 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 8 3 3 -1.</_>
        <_>
          22 9 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 8 3 5 -1.</_>
        <_>
          22 9 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 9 3 3 -1.</_>
        <_>
          20 10 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 10 1 3 -1.</_>
        <_>
          20 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 10 2 5 -1.</_>
        <_>
          21 10 1 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 10 3 3 -1.</_>
        <_>
          21 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 11 1 3 -1.</_>
        <_>
          20 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 15 1 3 -1.</_>
        <_>
          20 16 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 15 3 6 -1.</_>
        <_>
          22 15 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 1 3 -1.</_>
        <_>
          20 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 7 -1.</_>
        <_>
          22 16 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 8 -1.</_>
        <_>
          22 16 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 19 3 5 -1.</_>
        <_>
          22 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 22 3 2 -1.</_>
        <_>
          22 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 2 1 18 -1.</_>
        <_>
          22 2 1 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 10 1 3 -1.</_>
        <_>
          21 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 10 2 3 -1.</_>
        <_>
          21 11 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 11 1 3 -1.</_>
        <_>
          21 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 11 2 6 -1.</_>
        <_>
          22 11 1 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 11 2 9 -1.</_>
        <_>
          22 11 1 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 12 2 3 -1.</_>
        <_>
          21 13 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 13 2 9 -1.</_>
        <_>
          22 13 1 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 13 2 8 -1.</_>
        <_>
          20 15 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 17 1 4 -1.</_>
        <_>
          21 18 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 18 1 4 -1.</_>
        <_>
          21 19 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          23 9 1 3 -1.</_>
        <_>
          23 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 10 1 3 -1.</_>
        <_>
          23 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 11 1 3 -1.</_>
        <_>
          22 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          23 12 1 3 -1.</_>
        <_>
          22 13 1 1 3.</_></rects>
      <tilted>1</tilted></_></features></cascade>
</opencv_storage>
