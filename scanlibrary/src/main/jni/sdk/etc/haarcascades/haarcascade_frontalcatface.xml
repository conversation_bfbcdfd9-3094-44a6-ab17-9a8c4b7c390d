<?xml version="1.0"?>
<!----------------------------------------------------------------------------
 A frontal cat face detector using the basic set of Haar features, i.e.
 horizontal and vertical features but not diagonal features.

 Contributed by <PERSON> (<EMAIL>).

 More information can be found in the following publications and
 presentations:

 <PERSON>. OpenCV for Secret Agents (book). Packt Publishing, January
   2015.
 <PERSON>. "Training Detectors and Recognizers in Python and OpenCV"
   (tutorial). ISMAR 2014. September 9, 2014.
   http://nummist.com/opencv/Howse_ISMAR_20140909.pdf
 <PERSON>. "Training Intelligent Camera Systems with Python and OpenCV"
   (webcast). O’Reilly Media. June 17, 2014.
   http://www.oreilly.com/pub/e/3077

 Build scripts and demo applications can be found in the following repository:
 https://bitbucket.org/<PERSON>_<PERSON><PERSON>/angora-blue

 KNOWN LIMITATIONS:

 Sometimes, the detector mistakenly thinks that a human face is a cat face. In
 situations where either a human or a cat might be encountered, use both a
 human face detector and a cat face detector. Then, if a detected human face
 and a detected cat face intersect, reject the cat face.

 An upright subject is assumed. In situations where the cat's face might be
 sideways or upside down (e.g. the cat is rolling over), try various rotations
 of the input image.

 //////////////////////////////////////////////////////////////////////////
 | Contributors License Agreement
 | IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
 |   By downloading, copying, installing or using the software you agree
 |   to this license.
 |   If you do not agree to this license, do not download, install,
 |   copy or use the software.
 |
 | Copyright (c) 2014, Joseph Howse (Nummist Media Corporation Limited,
 | Halifax, Nova Scotia, Canada). All rights reserved.
 |
 | Redistribution and use in source and binary forms, with or without
 | modification, are permitted provided that the following conditions are
 | met:
 |
 |    * Redistributions of source code must retain the above copyright
 |       notice, this list of conditions and the following disclaimer.
 |    * Redistributions in binary form must reproduce the above
 |      copyright notice, this list of conditions and the following
 |      disclaimer in the documentation and/or other materials provided
 |      with the distribution.
 |    * The name of Contributor may not used to endorse or promote products
 |      derived from this software without specific prior written permission.
 |
 | THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 | "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 | LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 | A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 | CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 | EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 | PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 | PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 | LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 | NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 | SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  Back to
 | Top
 //////////////////////////////////////////////////////////////////////////
 ---------------------------------------------------------------------------->
<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>24</height>
  <width>24</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9900001287460327e-01</minHitRate>
    <maxFalseAlarm>5.0000000000000000e-01</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-01</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount>
    <featSize>1</featSize>
    <mode>BASIC</mode></featureParams>
  <stageNum>15</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>25</maxWeakCount>
      <stageThreshold>-1.7760953903198242e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 445 -1.4772760681807995e-02</internalNodes>
          <leafValues>
            8.4035199880599976e-01 -1.2701500952243805e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 792 4.5831585302948952e-03</internalNodes>
          <leafValues>
            -2.3791725933551788e-01 6.1978793144226074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 -1.5044892206788063e-02</internalNodes>
          <leafValues>
            5.7160794734954834e-01 -2.0493283867835999e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 -1.2275323271751404e-02</internalNodes>
          <leafValues>
            5.7243120670318604e-01 -1.9821420311927795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 8.3929381798952818e-04</internalNodes>
          <leafValues>
            -2.7865329384803772e-01 4.3508478999137878e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 1.5758406370878220e-02</internalNodes>
          <leafValues>
            -2.2696593403816223e-01 4.9478942155838013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 5.0025805830955505e-02</internalNodes>
          <leafValues>
            -1.5532729029655457e-01 6.8103748559951782e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 727 1.3194808736443520e-02</internalNodes>
          <leafValues>
            -1.7704419791698456e-01 5.8701574802398682e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 2.8080096468329430e-02</internalNodes>
          <leafValues>
            -1.6797901690006256e-01 5.7502186298370361e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 704 2.1941341459751129e-02</internalNodes>
          <leafValues>
            -1.4689344167709351e-01 5.2395468950271606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 -4.7663945704698563e-02</internalNodes>
          <leafValues>
            6.5710687637329102e-01 -1.1819842457771301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 3.5373851656913757e-02</internalNodes>
          <leafValues>
            -9.7323395311832428e-02 7.4618083238601685e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 1.4257467410061508e-04</internalNodes>
          <leafValues>
            -2.8882622718811035e-01 2.8288537263870239e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 3.8169793784618378e-02</internalNodes>
          <leafValues>
            -1.4375060796737671e-01 4.9739924073219299e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 6.5595638006925583e-03</internalNodes>
          <leafValues>
            1.2217019498348236e-01 -6.4208990335464478e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 753 -7.5260535813868046e-03</internalNodes>
          <leafValues>
            -7.7927684783935547e-01 8.4750138223171234e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 1.1342750862240791e-02</internalNodes>
          <leafValues>
            -2.2101284563541412e-01 3.4454554319381714e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 -1.8045576289296150e-03</internalNodes>
          <leafValues>
            -7.8645682334899902e-01 9.7392335534095764e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 -6.6228499636054039e-03</internalNodes>
          <leafValues>
            3.1783181428909302e-01 -2.0665900409221649e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 1.0336233302950859e-02</internalNodes>
          <leafValues>
            9.1840714216232300e-02 -7.7336055040359497e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 552 -2.4216776713728905e-02</internalNodes>
          <leafValues>
            3.6742198467254639e-01 -1.9339990615844727e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 1.4514809474349022e-02</internalNodes>
          <leafValues>
            -1.3652153313159943e-01 4.2311781644821167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 2.1728668361902237e-03</internalNodes>
          <leafValues>
            7.9096116125583649e-02 -7.6427251100540161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -3.9529249072074890e-02</internalNodes>
          <leafValues>
            5.7755863666534424e-01 -1.0801278799772263e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 640 1.5566367655992508e-02</internalNodes>
          <leafValues>
            -1.0259374976158142e-01 4.9773094058036804e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>34</maxWeakCount>
      <stageThreshold>-1.6417213678359985e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 699 8.2648023962974548e-03</internalNodes>
          <leafValues>
            -1.0913594812154770e-01 7.7392864227294922e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 1.4094283804297447e-02</internalNodes>
          <leafValues>
            -1.4461971819400787e-01 7.1108609437942505e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 6.3192001543939114e-03</internalNodes>
          <leafValues>
            -2.3035119473934174e-01 4.4546401500701904e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 2.0388111472129822e-02</internalNodes>
          <leafValues>
            -2.6455581188201904e-01 3.6559283733367920e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 1.4826779253780842e-02</internalNodes>
          <leafValues>
            -2.0881411433219910e-01 4.6142515540122986e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 821 2.1515905857086182e-02</internalNodes>
          <leafValues>
            -1.4785310626029968e-01 6.4220702648162842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 919 1.4720780309289694e-03</internalNodes>
          <leafValues>
            -2.2546350955963135e-01 3.6676284670829773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 -2.6044776663184166e-02</internalNodes>
          <leafValues>
            4.1465434432029724e-01 -1.5356495976448059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 -1.0589402168989182e-02</internalNodes>
          <leafValues>
            4.1222676634788513e-01 -1.6899079084396362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 6.9683273322880268e-03</internalNodes>
          <leafValues>
            1.0854535549879074e-01 -7.1554762125015259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 1.1014346033334732e-02</internalNodes>
          <leafValues>
            -1.7490877211093903e-01 4.4723153114318848e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 -1.1037060990929604e-02</internalNodes>
          <leafValues>
            4.4173675775527954e-01 -1.3834878802299500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 6.2014400959014893e-02</internalNodes>
          <leafValues>
            -1.1345938593149185e-01 6.1101204156875610e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 6.0412874445319176e-03</internalNodes>
          <leafValues>
            1.0154686868190765e-01 -7.2787815332412720e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 -5.8582504279911518e-03</internalNodes>
          <leafValues>
            -6.0382944345474243e-01 8.4109157323837280e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 885 7.3200068436563015e-04</internalNodes>
          <leafValues>
            -1.6661356389522552e-01 3.7944686412811279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 1.9083185121417046e-02</internalNodes>
          <leafValues>
            -1.2449446320533752e-01 4.2328220605850220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 978 6.8236114457249641e-03</internalNodes>
          <leafValues>
            1.0009460896253586e-01 -5.7412278652191162e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 655 -8.0357380211353302e-03</internalNodes>
          <leafValues>
            3.5114383697509766e-01 -1.5984705090522766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 5.3236715495586395e-02</internalNodes>
          <leafValues>
            -1.1999151855707169e-01 4.5052844285964966e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 -2.5083746761083603e-02</internalNodes>
          <leafValues>
            4.2925071716308594e-01 -1.3847301900386810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 -7.7097099274396896e-03</internalNodes>
          <leafValues>
            4.2245966196060181e-01 -1.4710718393325806e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 1.1426717042922974e-02</internalNodes>
          <leafValues>
            7.3091737926006317e-02 -7.6901757717132568e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 980 -4.7868257388472557e-03</internalNodes>
          <leafValues>
            -5.4498624801635742e-01 8.9874587953090668e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 9.9204876460134983e-04</internalNodes>
          <leafValues>
            -1.5133780241012573e-01 3.5266080498695374e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 -7.3156114667654037e-03</internalNodes>
          <leafValues>
            -7.7262216806411743e-01 7.4858717620372772e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 5.6915655732154846e-03</internalNodes>
          <leafValues>
            5.7124871760606766e-02 -6.7257982492446899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 854 -9.5818340778350830e-03</internalNodes>
          <leafValues>
            3.5280853509902954e-01 -1.5295246243476868e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 2.3029895499348640e-02</internalNodes>
          <leafValues>
            8.0654025077819824e-02 -6.6331261396408081e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 8.3062853664159775e-03</internalNodes>
          <leafValues>
            -1.8261365592479706e-01 2.9241952300071716e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 1.2940693646669388e-02</internalNodes>
          <leafValues>
            6.2763690948486328e-02 -7.9858863353729248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 855 4.5793736353516579e-03</internalNodes>
          <leafValues>
            -1.6761651635169983e-01 2.8498283028602600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 3.1311963684856892e-03</internalNodes>
          <leafValues>
            6.4963281154632568e-02 -7.1869355440139771e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 6.2793528195470572e-04</internalNodes>
          <leafValues>
            -1.4610730111598969e-01 3.1872764229774475e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>47</maxWeakCount>
      <stageThreshold>-1.8195210695266724e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 389 2.8032548725605011e-02</internalNodes>
          <leafValues>
            3.9794921875000000e-02 7.6527571678161621e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 512 -1.3777698390185833e-02</internalNodes>
          <leafValues>
            4.8011425137519836e-01 -2.0891545712947845e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 -1.5191626735031605e-03</internalNodes>
          <leafValues>
            4.3561527132987976e-01 -1.9825626909732819e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 5.4982006549835205e-02</internalNodes>
          <leafValues>
            -1.9595552980899811e-01 5.1033991575241089e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -3.8968096487224102e-03</internalNodes>
          <leafValues>
            3.6698764562606812e-01 -2.1704223752021790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 -1.0689822956919670e-02</internalNodes>
          <leafValues>
            5.7506144046783447e-01 -1.2323237210512161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 -3.6211537662893534e-03</internalNodes>
          <leafValues>
            3.9189809560775757e-01 -2.0496053993701935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 -5.6232484057545662e-03</internalNodes>
          <leafValues>
            -7.0911335945129395e-01 8.9780956506729126e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 7.6950237154960632e-02</internalNodes>
          <leafValues>
            -1.6086885333061218e-01 3.9858064055442810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 -9.4812046736478806e-03</internalNodes>
          <leafValues>
            4.4439849257469177e-01 -1.4388205111026764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 637 4.8636407591402531e-03</internalNodes>
          <leafValues>
            8.3068624138832092e-02 -7.4362516403198242e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 -2.9443078674376011e-03</internalNodes>
          <leafValues>
            -6.1576569080352783e-01 8.1917040050029755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 573 -2.0938962697982788e-03</internalNodes>
          <leafValues>
            -5.7727062702178955e-01 9.1147974133491516e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 2.7704227250069380e-03</internalNodes>
          <leafValues>
            -1.9285553693771362e-01 3.1007087230682373e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 1.2979362159967422e-02</internalNodes>
          <leafValues>
            -1.4128974080085754e-01 3.8875201344490051e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -1.1832339689135551e-02</internalNodes>
          <leafValues>
            -7.8204095363616943e-01 7.2949714958667755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 3.3956445753574371e-02</internalNodes>
          <leafValues>
            -1.3892538845539093e-01 3.9772579073905945e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 4.9063982442021370e-03</internalNodes>
          <leafValues>
            8.1131778657436371e-02 -7.2757917642593384e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 -2.3896694183349609e-03</internalNodes>
          <leafValues>
            -6.3438212871551514e-01 7.6958425343036652e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 2.7626976370811462e-02</internalNodes>
          <leafValues>
            -1.7219249904155731e-01 3.7336668372154236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 539 3.9899977855384350e-03</internalNodes>
          <leafValues>
            7.0482976734638214e-02 -6.6334074735641479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -1.3984958641231060e-03</internalNodes>
          <leafValues>
            4.2500507831573486e-01 -1.2214981764554977e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 1.3496846659108996e-03</internalNodes>
          <leafValues>
            -1.1209741979837418e-01 4.0830954909324646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 709 -3.9141997694969177e-03</internalNodes>
          <leafValues>
            -5.3290998935699463e-01 9.4373866915702820e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 4.9622595310211182e-02</internalNodes>
          <leafValues>
            -9.4219848513603210e-02 4.9830704927444458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 -6.2201134860515594e-02</internalNodes>
          <leafValues>
            5.1749163866043091e-01 -9.4065755605697632e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -9.0873138979077339e-03</internalNodes>
          <leafValues>
            -6.6816878318786621e-01 7.1877375245094299e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 7.8745372593402863e-03</internalNodes>
          <leafValues>
            6.2571905553340912e-02 -6.6562908887863159e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 6.6907280124723911e-03</internalNodes>
          <leafValues>
            -1.0588129609823227e-01 4.0728682279586792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 8.9093092828989029e-03</internalNodes>
          <leafValues>
            -1.3461567461490631e-01 3.1837779283523560e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 908 3.9681773632764816e-03</internalNodes>
          <leafValues>
            7.1942776441574097e-02 -6.0182863473892212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 547 1.1530296877026558e-02</internalNodes>
          <leafValues>
            -1.2775546312332153e-01 3.0782708525657654e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 497 -3.0327122658491135e-02</internalNodes>
          <leafValues>
            3.6777910590171814e-01 -1.4219322800636292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 -8.5609289817512035e-04</internalNodes>
          <leafValues>
            2.3868902027606964e-01 -1.7458973824977875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 -8.7312739342451096e-03</internalNodes>
          <leafValues>
            -5.7229250669479370e-01 6.7568361759185791e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 3.6238119006156921e-02</internalNodes>
          <leafValues>
            -9.3856818974018097e-02 4.3806123733520508e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 859 -1.7243899405002594e-02</internalNodes>
          <leafValues>
            3.2672104239463806e-01 -1.2465524673461914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 571 3.0146117787808180e-03</internalNodes>
          <leafValues>
            8.4438301622867584e-02 -4.7965818643569946e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -6.5812864340841770e-03</internalNodes>
          <leafValues>
            -6.5075701475143433e-01 5.2506592124700546e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 -2.6519154198467731e-03</internalNodes>
          <leafValues>
            3.9998021721839905e-01 -1.2199153006076813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 2.2568926215171814e-03</internalNodes>
          <leafValues>
            -1.1875165253877640e-01 3.6033150553703308e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 7.8723561018705368e-03</internalNodes>
          <leafValues>
            6.2930762767791748e-02 -7.2076427936553955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 826 5.7557085528969765e-04</internalNodes>
          <leafValues>
            -1.9064085185527802e-01 1.9888252019882202e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 -4.9874491989612579e-02</internalNodes>
          <leafValues>
            3.3844900131225586e-01 -1.0514739900827408e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 934 -5.1496019586920738e-03</internalNodes>
          <leafValues>
            2.3594251275062561e-01 -1.7097522318363190e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 -4.2706914246082306e-03</internalNodes>
          <leafValues>
            3.9194715023040771e-01 -9.3349114060401917e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 493 -1.0737263411283493e-01</internalNodes>
          <leafValues>
            5.6014204025268555e-01 -6.5082974731922150e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>54</maxWeakCount>
      <stageThreshold>-1.6577893495559692e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 508 -9.3684419989585876e-03</internalNodes>
          <leafValues>
            7.2173911333084106e-01 -1.8106427043676376e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 1.2989062815904617e-03</internalNodes>
          <leafValues>
            -2.0532198250293732e-01 4.2473095655441284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 3.6645986139774323e-02</internalNodes>
          <leafValues>
            -1.6229844093322754e-01 5.1024991273880005e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 7.4745947495102882e-03</internalNodes>
          <leafValues>
            -2.5622242689132690e-01 3.1706622242927551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 -6.4315330237150192e-03</internalNodes>
          <leafValues>
            3.1292220950126648e-01 -2.2839428484439850e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 3.9290782064199448e-02</internalNodes>
          <leafValues>
            -1.3426035642623901e-01 5.8237910270690918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 459 7.8858569264411926e-02</internalNodes>
          <leafValues>
            -1.5872669219970703e-01 4.5478171110153198e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 -2.8797138482332230e-02</internalNodes>
          <leafValues>
            5.3397864103317261e-01 -1.1107259988784790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 -1.0816241614520550e-02</internalNodes>
          <leafValues>
            4.7242766618728638e-01 -9.8347179591655731e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 2.8447926044464111e-02</internalNodes>
          <leafValues>
            -1.5696190297603607e-01 3.9995491504669189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 594 -4.1472744196653366e-03</internalNodes>
          <leafValues>
            -6.5492427349090576e-01 8.9378200471401215e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 780 3.7436613347381353e-03</internalNodes>
          <leafValues>
            -1.6716095805168152e-01 2.9251593351364136e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 958 6.8116746842861176e-03</internalNodes>
          <leafValues>
            8.5507057607173920e-02 -6.0906678438186646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 4.9823587760329247e-03</internalNodes>
          <leafValues>
            6.6678501665592194e-02 -5.8637374639511108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 4.1811540722846985e-03</internalNodes>
          <leafValues>
            -1.0231449455022812e-01 4.5615595579147339e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 -1.0445066727697849e-02</internalNodes>
          <leafValues>
            -7.6294642686843872e-01 5.7752605527639389e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 -6.1920420266687870e-03</internalNodes>
          <leafValues>
            -6.6523051261901855e-01 5.2041802555322647e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 -2.3358925245702267e-03</internalNodes>
          <leafValues>
            3.2779076695442200e-01 -1.3041152060031891e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 -5.6658107787370682e-03</internalNodes>
          <leafValues>
            -6.8133842945098877e-01 5.6511644273996353e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 -1.0911209508776665e-02</internalNodes>
          <leafValues>
            3.3160626888275146e-01 -1.2574554979801178e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 -1.7958178650587797e-03</internalNodes>
          <leafValues>
            3.0492588877677917e-01 -1.5025834739208221e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 961 -4.0859095752239227e-03</internalNodes>
          <leafValues>
            -5.3059929609298706e-01 7.3518328368663788e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 -2.0980034023523331e-02</internalNodes>
          <leafValues>
            3.0610927939414978e-01 -1.2851184606552124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 -7.3018344119191170e-03</internalNodes>
          <leafValues>
            3.7514328956604004e-01 -1.2826474010944366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 -2.9183469712734222e-02</internalNodes>
          <leafValues>
            4.8722788691520691e-01 -8.4734588861465454e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 4.1865529492497444e-03</internalNodes>
          <leafValues>
            7.4395999312400818e-02 -5.8252948522567749e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 662 -1.3149678707122803e-02</internalNodes>
          <leafValues>
            2.9923921823501587e-01 -1.3687820732593536e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 2.3594651371240616e-02</internalNodes>
          <leafValues>
            6.2197674065828323e-02 -6.3622504472732544e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 630 -1.7678245902061462e-02</internalNodes>
          <leafValues>
            3.3281046152114868e-01 -1.2175620347261429e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 -1.0656865313649178e-02</internalNodes>
          <leafValues>
            3.7733754515647888e-01 -1.0491474717855453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 710 5.6399200111627579e-03</internalNodes>
          <leafValues>
            5.4276369512081146e-02 -7.2659504413604736e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 536 5.9759367257356644e-02</internalNodes>
          <leafValues>
            -9.3571089208126068e-02 4.3768402934074402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 836 1.5957858413457870e-03</internalNodes>
          <leafValues>
            -7.4856951832771301e-02 4.6114641427993774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 -3.4797050058841705e-02</internalNodes>
          <leafValues>
            4.3322169780731201e-01 -1.0314103215932846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 7.0810377597808838e-02</internalNodes>
          <leafValues>
            -1.3931407034397125e-01 2.7839028835296631e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 1.0995890479534864e-03</internalNodes>
          <leafValues>
            8.2519724965095520e-02 -4.6114745736122131e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 1.1323073413223028e-03</internalNodes>
          <leafValues>
            -1.1879909038543701e-01 3.1659367680549622e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 2.5819817557930946e-02</internalNodes>
          <leafValues>
            -1.0563226789236069e-01 3.3343997597694397e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 -3.0109258368611336e-03</internalNodes>
          <leafValues>
            -4.3363004922866821e-01 9.0100899338722229e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 6.4696911722421646e-03</internalNodes>
          <leafValues>
            4.3469883501529694e-02 -6.7987263202667236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 1.6815876588225365e-02</internalNodes>
          <leafValues>
            -9.4611480832099915e-02 3.5600626468658447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 642 -1.0637525469064713e-02</internalNodes>
          <leafValues>
            1.9808849692344666e-01 -1.5571328997612000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 -4.1952659375965595e-03</internalNodes>
          <leafValues>
            -5.3476226329803467e-01 6.5039873123168945e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 442 -1.5134566929191351e-03</internalNodes>
          <leafValues>
            4.2552566528320312e-01 -8.3483003079891205e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 -1.1657587019726634e-03</internalNodes>
          <leafValues>
            2.4901403486728668e-01 -1.2862072885036469e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 495 5.2369404584169388e-03</internalNodes>
          <leafValues>
            5.4264735430479050e-02 -6.5448409318923950e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 3.1857702415436506e-03</internalNodes>
          <leafValues>
            -1.0423248261213303e-01 3.4729331731796265e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 468 3.2315873540937901e-03</internalNodes>
          <leafValues>
            7.3592320084571838e-02 -4.5663174986839294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 8.1546325236558914e-03</internalNodes>
          <leafValues>
            5.8343004435300827e-02 -4.7462043166160583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 1.2796278111636639e-02</internalNodes>
          <leafValues>
            -1.0276926308870316e-01 3.4186348319053650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 -2.3663226515054703e-02</internalNodes>
          <leafValues>
            2.8974771499633789e-01 -1.2335656583309174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 -1.9080806523561478e-03</internalNodes>
          <leafValues>
            -7.0497012138366699e-01 5.8831237256526947e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 633 3.0501103028655052e-03</internalNodes>
          <leafValues>
            5.1351051777601242e-02 -5.2438431978225708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 -4.1300453245639801e-02</internalNodes>
          <leafValues>
            -4.2261663079261780e-01 6.5464369952678680e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>66</maxWeakCount>
      <stageThreshold>-1.6743642091751099e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 699 1.3901394791901112e-02</internalNodes>
          <leafValues>
            4.2875479906797409e-02 7.1057194471359253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 444 -1.0640731081366539e-02</internalNodes>
          <leafValues>
            5.9900563955307007e-01 -1.4475977420806885e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 -4.2351996526122093e-03</internalNodes>
          <leafValues>
            4.9660456180572510e-01 -1.1618923395872116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 1.3073073700070381e-02</internalNodes>
          <leafValues>
            -1.6428959369659424e-01 5.0240677595138550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 879 -5.0384560599923134e-03</internalNodes>
          <leafValues>
            4.1822317242622375e-01 -1.4690572023391724e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 1.2402677675709128e-03</internalNodes>
          <leafValues>
            -2.0295573770999908e-01 2.7518931031227112e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 -1.8025336321443319e-03</internalNodes>
          <leafValues>
            -7.3610079288482666e-01 7.6008267700672150e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 9.2999219894409180e-02</internalNodes>
          <leafValues>
            -1.4368277788162231e-01 3.3687326312065125e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 2.1399226039648056e-02</internalNodes>
          <leafValues>
            -1.6605213284492493e-01 3.0147713422775269e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 703 9.3348361551761627e-03</internalNodes>
          <leafValues>
            -1.6568347811698914e-01 3.0875685811042786e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 -5.8921691961586475e-03</internalNodes>
          <leafValues>
            -5.6214910745620728e-01 8.5347160696983337e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -1.4118023682385683e-03</internalNodes>
          <leafValues>
            2.6530963182449341e-01 -1.8439114093780518e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 360 -1.1060921475291252e-02</internalNodes>
          <leafValues>
            2.5005301833152771e-01 -1.7930330336093903e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 -3.8227883633226156e-03</internalNodes>
          <leafValues>
            -6.9963920116424561e-01 6.4229309558868408e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 900 -8.4845684468746185e-03</internalNodes>
          <leafValues>
            -6.6004335880279541e-01 5.2848633378744125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 6.9843865931034088e-03</internalNodes>
          <leafValues>
            5.5426578968763351e-02 -6.1068946123123169e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 952 4.8600544687360525e-04</internalNodes>
          <leafValues>
            -1.8981190025806427e-01 2.1059055626392365e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 3.5324528813362122e-02</internalNodes>
          <leafValues>
            -8.3993434906005859e-02 5.1941823959350586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 3.9002462290227413e-03</internalNodes>
          <leafValues>
            7.6367795467376709e-02 -5.7813030481338501e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 -4.5398771762847900e-03</internalNodes>
          <leafValues>
            3.3430457115173340e-01 -1.2331557273864746e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 -3.1904221978038549e-03</internalNodes>
          <leafValues>
            -5.5545800924301147e-01 7.3627986013889313e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -7.7975630760192871e-02</internalNodes>
          <leafValues>
            4.8881098628044128e-01 -8.1377774477005005e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 -1.1978685855865479e-02</internalNodes>
          <leafValues>
            2.7509516477584839e-01 -1.6613669693470001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 -8.3151785656809807e-03</internalNodes>
          <leafValues>
            -5.9314393997192383e-01 7.2014525532722473e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 466 1.3827871531248093e-02</internalNodes>
          <leafValues>
            -1.2803319096565247e-01 2.9465702176094055e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 2.0103808492422104e-02</internalNodes>
          <leafValues>
            6.9175720214843750e-02 -5.6205666065216064e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 530 -4.7183044254779816e-02</internalNodes>
          <leafValues>
            3.6449643969535828e-01 -1.0480687767267227e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 613 9.7303036600351334e-03</internalNodes>
          <leafValues>
            -9.7578004002571106e-02 3.6821505427360535e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 -3.9133569225668907e-03</internalNodes>
          <leafValues>
            -5.1443296670913696e-01 7.2868466377258301e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 666 3.0236630700528622e-03</internalNodes>
          <leafValues>
            5.5799212306737900e-02 -5.3572463989257812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 4.3436158448457718e-03</internalNodes>
          <leafValues>
            -1.5579865872859955e-01 2.0468661189079285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 1.4063127338886261e-02</internalNodes>
          <leafValues>
            3.6827385425567627e-02 -8.5656464099884033e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 1.3388927327468991e-03</internalNodes>
          <leafValues>
            -1.2769578397274017e-01 2.8884974122047424e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -1.0684424778446555e-03</internalNodes>
          <leafValues>
            3.4348404407501221e-01 -9.6543140709400177e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 779 -2.3754546418786049e-03</internalNodes>
          <leafValues>
            -5.2730453014373779e-01 6.6801987588405609e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 549 -4.5106699690222740e-03</internalNodes>
          <leafValues>
            -5.4066735506057739e-01 5.1074951887130737e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 5.6549627333879471e-03</internalNodes>
          <leafValues>
            -9.0248994529247284e-02 3.5249757766723633e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 494 -3.3719413913786411e-03</internalNodes>
          <leafValues>
            2.8907671570777893e-01 -1.1475016921758652e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 1.2221395969390869e-02</internalNodes>
          <leafValues>
            -1.0579165816307068e-01 3.0012521147727966e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 3.2931473106145859e-03</internalNodes>
          <leafValues>
            7.7675424516201019e-02 -4.1641706228256226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 -3.7331613712012768e-03</internalNodes>
          <leafValues>
            -4.6958047151565552e-01 7.6012723147869110e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -3.0090190470218658e-02</internalNodes>
          <leafValues>
            -6.2376546859741211e-01 4.4617597013711929e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 538 -1.1998139321804047e-02</internalNodes>
          <leafValues>
            3.4000751376152039e-01 -9.7472421824932098e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 592 2.5055123493075371e-02</internalNodes>
          <leafValues>
            -7.5190685689449310e-02 4.1993573307991028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 9.7161782905459404e-03</internalNodes>
          <leafValues>
            5.5982969701290131e-02 -6.0615026950836182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 -8.3486355841159821e-02</internalNodes>
          <leafValues>
            3.7426739931106567e-01 -8.7825424969196320e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 3.8952599279582500e-03</internalNodes>
          <leafValues>
            -1.3330259919166565e-01 2.6031884551048279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 -9.8262424580752850e-04</internalNodes>
          <leafValues>
            -3.9980980753898621e-01 8.2922257483005524e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 8.4215197712182999e-03</internalNodes>
          <leafValues>
            -1.3371372222900391e-01 2.4220858514308929e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 1.2292047031223774e-03</internalNodes>
          <leafValues>
            -7.7718295156955719e-02 3.8342806696891785e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 682 -1.2375478632748127e-02</internalNodes>
          <leafValues>
            2.3885957896709442e-01 -1.2220640480518341e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 5.4268687963485718e-03</internalNodes>
          <leafValues>
            5.5337987840175629e-02 -5.6287312507629395e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 418 -5.5062575265765190e-03</internalNodes>
          <leafValues>
            -5.7845181226730347e-01 4.2599424719810486e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 614 1.0439271107316017e-02</internalNodes>
          <leafValues>
            -8.1215575337409973e-02 3.8115817308425903e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 -2.6399283669888973e-03</internalNodes>
          <leafValues>
            -4.5001742243766785e-01 6.7338116466999054e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 2.6166734751313925e-03</internalNodes>
          <leafValues>
            5.5316500365734100e-02 -4.4958963990211487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 5.4496107622981071e-03</internalNodes>
          <leafValues>
            -8.1449449062347412e-02 3.8300925493240356e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 762 3.4203347750008106e-03</internalNodes>
          <leafValues>
            -8.8027402758598328e-02 3.0551746487617493e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 9.3909027054905891e-03</internalNodes>
          <leafValues>
            5.8738458901643753e-02 -4.9437648057937622e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 9.6318572759628296e-03</internalNodes>
          <leafValues>
            -1.0816254466772079e-01 2.7723982930183411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 1.9004285335540771e-02</internalNodes>
          <leafValues>
            -8.2863554358482361e-02 3.1288793683052063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 580 -8.5283098742365837e-03</internalNodes>
          <leafValues>
            2.9951119422912598e-01 -8.9406743645668030e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 6.2621399760246277e-02</internalNodes>
          <leafValues>
            -1.0106554627418518e-01 2.6782277226448059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 1.5604515559971333e-02</internalNodes>
          <leafValues>
            -1.0081429034471512e-01 2.9597061872482300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 1.3034233450889587e-01</internalNodes>
          <leafValues>
            -8.0539934337139130e-02 3.4444472193717957e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 4.5186877250671387e-02</internalNodes>
          <leafValues>
            5.8131664991378784e-02 -5.4693692922592163e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>72</maxWeakCount>
      <stageThreshold>-1.6700928211212158e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 763 -2.0017849747091532e-03</internalNodes>
          <leafValues>
            6.7056620121002197e-01 1.8883759155869484e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -1.5712467953562737e-02</internalNodes>
          <leafValues>
            5.8154827356338501e-01 -1.4273743331432343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 2.4972280953079462e-03</internalNodes>
          <leafValues>
            -1.8454430997371674e-01 3.4281551837921143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 5.7405559346079826e-03</internalNodes>
          <leafValues>
            -1.7856663465499878e-01 5.1571351289749146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 869 8.1182969734072685e-04</internalNodes>
          <leafValues>
            -2.6075574755668640e-01 2.2161382436752319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 889 1.9957972690463066e-03</internalNodes>
          <leafValues>
            -1.6690193116664886e-01 3.1083983182907104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 943 7.9797822982072830e-03</internalNodes>
          <leafValues>
            -1.7903617024421692e-01 2.7573335170745850e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 733 1.0206390172243118e-02</internalNodes>
          <leafValues>
            -9.3843363225460052e-02 5.3358590602874756e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 3.4904260188341141e-02</internalNodes>
          <leafValues>
            -8.6874812841415405e-02 5.0340282917022705e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 564 -3.8090778980404139e-03</internalNodes>
          <leafValues>
            2.3382174968719482e-01 -1.7857478559017181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 2.0690055098384619e-03</internalNodes>
          <leafValues>
            -1.8347945809364319e-01 2.1276330947875977e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 -1.3203858397901058e-02</internalNodes>
          <leafValues>
            3.0799895524978638e-01 -1.0921970754861832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 1.6044422984123230e-02</internalNodes>
          <leafValues>
            -1.2722490727901459e-01 3.5042104125022888e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 -2.8435707092285156e-02</internalNodes>
          <leafValues>
            -6.9257086515426636e-01 6.0832630842924118e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 -3.5719089210033417e-03</internalNodes>
          <leafValues>
            -6.1009460687637329e-01 5.5150210857391357e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 526 -3.3032532781362534e-02</internalNodes>
          <leafValues>
            3.9706656336784363e-01 -1.0905303061008453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 863 -3.4918042365461588e-03</internalNodes>
          <leafValues>
            2.2872641682624817e-01 -1.6809244453907013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 -1.2281725648790598e-03</internalNodes>
          <leafValues>
            2.8336051106452942e-01 -1.2107009440660477e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 1.5130165964365005e-02</internalNodes>
          <leafValues>
            7.4081726372241974e-02 -5.0906944274902344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 -5.2624624222517014e-03</internalNodes>
          <leafValues>
            -5.1471787691116333e-01 5.6423079222440720e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 1.3198424130678177e-03</internalNodes>
          <leafValues>
            -9.7633212804794312e-02 3.6938476562500000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 -3.9102118462324142e-03</internalNodes>
          <leafValues>
            -7.5149536132812500e-01 5.1220502704381943e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 7.7746850438416004e-03</internalNodes>
          <leafValues>
            -1.6232925653457642e-01 2.0670217275619507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 555 -1.9946731626987457e-02</internalNodes>
          <leafValues>
            3.0092230439186096e-01 -9.9984362721443176e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 -4.9132145941257477e-03</internalNodes>
          <leafValues>
            2.1954736113548279e-01 -1.6271042823791504e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 -6.4505757763981819e-03</internalNodes>
          <leafValues>
            -4.9815052747726440e-01 6.7167595028877258e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 -6.7926600575447083e-02</internalNodes>
          <leafValues>
            4.3458208441734314e-01 -7.7230423688888550e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 -3.7927636876702309e-03</internalNodes>
          <leafValues>
            2.6469963788986206e-01 -1.2098944932222366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -2.7826299890875816e-02</internalNodes>
          <leafValues>
            2.9173719882965088e-01 -1.0972167551517487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 6.0029705055058002e-03</internalNodes>
          <leafValues>
            -1.0543220490217209e-01 3.6175185441970825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 972 1.4797106850892305e-03</internalNodes>
          <leafValues>
            6.5247461199760437e-02 -5.3303873538970947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 9.7709074616432190e-03</internalNodes>
          <leafValues>
            3.5595752298831940e-02 -6.8972426652908325e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 4.5413424959406257e-04</internalNodes>
          <leafValues>
            -1.7233507335186005e-01 1.6001893579959869e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 687 -1.4395804610103369e-03</internalNodes>
          <leafValues>
            -5.1606172323226929e-01 5.5443800985813141e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -2.4502794444561005e-01</internalNodes>
          <leafValues>
            -8.0270200967788696e-01 2.9995493590831757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 410 4.2062443681061268e-03</internalNodes>
          <leafValues>
            5.0134483724832535e-02 -4.9082162976264954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 1.4561998657882214e-02</internalNodes>
          <leafValues>
            -1.2065179646015167e-01 2.4143299460411072e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 -3.3104062080383301e-02</internalNodes>
          <leafValues>
            3.6770820617675781e-01 -7.8033976256847382e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 3.2625906169414520e-02</internalNodes>
          <leafValues>
            -8.5231229662895203e-02 3.3994022011756897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 -1.1897137388586998e-02</internalNodes>
          <leafValues>
            2.6476562023162842e-01 -1.0443684458732605e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 816 5.7352259755134583e-03</internalNodes>
          <leafValues>
            5.5395182222127914e-02 -5.1973640918731689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 -1.5062794089317322e-02</internalNodes>
          <leafValues>
            -7.5300645828247070e-01 3.0149688944220543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 -7.9075228422880173e-03</internalNodes>
          <leafValues>
            2.8287026286125183e-01 -1.0227695107460022e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 3.2159998081624508e-03</internalNodes>
          <leafValues>
            -6.5929308533668518e-02 3.6590230464935303e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 987 -2.5269058533012867e-03</internalNodes>
          <leafValues>
            -4.7419819235801697e-01 5.8165557682514191e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 -1.8206760287284851e-03</internalNodes>
          <leafValues>
            2.5627982616424561e-01 -1.1308469623327255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 1.4899271540343761e-02</internalNodes>
          <leafValues>
            3.6266356706619263e-02 -7.6167815923690796e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 965 3.8532419130206108e-03</internalNodes>
          <leafValues>
            2.6696149259805679e-02 -7.5058454275131226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 -6.1673661693930626e-03</internalNodes>
          <leafValues>
            3.5572922229766846e-01 -8.2323268055915833e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 822 -7.0832269266247749e-03</internalNodes>
          <leafValues>
            2.1490900218486786e-01 -1.3906964659690857e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 724 3.5079419612884521e-03</internalNodes>
          <leafValues>
            4.7017443925142288e-02 -6.3156962394714355e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 1.2706735869869590e-03</internalNodes>
          <leafValues>
            -1.0659208893775940e-01 3.1986060738563538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 -3.0069730710238218e-03</internalNodes>
          <leafValues>
            -7.9292476177215576e-01 3.8936499506235123e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 2.5489944964647293e-03</internalNodes>
          <leafValues>
            -1.4662979543209076e-01 2.0257341861724854e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 2.7439245022833347e-03</internalNodes>
          <leafValues>
            -9.1609120368957520e-02 3.0721578001976013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 -2.5972947478294373e-03</internalNodes>
          <leafValues>
            -4.2591169476509094e-01 6.7926779389381409e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 717 1.4116591773927212e-03</internalNodes>
          <leafValues>
            -1.2321621179580688e-01 2.4072754383087158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 603 -2.5579232722520828e-02</internalNodes>
          <leafValues>
            5.7537192106246948e-01 -4.9050308763980865e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 -5.6264195591211319e-03</internalNodes>
          <leafValues>
            -5.2892494201660156e-01 6.0454059392213821e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 -1.8926127813756466e-03</internalNodes>
          <leafValues>
            2.4074989557266235e-01 -1.2150175124406815e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 -1.0207362473011017e-02</internalNodes>
          <leafValues>
            -6.7782247066497803e-01 4.3572813272476196e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 -3.1391347292810678e-03</internalNodes>
          <leafValues>
            1.9982162117958069e-01 -1.3408482074737549e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 -3.1065782532095909e-03</internalNodes>
          <leafValues>
            2.2946853935718536e-01 -1.2754726409912109e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 -5.2223210223019123e-03</internalNodes>
          <leafValues>
            -5.0463259220123291e-01 4.9872294068336487e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 -3.4163258969783783e-03</internalNodes>
          <leafValues>
            2.1628817915916443e-01 -1.1369658261537552e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 5.1404302939772606e-03</internalNodes>
          <leafValues>
            -9.6477277576923370e-02 2.4602085351943970e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 425 5.8603808283805847e-03</internalNodes>
          <leafValues>
            4.0628310292959213e-02 -6.3314515352249146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 -1.1338826734572649e-03</internalNodes>
          <leafValues>
            3.4083816409111023e-01 -7.9400509595870972e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 8.0572068691253662e-04</internalNodes>
          <leafValues>
            -9.5763482153415680e-02 3.2283502817153931e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 431 -4.8957285471260548e-03</internalNodes>
          <leafValues>
            -5.3353887796401978e-01 4.9622885882854462e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 675 7.1027994155883789e-02</internalNodes>
          <leafValues>
            -6.2327813357114792e-02 4.5151355862617493e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 911 -1.5200550667941570e-02</internalNodes>
          <leafValues>
            2.5859493017196655e-01 -1.0548926889896393e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>81</maxWeakCount>
      <stageThreshold>-1.5793150663375854e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 562 -5.8624427765607834e-03</internalNodes>
          <leafValues>
            6.2237185239791870e-01 -1.5120165422558784e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 507 4.7948276624083519e-03</internalNodes>
          <leafValues>
            -1.8684723973274231e-01 4.3783110380172729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 2.4864529259502888e-03</internalNodes>
          <leafValues>
            -1.9763210415840149e-01 3.4098726511001587e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 -7.7013596892356873e-03</internalNodes>
          <leafValues>
            3.4580937027931213e-01 -2.2749660909175873e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 -1.8182904459536076e-03</internalNodes>
          <leafValues>
            3.7125843763351440e-01 -1.4019212126731873e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -1.4325398951768875e-03</internalNodes>
          <leafValues>
            4.4040992856025696e-01 -1.0599569976329803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 5.4927580058574677e-03</internalNodes>
          <leafValues>
            8.4835931658744812e-02 -5.3435057401657104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -7.0448440965265036e-04</internalNodes>
          <leafValues>
            2.8704917430877686e-01 -1.4405579864978790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 -5.7945270091295242e-03</internalNodes>
          <leafValues>
            -5.1373738050460815e-01 8.3974525332450867e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 -1.1944295838475227e-02</internalNodes>
          <leafValues>
            3.5980853438377380e-01 -1.0862441360950470e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 3.4179994836449623e-03</internalNodes>
          <leafValues>
            -1.2899027764797211e-01 3.3064863085746765e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 805 -7.2756269946694374e-03</internalNodes>
          <leafValues>
            2.3903866112232208e-01 -1.6133096814155579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 647 -8.5910838097333908e-03</internalNodes>
          <leafValues>
            -8.4211397171020508e-01 4.1614245623350143e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 -3.7941135466098785e-02</internalNodes>
          <leafValues>
            2.4674071371555328e-01 -1.4177341759204865e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 -3.6782763898372650e-02</internalNodes>
          <leafValues>
            3.1695553660392761e-01 -1.1061279475688934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 -1.1086702346801758e-01</internalNodes>
          <leafValues>
            -7.7248167991638184e-01 4.9090590327978134e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 -4.3419219553470612e-02</internalNodes>
          <leafValues>
            -6.3905894756317139e-01 4.7248683869838715e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 7.1155801415443420e-03</internalNodes>
          <leafValues>
            5.5369954556226730e-02 -5.6675219535827637e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 2.9427852481603622e-02</internalNodes>
          <leafValues>
            -1.0744783282279968e-01 3.1137129664421082e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -5.6870315223932266e-02</internalNodes>
          <leafValues>
            4.8549285531044006e-01 -8.4140487015247345e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 -2.1800836548209190e-03</internalNodes>
          <leafValues>
            3.0136430263519287e-01 -1.0869345813989639e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 1.2336489744484425e-03</internalNodes>
          <leafValues>
            -8.9073576033115387e-02 3.3233630657196045e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 737 2.7930366341024637e-03</internalNodes>
          <leafValues>
            -1.1784177273511887e-01 2.3968270421028137e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 -1.4338749647140503e-01</internalNodes>
          <leafValues>
            4.7303047776222229e-01 -5.1608867943286896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 7.9552028328180313e-03</internalNodes>
          <leafValues>
            6.0302641242742538e-02 -4.7011384367942810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -4.6112807467579842e-03</internalNodes>
          <leafValues>
            -4.2084765434265137e-01 7.6558656990528107e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 7.8915785998106003e-03</internalNodes>
          <leafValues>
            -1.3884299993515015e-01 2.0495900511741638e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 -2.9021099209785461e-02</internalNodes>
          <leafValues>
            3.3683353662490845e-01 -8.6431317031383514e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 8.7932050228118896e-03</internalNodes>
          <leafValues>
            -1.2117365747690201e-01 2.3939569294452667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 692 -4.3326904997229576e-03</internalNodes>
          <leafValues>
            -6.3044422864913940e-01 4.8393115401268005e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 729 -1.6151482705026865e-03</internalNodes>
          <leafValues>
            3.0203807353973389e-01 -9.2361047863960266e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 -3.6242920905351639e-03</internalNodes>
          <leafValues>
            -4.0946927666664124e-01 7.3978066444396973e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -8.0828834325075150e-03</internalNodes>
          <leafValues>
            -6.1597609519958496e-01 4.0132850408554077e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 -1.8987425137311220e-03</internalNodes>
          <leafValues>
            2.5910443067550659e-01 -1.0553860664367676e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 3.4845639020204544e-03</internalNodes>
          <leafValues>
            4.1376896202564240e-02 -6.4275610446929932e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 728 -1.3704899698495865e-02</internalNodes>
          <leafValues>
            2.7235555648803711e-01 -1.0047114640474319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 -9.5059927552938461e-03</internalNodes>
          <leafValues>
            2.3305405676364899e-01 -1.2722322344779968e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 -1.7753308638930321e-02</internalNodes>
          <leafValues>
            2.7121108770370483e-01 -9.8972275853157043e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 4.1816420853137970e-02</internalNodes>
          <leafValues>
            -8.6314909160137177e-02 2.9308396577835083e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 470 -5.2781165577471256e-03</internalNodes>
          <leafValues>
            -4.3240407109260559e-01 6.6678449511528015e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 -3.5982416011393070e-03</internalNodes>
          <leafValues>
            -4.0664613246917725e-01 5.9939380735158920e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 -3.3817887306213379e-03</internalNodes>
          <leafValues>
            2.1198178827762604e-01 -1.2401402741670609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 -8.4468610584735870e-03</internalNodes>
          <leafValues>
            -5.0658410787582397e-01 5.6176334619522095e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 522 -1.5873050317168236e-02</internalNodes>
          <leafValues>
            3.0237907171249390e-01 -8.9766949415206909e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 948 5.2925320342183113e-03</internalNodes>
          <leafValues>
            4.7194946557283401e-02 -5.8447927236557007e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 -1.4822685159742832e-02</internalNodes>
          <leafValues>
            2.7581340074539185e-01 -1.0343603044748306e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 678 -1.2328238226473331e-02</internalNodes>
          <leafValues>
            1.9257421791553497e-01 -1.3730424642562866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 5.4736144840717316e-02</internalNodes>
          <leafValues>
            -1.0764957219362259e-01 2.4070124328136444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 -2.0030699670314789e-03</internalNodes>
          <leafValues>
            2.8992170095443726e-01 -8.6155213415622711e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 4.9616778269410133e-03</internalNodes>
          <leafValues>
            3.7793070077896118e-02 -6.8241751194000244e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 2.1582433953881264e-02</internalNodes>
          <leafValues>
            -9.2316769063472748e-02 2.6424714922904968e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 -9.5060802996158600e-03</internalNodes>
          <leafValues>
            2.0518042147159576e-01 -1.1375468224287033e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 7.4238084256649017e-02</internalNodes>
          <leafValues>
            -7.0650860667228699e-02 3.3061835169792175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 567 5.8014206588268280e-03</internalNodes>
          <leafValues>
            3.9557952433824539e-02 -6.0553658008575439e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 8.3722146227955818e-03</internalNodes>
          <leafValues>
            -6.6242359578609467e-02 3.5559263825416565e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 5.9322156012058258e-03</internalNodes>
          <leafValues>
            5.1374353468418121e-02 -4.4348692893981934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -1.5613199211657047e-03</internalNodes>
          <leafValues>
            3.3984366059303284e-01 -7.1964941918849945e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 -1.6189547255635262e-02</internalNodes>
          <leafValues>
            2.0777270197868347e-01 -1.2065915018320084e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 589 3.7794266827404499e-03</internalNodes>
          <leafValues>
            4.0290340781211853e-02 -5.5574357509613037e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 -9.3125496059656143e-03</internalNodes>
          <leafValues>
            2.5648719072341919e-01 -9.4139434397220612e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 8.9797464897856116e-04</internalNodes>
          <leafValues>
            -1.0596609860658646e-01 3.0083754658699036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 4.1124280542135239e-03</internalNodes>
          <leafValues>
            4.3452557176351547e-02 -5.6017982959747314e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 447 -5.7592550292611122e-03</internalNodes>
          <leafValues>
            -5.5715996026992798e-01 3.6530554294586182e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 -1.7109992913901806e-03</internalNodes>
          <leafValues>
            2.4350115656852722e-01 -9.9780716001987457e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 -1.9408876076340675e-02</internalNodes>
          <leafValues>
            -7.3736822605133057e-01 3.4421972930431366e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 -1.8505988642573357e-02</internalNodes>
          <leafValues>
            2.3959811031818390e-01 -1.0179302841424942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 974 -3.4968159161508083e-03</internalNodes>
          <leafValues>
            -4.5450085401535034e-01 5.2533198148012161e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 722 3.2340791076421738e-03</internalNodes>
          <leafValues>
            -9.7867593169212341e-02 2.5210717320442200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 853 -2.1103646140545607e-03</internalNodes>
          <leafValues>
            2.7718135714530945e-01 -8.3934187889099121e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 -6.0913376510143280e-03</internalNodes>
          <leafValues>
            -5.0269359350204468e-01 4.7654323279857635e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 -1.4657910168170929e-01</internalNodes>
          <leafValues>
            -5.8180803060531616e-01 3.7255339324474335e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 721 -5.7285130023956299e-02</internalNodes>
          <leafValues>
            -8.1681364774703979e-01 2.3416126146912575e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 560 -1.0855928063392639e-02</internalNodes>
          <leafValues>
            1.6488714516162872e-01 -1.3668881356716156e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 1.4742083847522736e-02</internalNodes>
          <leafValues>
            3.0842842534184456e-02 -7.8335261344909668e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 701 -1.8798124045133591e-02</internalNodes>
          <leafValues>
            3.1507465243339539e-01 -7.6406344771385193e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 458 1.8614485859870911e-02</internalNodes>
          <leafValues>
            -8.0178938806056976e-02 3.0463775992393494e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 803 -4.1011158376932144e-02</internalNodes>
          <leafValues>
            -6.7428815364837646e-01 3.5076040774583817e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -1.5514190308749676e-03</internalNodes>
          <leafValues>
            1.9351305067539215e-01 -1.0952673852443695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 7.6966043561697006e-03</internalNodes>
          <leafValues>
            3.2414216548204422e-02 -6.0751897096633911e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 -1.7850721254944801e-02</internalNodes>
          <leafValues>
            2.0461367070674896e-01 -1.0080502927303314e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 2.3059733211994171e-03</internalNodes>
          <leafValues>
            -1.3565167784690857e-01 1.7179486155509949e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>88</maxWeakCount>
      <stageThreshold>-1.6158927679061890e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 877 3.6321599036455154e-03</internalNodes>
          <leafValues>
            8.3464950323104858e-02 7.0313382148742676e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 -2.8561335057020187e-03</internalNodes>
          <leafValues>
            5.3444284200668335e-01 -1.4426039159297943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 767 -1.6910845879465342e-03</internalNodes>
          <leafValues>
            4.5417416095733643e-01 -1.4539502561092377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 2.4872912093997002e-02</internalNodes>
          <leafValues>
            -1.5180622041225433e-01 4.7290563583374023e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 -1.3844612985849380e-02</internalNodes>
          <leafValues>
            4.3979367613792419e-01 -1.9149754941463470e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 -2.0526167005300522e-02</internalNodes>
          <leafValues>
            3.5583654046058655e-01 -1.7526121437549591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 513 -1.0601939633488655e-02</internalNodes>
          <leafValues>
            2.1787860989570618e-01 -1.8377628922462463e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 1.5976445749402046e-02</internalNodes>
          <leafValues>
            8.6497880518436432e-02 -4.3799200654029846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 698 -4.9493601545691490e-03</internalNodes>
          <leafValues>
            2.3401068150997162e-01 -1.5603557229042053e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 2.8549194335937500e-02</internalNodes>
          <leafValues>
            -1.4324828982353210e-01 2.4674744904041290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 5.9233225882053375e-02</internalNodes>
          <leafValues>
            -1.0831536352634430e-01 3.3649173378944397e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 436 9.5888428390026093e-02</internalNodes>
          <leafValues>
            -1.0589215904474258e-01 3.7052422761917114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 453 -7.5694853439927101e-03</internalNodes>
          <leafValues>
            2.3113159835338593e-01 -1.4907826483249664e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 4.7857249155640602e-03</internalNodes>
          <leafValues>
            5.1639214158058167e-02 -6.2650465965270996e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 5.4310320410877466e-04</internalNodes>
          <leafValues>
            -1.8886238336563110e-01 1.5287129580974579e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 2.6117763482034206e-03</internalNodes>
          <leafValues>
            6.6002741456031799e-02 -4.4275501370429993e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 -8.0328416079282761e-03</internalNodes>
          <leafValues>
            -5.1598960161209106e-01 5.6888539344072342e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 1.2324389535933733e-03</internalNodes>
          <leafValues>
            -1.2387900799512863e-01 2.6769712567329407e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 -9.7260549664497375e-03</internalNodes>
          <leafValues>
            3.5000637173652649e-01 -1.0784699767827988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 8.6696096695959568e-04</internalNodes>
          <leafValues>
            -1.1435680836439133e-01 2.5563576817512512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 -3.4542869776487350e-02</internalNodes>
          <leafValues>
            -5.8313912153244019e-01 5.1875289529561996e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 7.5529976747930050e-03</internalNodes>
          <leafValues>
            3.6179721355438232e-02 -6.9380182027816772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 550 4.0939049795269966e-03</internalNodes>
          <leafValues>
            4.1062511503696442e-02 -5.9848028421401978e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 2.8113774023950100e-03</internalNodes>
          <leafValues>
            5.5211704224348068e-02 -4.7554171085357666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 -3.5045309923589230e-03</internalNodes>
          <leafValues>
            -3.9402753114700317e-01 6.9415092468261719e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 1.7898422665894032e-03</internalNodes>
          <leafValues>
            -7.6636046171188354e-02 4.0437600016593933e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 486 4.0369415655732155e-03</internalNodes>
          <leafValues>
            -9.7471550107002258e-02 2.7784994244575500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 430 -8.5533969104290009e-03</internalNodes>
          <leafValues>
            3.3252051472663879e-01 -1.0098887234926224e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 2.6041156053543091e-01</internalNodes>
          <leafValues>
            -4.3942935764789581e-02 5.0610113143920898e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 1.4894269406795502e-03</internalNodes>
          <leafValues>
            -7.6404698193073273e-02 3.0335766077041626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 -4.8547232151031494e-01</internalNodes>
          <leafValues>
            6.1892670392990112e-01 -3.9150018244981766e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 895 -7.6227495446801186e-04</internalNodes>
          <leafValues>
            1.9151827692985535e-01 -1.2709514796733856e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 553 -4.8853931948542595e-03</internalNodes>
          <leafValues>
            -5.6353646516799927e-01 4.4611949473619461e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 1.8246687250211835e-03</internalNodes>
          <leafValues>
            6.3832193613052368e-02 -3.6079093813896179e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -3.7001366727054119e-03</internalNodes>
          <leafValues>
            1.9149990379810333e-01 -1.3052800297737122e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -5.1008379086852074e-03</internalNodes>
          <leafValues>
            4.0633511543273926e-01 -6.6167853772640228e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -1.7046853899955750e-02</internalNodes>
          <leafValues>
            -3.5266619920730591e-01 7.1908973157405853e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 7.4824336916208267e-03</internalNodes>
          <leafValues>
            5.7628002017736435e-02 -4.6899631619453430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 955 -1.0255416855216026e-02</internalNodes>
          <leafValues>
            -5.3142738342285156e-01 4.1362568736076355e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 500 2.2401358000934124e-03</internalNodes>
          <leafValues>
            -7.4236951768398285e-02 3.2401573657989502e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 -1.1338146403431892e-02</internalNodes>
          <leafValues>
            -5.8281844854354858e-01 4.3767549097537994e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 8.4229296771809459e-04</internalNodes>
          <leafValues>
            -1.2624698877334595e-01 1.8474358320236206e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 2.7594935148954391e-02</internalNodes>
          <leafValues>
            4.2021647095680237e-02 -5.0711041688919067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 3.0329828150570393e-03</internalNodes>
          <leafValues>
            -7.0778228342533112e-02 3.3471760153770447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 984 -8.9767086319625378e-04</internalNodes>
          <leafValues>
            -2.8313115239143372e-01 8.4212802350521088e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 -4.3688914738595486e-03</internalNodes>
          <leafValues>
            -4.8104682564735413e-01 4.6536806970834732e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 817 -6.5738772973418236e-03</internalNodes>
          <leafValues>
            1.8955506384372711e-01 -1.1803213506937027e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 1.6207897569984198e-03</internalNodes>
          <leafValues>
            -6.5827853977680206e-02 3.2122904062271118e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 4.8786089755594730e-03</internalNodes>
          <leafValues>
            3.4384466707706451e-02 -7.0796263217926025e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 -7.3928516358137131e-03</internalNodes>
          <leafValues>
            -4.5154401659965515e-01 4.3622057884931564e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 2.2087101824581623e-03</internalNodes>
          <leafValues>
            -4.4949851930141449e-02 4.9991622567176819e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 2.9001948423683643e-03</internalNodes>
          <leafValues>
            5.1781963557004929e-02 -4.2971140146255493e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -9.9636090453714132e-04</internalNodes>
          <leafValues>
            3.0347472429275513e-01 -7.4883252382278442e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 6.3986405730247498e-03</internalNodes>
          <leafValues>
            3.8914524018764496e-02 -5.6881076097488403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 -5.2966945804655552e-03</internalNodes>
          <leafValues>
            -5.5442500114440918e-01 3.3734291791915894e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 540 9.2505104839801788e-03</internalNodes>
          <leafValues>
            -9.5525965094566345e-02 2.2353705763816833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 942 3.2701031304895878e-03</internalNodes>
          <leafValues>
            -8.7515957653522491e-02 2.7356430888175964e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 938 -9.0824589133262634e-03</internalNodes>
          <leafValues>
            2.2333034873008728e-01 -9.6923373639583588e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 981 -3.0515873804688454e-03</internalNodes>
          <leafValues>
            -4.0757030248641968e-01 5.0947520881891251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 518 6.3385991379618645e-03</internalNodes>
          <leafValues>
            -6.0888923704624176e-02 3.5273307561874390e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 -3.4346987958997488e-03</internalNodes>
          <leafValues>
            2.9356080293655396e-01 -8.1172131001949310e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 -3.9428919553756714e-03</internalNodes>
          <leafValues>
            -4.9512249231338501e-01 4.4033303856849670e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 -9.1780513525009155e-02</internalNodes>
          <leafValues>
            -3.9046007394790649e-01 5.0150144845247269e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 -6.6375046968460083e-02</internalNodes>
          <leafValues>
            -6.0851734876632690e-01 3.0974121764302254e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 621 -6.9266660138964653e-03</internalNodes>
          <leafValues>
            -7.3090195655822754e-01 2.3663638159632683e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 -3.5928614437580109e-02</internalNodes>
          <leafValues>
            1.8060323596000671e-01 -1.1467467248439789e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 848 -4.6623144298791885e-03</internalNodes>
          <leafValues>
            2.3666681349277496e-01 -1.0607369244098663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 597 6.3546001911163330e-02</internalNodes>
          <leafValues>
            3.3510908484458923e-02 -6.6823011636734009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 1.0070858988910913e-03</internalNodes>
          <leafValues>
            -1.1336669325828552e-01 1.9138091802597046e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 2.0964080467820168e-03</internalNodes>
          <leafValues>
            -1.3783766329288483e-01 1.6943521797657013e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 -3.7955917418003082e-02</internalNodes>
          <leafValues>
            4.2277663946151733e-01 -5.3925208747386932e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 915 1.5981765463948250e-03</internalNodes>
          <leafValues>
            7.2201833128929138e-02 -2.9906082153320312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 -1.0036448948085308e-03</internalNodes>
          <leafValues>
            3.0661484599113464e-01 -7.1594037115573883e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 -8.7398784235119820e-03</internalNodes>
          <leafValues>
            -4.0562248229980469e-01 5.0554409623146057e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 3.8445743266493082e-03</internalNodes>
          <leafValues>
            3.0860245227813721e-02 -5.6328177452087402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 2.9180462006479502e-03</internalNodes>
          <leafValues>
            -9.4123579561710358e-02 2.1829530596733093e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 705 -4.4532963074743748e-03</internalNodes>
          <leafValues>
            2.2356307506561279e-01 -1.0032130777835846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 2.8718812391161919e-03</internalNodes>
          <leafValues>
            5.8825947344303131e-02 -3.5275349020957947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 -9.6740125445649028e-04</internalNodes>
          <leafValues>
            2.2895433008670807e-01 -8.8445298373699188e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 6.3862642273306847e-03</internalNodes>
          <leafValues>
            -7.9845495522022247e-02 2.6533949375152588e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 739 -1.8584533827379346e-03</internalNodes>
          <leafValues>
            -3.5019376873970032e-01 5.8831077069044113e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -4.1777163743972778e-02</internalNodes>
          <leafValues>
            -7.5915527343750000e-01 2.5032732635736465e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 798 1.7466493882238865e-03</internalNodes>
          <leafValues>
            -7.8535526990890503e-02 2.5856694579124451e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 873 -1.7557941377162933e-02</internalNodes>
          <leafValues>
            1.5769363939762115e-01 -1.2735258042812347e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 -2.0210664719343185e-02</internalNodes>
          <leafValues>
            2.4479819834232330e-01 -8.0905362963676453e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 623 -1.2817385140806437e-03</internalNodes>
          <leafValues>
            2.0145194232463837e-01 -1.1555081605911255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 173 -4.6622417867183685e-03</internalNodes>
          <leafValues>
            -5.7475388050079346e-01 3.5327285528182983e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 7.2181676514446735e-03</internalNodes>
          <leafValues>
            -6.5562173724174500e-02 2.9846385121345520e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>91</maxWeakCount>
      <stageThreshold>-1.4331817626953125e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 508 -1.0981839150190353e-02</internalNodes>
          <leafValues>
            6.4476418495178223e-01 5.4893907159566879e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 4.4120610691606998e-03</internalNodes>
          <leafValues>
            -1.1835748702287674e-01 5.0045996904373169e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 2.8575016185641289e-03</internalNodes>
          <leafValues>
            -1.3248406350612640e-01 4.2942702770233154e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 2.0933073014020920e-02</internalNodes>
          <leafValues>
            -1.2826231122016907e-01 5.0430470705032349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 742 -1.4590121805667877e-02</internalNodes>
          <leafValues>
            3.1983098387718201e-01 -1.6088847815990448e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 6.0559157282114029e-02</internalNodes>
          <leafValues>
            -7.2834797203540802e-02 4.6935465931892395e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 537 3.3241603523492813e-02</internalNodes>
          <leafValues>
            -1.2657077610492706e-01 3.8025194406509399e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 -1.5009621158242226e-02</internalNodes>
          <leafValues>
            3.6463224887847900e-01 -9.8299026489257812e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 1.5744348056614399e-03</internalNodes>
          <leafValues>
            -1.1195008456707001e-01 3.1572943925857544e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 574 -2.3936885409057140e-03</internalNodes>
          <leafValues>
            -4.7027668356895447e-01 7.1979902684688568e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 4.7012902796268463e-03</internalNodes>
          <leafValues>
            9.1855168342590332e-02 -3.6548766493797302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 899 3.9329114370048046e-03</internalNodes>
          <leafValues>
            4.4214393943548203e-02 -5.6691557168960571e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 -2.8009498491883278e-03</internalNodes>
          <leafValues>
            -4.8461133241653442e-01 5.4654970765113831e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 -4.2012645862996578e-03</internalNodes>
          <leafValues>
            1.9422210752964020e-01 -1.4497868716716766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -8.7564159184694290e-03</internalNodes>
          <leafValues>
            -4.9580562114715576e-01 5.6972313672304153e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 843 3.0297664925456047e-03</internalNodes>
          <leafValues>
            -1.4587514102458954e-01 2.3592919111251831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 882 -1.1956840753555298e-02</internalNodes>
          <leafValues>
            3.6318615078926086e-01 -8.9037798345088959e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 -9.4736125320196152e-03</internalNodes>
          <leafValues>
            -6.6952317953109741e-01 5.2261870354413986e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 -7.3966579511761665e-03</internalNodes>
          <leafValues>
            -5.5038225650787354e-01 4.5135255903005600e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 909 5.1108570769429207e-03</internalNodes>
          <leafValues>
            4.1587084531784058e-02 -5.5355554819107056e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 2.1153174340724945e-02</internalNodes>
          <leafValues>
            -9.0628616511821747e-02 2.9272273182868958e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 690 -4.0524005889892578e-03</internalNodes>
          <leafValues>
            -5.5750316381454468e-01 4.8259153962135315e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 -4.5195231214165688e-03</internalNodes>
          <leafValues>
            2.4507603049278259e-01 -1.1037164181470871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 2.9709473252296448e-02</internalNodes>
          <leafValues>
            -9.0624623000621796e-02 3.0535447597503662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 596 -2.4458598345518112e-02</internalNodes>
          <leafValues>
            3.8106867671012878e-01 -6.5381005406379700e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 833 2.3627276532351971e-03</internalNodes>
          <leafValues>
            -8.9016206562519073e-02 2.7661785483360291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 576 2.5604534894227982e-03</internalNodes>
          <leafValues>
            4.9425628036260605e-02 -5.4407423734664917e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 1.1583864688873291e-02</internalNodes>
          <leafValues>
            3.7279289215803146e-02 -6.2233042716979980e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 745 -6.5262932330369949e-03</internalNodes>
          <leafValues>
            2.2733294963836670e-01 -1.1270135641098022e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 -5.5729500018060207e-03</internalNodes>
          <leafValues>
            -5.2463114261627197e-01 4.3969567865133286e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 2.7682324871420860e-03</internalNodes>
          <leafValues>
            -5.6408800184726715e-02 4.5612502098083496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 683 5.3484998643398285e-03</internalNodes>
          <leafValues>
            5.1867362111806870e-02 -4.8760178685188293e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 5.3632212802767754e-03</internalNodes>
          <leafValues>
            4.4008553028106689e-02 -4.8531344532966614e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 -4.5866379514336586e-03</internalNodes>
          <leafValues>
            -4.7415995597839355e-01 4.6904686838388443e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 -2.4732598103582859e-03</internalNodes>
          <leafValues>
            3.5594299435615540e-01 -6.6797487437725067e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 504 -1.3544519897550344e-03</internalNodes>
          <leafValues>
            2.5692245364189148e-01 -1.0549759119749069e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 2.0623015239834785e-02</internalNodes>
          <leafValues>
            -1.2213230878114700e-01 1.8851162493228912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 -2.5555126368999481e-02</internalNodes>
          <leafValues>
            2.1115033328533173e-01 -1.1155050992965698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 -2.6123304851353168e-03</internalNodes>
          <leafValues>
            -4.3362092971801758e-01 5.9404496103525162e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 454 -2.0256865769624710e-02</internalNodes>
          <leafValues>
            2.3865076899528503e-01 -1.1111994832754135e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 -1.3834737241268158e-02</internalNodes>
          <leafValues>
            2.4716469645500183e-01 -9.9341392517089844e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 1.0408030357211828e-03</internalNodes>
          <leafValues>
            -8.5192345082759857e-02 2.8685340285301208e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 693 -7.8561680857092142e-04</internalNodes>
          <leafValues>
            -3.1685733795166016e-01 7.3741830885410309e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 -3.1890799291431904e-03</internalNodes>
          <leafValues>
            2.8838813304901123e-01 -7.9454399645328522e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 -1.8864404410123825e-02</internalNodes>
          <leafValues>
            -6.1039513349533081e-01 3.4549634903669357e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 1.9871932454407215e-03</internalNodes>
          <leafValues>
            -6.4915224909782410e-02 3.3199799060821533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 -4.4487272389233112e-03</internalNodes>
          <leafValues>
            -4.8411524295806885e-01 4.3471843004226685e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 1.1506278999149799e-02</internalNodes>
          <leafValues>
            3.4361593425273895e-02 -5.1601499319076538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 -4.9164015799760818e-03</internalNodes>
          <leafValues>
            2.8029051423072815e-01 -7.6438650488853455e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 6.8624541163444519e-03</internalNodes>
          <leafValues>
            -1.3434819877147675e-01 1.7164944112300873e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 788 1.2513504363596439e-02</internalNodes>
          <leafValues>
            -8.2097627222537994e-02 2.6367199420928955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 2.5324244052171707e-02</internalNodes>
          <leafValues>
            -9.8793335258960724e-02 2.1438889205455780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 4.2827110737562180e-03</internalNodes>
          <leafValues>
            4.8010587692260742e-02 -5.3329360485076904e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 -1.0588562116026878e-02</internalNodes>
          <leafValues>
            2.0557209849357605e-01 -1.1096615344285965e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 7.3749665170907974e-03</internalNodes>
          <leafValues>
            4.2330745607614517e-02 -4.9726718664169312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 -1.0184315964579582e-02</internalNodes>
          <leafValues>
            2.2020325064659119e-01 -1.0080294311046600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 -3.1706739682704210e-03</internalNodes>
          <leafValues>
            -4.1845852136611938e-01 5.4482847452163696e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 2.5342048611491919e-03</internalNodes>
          <leafValues>
            -6.9553844630718231e-02 3.2299628853797913e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 521 -8.4150061011314392e-03</internalNodes>
          <leafValues>
            2.6279926300048828e-01 -9.3804508447647095e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 4.0150225162506104e-02</internalNodes>
          <leafValues>
            2.9147522523999214e-02 -7.8112679719924927e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 -4.3324208818376064e-03</internalNodes>
          <leafValues>
            -5.2339142560958862e-01 3.6419976502656937e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 -4.0873344987630844e-02</internalNodes>
          <leafValues>
            3.7220278382301331e-01 -6.1695497483015060e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 5.9101730585098267e-02</internalNodes>
          <leafValues>
            -5.1950857043266296e-02 4.1701674461364746e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 628 -3.1048480886965990e-03</internalNodes>
          <leafValues>
            2.1874889731407166e-01 -9.0781040489673615e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 627 3.5321055911481380e-03</internalNodes>
          <leafValues>
            -8.2859635353088379e-02 2.9276433587074280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 668 -1.2976058060303330e-03</internalNodes>
          <leafValues>
            -4.3279412388801575e-01 4.9447599798440933e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 1.1339010670781136e-02</internalNodes>
          <leafValues>
            2.6531336829066277e-02 -6.9358879327774048e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 969 8.8861025869846344e-03</internalNodes>
          <leafValues>
            2.6764476671814919e-02 -6.1700969934463501e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 1.1916678398847580e-02</internalNodes>
          <leafValues>
            -9.7341567277908325e-02 2.0659063756465912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 804 1.2824826873838902e-02</internalNodes>
          <leafValues>
            -8.5851043462753296e-02 2.6430803537368774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 820 -4.0587522089481354e-03</internalNodes>
          <leafValues>
            1.8127168715000153e-01 -1.1241084337234497e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 931 5.4964423179626465e-04</internalNodes>
          <leafValues>
            -9.1979973018169403e-02 2.1896743774414062e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 1.5558412997052073e-03</internalNodes>
          <leafValues>
            -7.7465757727622986e-02 2.5980666279792786e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 587 4.3281335383653641e-03</internalNodes>
          <leafValues>
            3.4584067761898041e-02 -6.2342578172683716e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -1.4533417299389839e-02</internalNodes>
          <leafValues>
            2.0229732990264893e-01 -1.0071664303541183e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 5.6288111954927444e-03</internalNodes>
          <leafValues>
            3.9174310863018036e-02 -5.0741255283355713e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 7.9474167432636023e-04</internalNodes>
          <leafValues>
            -8.0158397555351257e-02 2.3534512519836426e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 585 -1.0926688089966774e-02</internalNodes>
          <leafValues>
            -5.9471416473388672e-01 3.1198443844914436e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 -8.2422709092497826e-03</internalNodes>
          <leafValues>
            2.0226760208606720e-01 -1.1066834628582001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 923 -3.6351364105939865e-03</internalNodes>
          <leafValues>
            -6.4762312173843384e-01 2.9601249843835831e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 784 -2.7860058471560478e-03</internalNodes>
          <leafValues>
            4.2289113998413086e-01 -4.8328761011362076e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 1.4032858889549971e-03</internalNodes>
          <leafValues>
            7.7722996473312378e-02 -2.5018605589866638e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 -4.9225967377424240e-03</internalNodes>
          <leafValues>
            -5.3430163860321045e-01 3.4801222383975983e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 566 -1.2032099068164825e-02</internalNodes>
          <leafValues>
            2.3754563927650452e-01 -8.4349773824214935e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 5.9743728488683701e-03</internalNodes>
          <leafValues>
            -9.6527986228466034e-02 2.0883874595165253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 9.0228002518415451e-03</internalNodes>
          <leafValues>
            2.6527268812060356e-02 -7.1255648136138916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 -4.3231204152107239e-02</internalNodes>
          <leafValues>
            -4.5857131481170654e-01 3.5763122141361237e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 1.9863661378622055e-02</internalNodes>
          <leafValues>
            3.8993570953607559e-02 -4.7274601459503174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 1.7278429004363716e-04</internalNodes>
          <leafValues>
            -1.2378288805484772e-01 1.5123386681079865e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 8.4997266530990601e-03</internalNodes>
          <leafValues>
            3.8503456860780716e-02 -4.7635042667388916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 -1.3840992469340563e-03</internalNodes>
          <leafValues>
            2.4361917376518250e-01 -7.7643536031246185e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.4933127164840698e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 769 -2.4473592638969421e-03</internalNodes>
          <leafValues>
            6.4225250482559204e-01 1.1617031693458557e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 918 2.5613610632717609e-03</internalNodes>
          <leafValues>
            -1.4125512540340424e-01 4.1756254434585571e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 -3.6349727306514978e-03</internalNodes>
          <leafValues>
            4.2445364594459534e-01 -1.2421201914548874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 7.6568350195884705e-02</internalNodes>
          <leafValues>
            -2.0400929450988770e-01 2.4128499627113342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 4.6866592019796371e-03</internalNodes>
          <leafValues>
            7.2638466954231262e-02 -5.3155571222305298e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 -3.9937674999237061e-02</internalNodes>
          <leafValues>
            4.3441477417945862e-01 -8.1894725561141968e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 -1.7270710319280624e-02</internalNodes>
          <leafValues>
            4.9218431115150452e-01 -8.1687144935131073e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 1.0907177627086639e-01</internalNodes>
          <leafValues>
            -1.3829828798770905e-01 3.0388528108596802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 563 -8.6617004126310349e-03</internalNodes>
          <leafValues>
            2.4966995418071747e-01 -1.4650684595108032e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 5.3433720022439957e-03</internalNodes>
          <leafValues>
            5.6382026523351669e-02 -4.7699481248855591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 -7.3083816096186638e-03</internalNodes>
          <leafValues>
            1.6508759558200836e-01 -1.8176083266735077e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 -1.2855050154030323e-03</internalNodes>
          <leafValues>
            2.5566878914833069e-01 -1.1560125648975372e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 1.6045335214585066e-03</internalNodes>
          <leafValues>
            -1.5024451911449432e-01 1.8980197608470917e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 711 -3.2996428199112415e-03</internalNodes>
          <leafValues>
            -5.4483765363693237e-01 5.2060887217521667e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 -2.1930811926722527e-03</internalNodes>
          <leafValues>
            -5.0953930616378784e-01 4.9616143107414246e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 -1.3328871689736843e-02</internalNodes>
          <leafValues>
            3.2616052031517029e-01 -8.1101849675178528e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 3.5710524767637253e-02</internalNodes>
          <leafValues>
            -9.0930387377738953e-02 3.1067803502082825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 488 6.7417006939649582e-03</internalNodes>
          <leafValues>
            -7.7294938266277313e-02 3.2216030359268188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -8.1952005624771118e-02</internalNodes>
          <leafValues>
            5.3666585683822632e-01 -5.9473395347595215e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 9.2416517436504364e-03</internalNodes>
          <leafValues>
            -1.2617717683315277e-01 2.0812577009201050e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 619 -2.0654057152569294e-03</internalNodes>
          <leafValues>
            -4.8568764328956604e-01 5.2649311721324921e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 -1.3702171854674816e-02</internalNodes>
          <leafValues>
            -6.6060936450958252e-01 3.5181287676095963e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 -3.0807605944573879e-03</internalNodes>
          <leafValues>
            -4.4769099354743958e-01 4.8634912818670273e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 -1.2945728376507759e-02</internalNodes>
          <leafValues>
            -5.4323107004165649e-01 4.0633078664541245e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 -1.3134386390447617e-02</internalNodes>
          <leafValues>
            -4.7699347138404846e-01 4.5706178992986679e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 1.6984546091407537e-03</internalNodes>
          <leafValues>
            -7.0986136794090271e-02 3.2597315311431885e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 3.8461894728243351e-03</internalNodes>
          <leafValues>
            4.0658432990312576e-02 -5.7832121849060059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 -1.7426438629627228e-02</internalNodes>
          <leafValues>
            -4.3611589074134827e-01 4.5463379472494125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 730 -2.6193801313638687e-03</internalNodes>
          <leafValues>
            2.5506833195686340e-01 -8.7045751512050629e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 -1.0474737733602524e-02</internalNodes>
          <leafValues>
            2.3522177338600159e-01 -9.5193855464458466e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 -2.0476649515330791e-03</internalNodes>
          <leafValues>
            -4.0278571844100952e-01 5.3846791386604309e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 492 5.0511634908616543e-03</internalNodes>
          <leafValues>
            3.5829022526741028e-02 -5.8457142114639282e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 -2.6015858165919781e-03</internalNodes>
          <leafValues>
            2.8992271423339844e-01 -7.7776394784450531e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 1.4678405132144690e-03</internalNodes>
          <leafValues>
            4.2822040617465973e-02 -5.0615262985229492e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 3.4870039671659470e-03</internalNodes>
          <leafValues>
            -8.2636579871177673e-02 2.5724917650222778e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 -1.9308419525623322e-01</internalNodes>
          <leafValues>
            3.6281177401542664e-01 -6.3503719866275787e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 1.9733399152755737e-02</internalNodes>
          <leafValues>
            -8.6004406213760376e-02 2.5530698895454407e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 -3.2536339014768600e-02</internalNodes>
          <leafValues>
            -5.8808100223541260e-01 3.7802245467901230e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 8.8406521826982498e-03</internalNodes>
          <leafValues>
            2.5931548327207565e-02 -6.5399199724197388e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 -1.0922113433480263e-03</internalNodes>
          <leafValues>
            2.0415900647640228e-01 -1.0292074084281921e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 1.5520181041210890e-03</internalNodes>
          <leafValues>
            -8.4858812391757965e-02 3.0234101414680481e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 3.6752538289874792e-03</internalNodes>
          <leafValues>
            3.9351969957351685e-02 -5.4435199499130249e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 -1.7198601737618446e-02</internalNodes>
          <leafValues>
            -6.8883073329925537e-01 2.4503545835614204e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 783 -4.8199836164712906e-03</internalNodes>
          <leafValues>
            3.3481866121292114e-01 -6.0483735054731369e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 838 8.6327344179153442e-03</internalNodes>
          <leafValues>
            2.9317237436771393e-02 -7.1339315176010132e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 761 5.9000156819820404e-02</internalNodes>
          <leafValues>
            2.5574376806616783e-02 -6.3106632232666016e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 1.0106523986905813e-03</internalNodes>
          <leafValues>
            -1.0588756203651428e-01 1.9086131453514099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 880 1.1946363374590874e-03</internalNodes>
          <leafValues>
            -8.1224068999290466e-02 2.6191043853759766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 8.8360151275992393e-03</internalNodes>
          <leafValues>
            -7.4570186436176300e-02 2.7295246720314026e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 -3.8921819068491459e-03</internalNodes>
          <leafValues>
            -4.6577858924865723e-01 4.1507720947265625e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 2.6258802972733974e-03</internalNodes>
          <leafValues>
            6.9952867925167084e-02 -2.7727422118186951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 4.6104520559310913e-02</internalNodes>
          <leafValues>
            2.8723197057843208e-02 -6.4130103588104248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 -2.3556766100227833e-03</internalNodes>
          <leafValues>
            1.6910773515701294e-01 -1.2073179334402084e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 2.0879062358289957e-03</internalNodes>
          <leafValues>
            -6.1540558934211731e-02 3.1166607141494751e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 726 -2.7457359246909618e-03</internalNodes>
          <leafValues>
            -4.3359285593032837e-01 4.4867228716611862e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 -5.7035693898797035e-03</internalNodes>
          <leafValues>
            -6.3301932811737061e-01 2.7553720399737358e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 -2.3487601429224014e-03</internalNodes>
          <leafValues>
            2.7741125226020813e-01 -7.1828551590442657e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 -1.2618269771337509e-02</internalNodes>
          <leafValues>
            -6.0198032855987549e-01 3.3369537442922592e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 -1.2911427766084671e-02</internalNodes>
          <leafValues>
            1.9097310304641724e-01 -9.6765249967575073e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 2.5069573894143105e-02</internalNodes>
          <leafValues>
            2.9379865154623985e-02 -6.2714409828186035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 7.4739558622241020e-03</internalNodes>
          <leafValues>
            -6.2408778816461563e-02 3.2309064269065857e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 -5.5288206785917282e-03</internalNodes>
          <leafValues>
            -4.4652014970779419e-01 4.4482827186584473e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 707 5.1614809781312943e-03</internalNodes>
          <leafValues>
            -1.0451946407556534e-01 1.8219007551670074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 -4.6268366277217865e-03</internalNodes>
          <leafValues>
            2.4900399148464203e-01 -7.5879700481891632e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 -5.0914911553263664e-03</internalNodes>
          <leafValues>
            -3.8135659694671631e-01 4.8699565231800079e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 1.3252631761133671e-03</internalNodes>
          <leafValues>
            -8.2675725221633911e-02 2.3466596007347107e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 989 -2.9426435939967632e-03</internalNodes>
          <leafValues>
            -3.2485908269882202e-01 5.8781418949365616e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 7.1004321798682213e-03</internalNodes>
          <leafValues>
            -1.0710758715867996e-01 1.6876961290836334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 1.6454465221613646e-03</internalNodes>
          <leafValues>
            -6.8138562142848969e-02 2.6411062479019165e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 -4.4519053772091866e-03</internalNodes>
          <leafValues>
            -6.5162777900695801e-01 3.1989157199859619e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 -5.6191058829426765e-03</internalNodes>
          <leafValues>
            2.2833730280399323e-01 -8.4053449332714081e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 -5.4375766776502132e-03</internalNodes>
          <leafValues>
            -4.2135429382324219e-01 4.7153089195489883e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 1.4303867937996984e-03</internalNodes>
          <leafValues>
            -8.1828169524669647e-02 2.2752483189105988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 579 5.3636888042092323e-03</internalNodes>
          <leafValues>
            4.6839229762554169e-02 -4.3641954660415649e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 471 6.7459633573889732e-03</internalNodes>
          <leafValues>
            4.8482794314622879e-02 -3.7357741594314575e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 1.8225495005026460e-03</internalNodes>
          <leafValues>
            -7.2007156908512115e-02 2.6155912876129150e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 -6.8536788225173950e-02</internalNodes>
          <leafValues>
            2.3726168274879456e-01 -8.8266216218471527e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 806 8.4726633504033089e-03</internalNodes>
          <leafValues>
            3.0765017494559288e-02 -6.1803394556045532e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 1.6150671988725662e-02</internalNodes>
          <leafValues>
            -6.9989733397960663e-02 2.7056843042373657e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 7.6075509190559387e-02</internalNodes>
          <leafValues>
            -6.6986277699470520e-02 2.6545816659927368e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -2.4037770926952362e-03</internalNodes>
          <leafValues>
            2.0211938023567200e-01 -1.1674832552671432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 1.8187157809734344e-02</internalNodes>
          <leafValues>
            3.7632372230291367e-02 -5.2273052930831909e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 -1.3952046632766724e-02</internalNodes>
          <leafValues>
            3.2746854424476624e-01 -6.3546165823936462e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 -2.6383304595947266e-01</internalNodes>
          <leafValues>
            -4.4734519720077515e-01 4.3956480920314789e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 3.7522446364164352e-03</internalNodes>
          <leafValues>
            3.8990244269371033e-02 -4.2726546525955200e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 1.4125283341854811e-03</internalNodes>
          <leafValues>
            -5.6341815739870071e-02 3.3222517371177673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 582 4.1739037260413170e-03</internalNodes>
          <leafValues>
            -8.1689253449440002e-02 2.1681067347526550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 982 -6.1572249978780746e-03</internalNodes>
          <leafValues>
            -4.6595495939254761e-01 4.0940407663583755e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 482 -4.0140310302376747e-03</internalNodes>
          <leafValues>
            -4.4917678833007812e-01 3.6913067102432251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 5.7834591716527939e-03</internalNodes>
          <leafValues>
            3.7202619016170502e-02 -4.4926682114601135e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 -2.7866717427968979e-03</internalNodes>
          <leafValues>
            2.3668289184570312e-01 -7.8896284103393555e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 740 -2.0863343961536884e-03</internalNodes>
          <leafValues>
            -3.1535735726356506e-01 5.4699663072824478e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 -3.5249911248683929e-02</internalNodes>
          <leafValues>
            2.3871497809886932e-01 -7.4336178600788116e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 1.9735466688871384e-02</internalNodes>
          <leafValues>
            -6.9253593683242798e-02 2.7050065994262695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 977 6.9166612811386585e-03</internalNodes>
          <leafValues>
            3.9671208709478378e-02 -4.5562696456909180e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 1.2886511161923409e-02</internalNodes>
          <leafValues>
            -5.7705853134393692e-02 3.0492311716079712e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 680 -4.3238401412963867e-02</internalNodes>
          <leafValues>
            -6.9368255138397217e-01 2.5768887251615524e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 858 5.2338346838951111e-02</internalNodes>
          <leafValues>
            -4.1087090969085693e-02 4.6693238615989685e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 903 -3.8494272157549858e-03</internalNodes>
          <leafValues>
            1.6975462436676025e-01 -1.2150127440690994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 1.1914474889636040e-02</internalNodes>
          <leafValues>
            -7.7901296317577362e-02 2.2727672755718231e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.5521451234817505e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 700 1.0511254891753197e-02</internalNodes>
          <leafValues>
            7.8954458236694336e-02 6.2691432237625122e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 501 -4.5915953814983368e-03</internalNodes>
          <leafValues>
            4.8032283782958984e-01 -1.2750500440597534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 920 1.5926066553220153e-03</internalNodes>
          <leafValues>
            -2.2285957634449005e-01 2.5022059679031372e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 4.7702826559543610e-02</internalNodes>
          <leafValues>
            -1.6401256620883942e-01 4.6663716435432434e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 801 3.6355913616716862e-03</internalNodes>
          <leafValues>
            -9.0887933969497681e-02 4.3753233551979065e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 505 -2.9029550496488810e-03</internalNodes>
          <leafValues>
            3.1178581714630127e-01 -1.4401906728744507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 6.0793287120759487e-03</internalNodes>
          <leafValues>
            -1.6874884068965912e-01 2.2609569132328033e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 4.1879736818373203e-04</internalNodes>
          <leafValues>
            -1.9620604813098907e-01 1.5544828772544861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 2.2689576144330204e-04</internalNodes>
          <leafValues>
            -1.9928123056888580e-01 1.4717149734497070e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 -8.1100836396217346e-02</internalNodes>
          <leafValues>
            -5.1434135437011719e-01 5.8547608554363251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 2.7355052530765533e-02</internalNodes>
          <leafValues>
            6.0101613402366638e-02 -4.1557094454765320e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 967 -2.4958662688732147e-03</internalNodes>
          <leafValues>
            -5.0487858057022095e-01 5.1647525280714035e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 420 -1.5961761819198728e-03</internalNodes>
          <leafValues>
            2.1670737862586975e-01 -1.2691333889961243e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 369 2.1885756403207779e-02</internalNodes>
          <leafValues>
            -8.3462193608283997e-02 3.1052881479263306e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 760 -1.3334145769476891e-02</internalNodes>
          <leafValues>
            3.0464804172515869e-01 -8.8519357144832611e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 -1.9117443589493632e-03</internalNodes>
          <leafValues>
            -6.8484777212142944e-01 4.3931856751441956e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 689 2.2813947871327400e-03</internalNodes>
          <leafValues>
            4.6766888350248337e-02 -4.8588466644287109e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 -8.7422672659158707e-03</internalNodes>
          <leafValues>
            -4.9902194738388062e-01 4.8665110021829605e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 758 -4.0188119746744633e-03</internalNodes>
          <leafValues>
            2.2568543255329132e-01 -1.1153879016637802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 966 1.3594499323517084e-03</internalNodes>
          <leafValues>
            4.2799551039934158e-02 -5.6778526306152344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 -6.5033760620281100e-04</internalNodes>
          <leafValues>
            2.0394213497638702e-01 -1.1888848990201950e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 473 -3.1412184238433838e-02</internalNodes>
          <leafValues>
            2.5797879695892334e-01 -9.3766883015632629e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 -9.2636439949274063e-03</internalNodes>
          <leafValues>
            -5.6845456361770630e-01 4.3344158679246902e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 383 -4.1950333863496780e-02</internalNodes>
          <leafValues>
            -3.3588516712188721e-01 6.3371837139129639e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 2.9165907762944698e-03</internalNodes>
          <leafValues>
            -6.9124616682529449e-02 3.5223892331123352e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 904 -1.0271451901644468e-03</internalNodes>
          <leafValues>
            1.8418928980827332e-01 -1.1801387369632721e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 663 2.6636901311576366e-03</internalNodes>
          <leafValues>
            -1.0614035278558731e-01 2.1366043388843536e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 629 -1.8712934106588364e-02</internalNodes>
          <leafValues>
            2.4622038006782532e-01 -9.3236625194549561e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 -4.8551969230175018e-03</internalNodes>
          <leafValues>
            3.0577266216278076e-01 -7.4214689433574677e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 2.4788705632090569e-03</internalNodes>
          <leafValues>
            -8.6660243570804596e-02 2.9340657591819763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 -4.3734526261687279e-03</internalNodes>
          <leafValues>
            -5.0270831584930420e-01 4.9233034253120422e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 -4.9623926170170307e-03</internalNodes>
          <leafValues>
            -4.4072946906089783e-01 4.5711714774370193e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 3.0696424655616283e-03</internalNodes>
          <leafValues>
            5.5255725979804993e-02 -3.7899428606033325e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 -8.1601645797491074e-03</internalNodes>
          <leafValues>
            2.6425153017044067e-01 -8.2545064389705658e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 -1.4500592369586229e-03</internalNodes>
          <leafValues>
            1.8156392872333527e-01 -1.2637530267238617e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 -3.7104606162756681e-03</internalNodes>
          <leafValues>
            1.9824002683162689e-01 -1.0828326642513275e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 673 -8.7567782029509544e-03</internalNodes>
          <leafValues>
            2.8610706329345703e-01 -7.3251776397228241e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 -1.8970356322824955e-03</internalNodes>
          <leafValues>
            -6.0897153615951538e-01 3.7743657827377319e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 3.8996827602386475e-01</internalNodes>
          <leafValues>
            -5.8310892432928085e-02 3.6127504706382751e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 -4.9336552619934082e-03</internalNodes>
          <leafValues>
            3.0048343539237976e-01 -6.4242139458656311e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 2.5108925998210907e-02</internalNodes>
          <leafValues>
            2.5090903043746948e-02 -8.1637203693389893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 4.2880335822701454e-03</internalNodes>
          <leafValues>
            -8.1443257629871368e-02 2.6617726683616638e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 456 -5.6165808928199112e-05</internalNodes>
          <leafValues>
            1.4481364190578461e-01 -1.6212667524814606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 3.0591385439038277e-03</internalNodes>
          <leafValues>
            2.8733581304550171e-02 -7.0054715871810913e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 -3.0338061042129993e-03</internalNodes>
          <leafValues>
            -8.2671564817428589e-01 1.9727285951375961e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 -2.1344989072531462e-03</internalNodes>
          <leafValues>
            1.6036525368690491e-01 -1.2014801055192947e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 2.4434458464384079e-02</internalNodes>
          <leafValues>
            -1.0801825672388077e-01 2.0232307910919189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 -6.6356086172163486e-03</internalNodes>
          <leafValues>
            1.9015397131443024e-01 -1.0796815156936646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 781 -8.2725454121828079e-03</internalNodes>
          <leafValues>
            3.1057110428810120e-01 -8.6532585322856903e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 -7.1973735466599464e-03</internalNodes>
          <leafValues>
            -5.8514142036437988e-01 3.6453813314437866e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 -1.3809885131195188e-03</internalNodes>
          <leafValues>
            1.6234619915485382e-01 -1.2079101055860519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 1.2584345415234566e-02</internalNodes>
          <leafValues>
            -8.4917336702346802e-02 2.3094473779201508e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 907 8.0425739288330078e-03</internalNodes>
          <leafValues>
            4.0869396179914474e-02 -5.2088880538940430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 4.0921196341514587e-02</internalNodes>
          <leafValues>
            -5.4803006350994110e-02 3.5920065641403198e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 635 -3.6556557752192020e-03</internalNodes>
          <leafValues>
            3.0303934216499329e-01 -6.9975942373275757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 -2.6071248576045036e-03</internalNodes>
          <leafValues>
            1.7682927846908569e-01 -1.1164762824773788e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 -5.8146800845861435e-02</internalNodes>
          <leafValues>
            -3.7277954816818237e-01 5.6890588253736496e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 -2.2651627659797668e-01</internalNodes>
          <leafValues>
            4.0828245878219604e-01 -5.9184983372688293e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 5.7799168862402439e-03</internalNodes>
          <leafValues>
            4.0102362632751465e-02 -5.5016636848449707e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 1.6304963501170278e-03</internalNodes>
          <leafValues>
            -1.2154985219240189e-01 1.5898743271827698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 2.0045600831508636e-03</internalNodes>
          <leafValues>
            2.9790198430418968e-02 -5.8250021934509277e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 -2.0879322662949562e-02</internalNodes>
          <leafValues>
            2.1142369508743286e-01 -8.7382547557353973e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 954 -7.2442064993083477e-04</internalNodes>
          <leafValues>
            2.3263402283191681e-01 -8.1574723124504089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 971 -7.4541047215461731e-03</internalNodes>
          <leafValues>
            -4.5152980089187622e-01 4.5829907059669495e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 -8.5284758824855089e-04</internalNodes>
          <leafValues>
            2.1253970265388489e-01 -9.1697148978710175e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 2.0642249728552997e-04</internalNodes>
          <leafValues>
            -1.3943105936050415e-01 1.3452273607254028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 922 3.2144919969141483e-03</internalNodes>
          <leafValues>
            2.8089782223105431e-02 -6.4253503084182739e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 964 -4.6502160839736462e-03</internalNodes>
          <leafValues>
            2.4156840145587921e-01 -7.5992465019226074e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 -7.9781133681535721e-03</internalNodes>
          <leafValues>
            -6.4584964513778687e-01 2.7661839500069618e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 535 5.7130996137857437e-03</internalNodes>
          <leafValues>
            3.4437701106071472e-02 -4.5668947696685791e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 5.4299971088767052e-03</internalNodes>
          <leafValues>
            -8.4557615220546722e-02 2.4553726613521576e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 949 2.3061740212142467e-03</internalNodes>
          <leafValues>
            3.6576978862285614e-02 -5.1891702413558960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 5.0157017540186644e-04</internalNodes>
          <leafValues>
            -1.1976727843284607e-01 1.6033935546875000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 525 -1.9801128655672073e-04</internalNodes>
          <leafValues>
            1.5492685139179230e-01 -1.1399404704570770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 840 2.1493299864232540e-03</internalNodes>
          <leafValues>
            -8.7143458425998688e-02 2.1080201864242554e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 -1.0518108028918505e-03</internalNodes>
          <leafValues>
            1.7875078320503235e-01 -9.7399607300758362e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 590 9.2383408918976784e-03</internalNodes>
          <leafValues>
            3.2961033284664154e-02 -5.5265057086944580e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 3.6674018949270248e-02</internalNodes>
          <leafValues>
            -5.6394163519144058e-02 3.2619351148605347e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 725 1.8489698413759470e-03</internalNodes>
          <leafValues>
            -7.7260658144950867e-02 2.3810040950775146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 -3.9793262258172035e-03</internalNodes>
          <leafValues>
            2.3499612510204315e-01 -8.8490329682826996e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 619 3.7299278192222118e-03</internalNodes>
          <leafValues>
            3.1517989933490753e-02 -6.3558888435363770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 916 4.9755964428186417e-03</internalNodes>
          <leafValues>
            2.8343016281723976e-02 -5.1388341188430786e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 2.8308688197284937e-03</internalNodes>
          <leafValues>
            -1.1687427759170532e-01 1.4075778424739838e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 -4.3399848043918610e-02</internalNodes>
          <leafValues>
            3.3840376138687134e-01 -6.5363220870494843e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 4.4767763465642929e-03</internalNodes>
          <leafValues>
            5.3890492767095566e-02 -3.8843661546707153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 1.4579682610929012e-03</internalNodes>
          <leafValues>
            -9.5170073211193085e-02 1.7093485593795776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 4.3643731623888016e-03</internalNodes>
          <leafValues>
            -1.0353569686412811e-01 1.9202291965484619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 -6.4208358526229858e-03</internalNodes>
          <leafValues>
            -5.2469170093536377e-01 3.1174579635262489e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 -1.0310811921954155e-03</internalNodes>
          <leafValues>
            2.1713955700397491e-01 -8.0816701054573059e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 924 -4.0359990671277046e-03</internalNodes>
          <leafValues>
            -5.2423858642578125e-01 3.4388832747936249e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 750 -5.9410361573100090e-03</internalNodes>
          <leafValues>
            2.1114565432071686e-01 -8.0375924706459045e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 -4.7730140388011932e-02</internalNodes>
          <leafValues>
            -6.6018968820571899e-01 2.6816543191671371e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 777 -6.1359764076769352e-03</internalNodes>
          <leafValues>
            2.6270267367362976e-01 -7.4918255209922791e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 -3.2268161885440350e-03</internalNodes>
          <leafValues>
            -3.3210110664367676e-01 5.3716354072093964e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 2.7645970694720745e-03</internalNodes>
          <leafValues>
            2.4829804897308350e-02 -6.3309198617935181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 460 6.1914063990116119e-03</internalNodes>
          <leafValues>
            -7.9355642199516296e-02 2.0996589958667755e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 -6.0850339941680431e-03</internalNodes>
          <leafValues>
            1.8700407445430756e-01 -9.2255704104900360e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 3.8954569026827812e-03</internalNodes>
          <leafValues>
            -6.2000993639230728e-02 2.9947289824485779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 7.9390443861484528e-03</internalNodes>
          <leafValues>
            -4.7935441136360168e-02 3.3055123686790466e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 -7.4474988505244255e-03</internalNodes>
          <leafValues>
            -2.7902829647064209e-01 5.9020437300205231e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.5058170557022095e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 764 6.6398456692695618e-03</internalNodes>
          <leafValues>
            1.5219502151012421e-01 7.0482629537582397e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 -7.4103027582168579e-03</internalNodes>
          <leafValues>
            4.4995731115341187e-01 -1.0949239879846573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 509 -1.2679899111390114e-02</internalNodes>
          <leafValues>
            3.1908708810806274e-01 -1.3895240426063538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 1.0303283110260963e-02</internalNodes>
          <leafValues>
            -1.7546384036540985e-01 2.9695376753807068e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 -4.7976471483707428e-02</internalNodes>
          <leafValues>
            4.4112482666969299e-01 -1.1231642961502075e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 3.2145732548087835e-03</internalNodes>
          <leafValues>
            -1.8380118906497955e-01 2.3316045105457306e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 793 2.4557339493185282e-03</internalNodes>
          <leafValues>
            -1.4323309063911438e-01 2.3558256030082703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 595 -5.2055031061172485e-02</internalNodes>
          <leafValues>
            -4.4221264123916626e-01 7.0444636046886444e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 7.7533419243991375e-03</internalNodes>
          <leafValues>
            5.6696638464927673e-02 -5.1518803834915161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 534 4.7272985102608800e-04</internalNodes>
          <leafValues>
            -1.5081474184989929e-01 1.8353472650051117e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 -1.7044700682163239e-02</internalNodes>
          <leafValues>
            -4.3840527534484863e-01 6.8837635219097137e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 883 -3.5762921907007694e-03</internalNodes>
          <leafValues>
            2.4851283431053162e-01 -1.0779865086078644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 839 -2.4369158782064915e-03</internalNodes>
          <leafValues>
            3.0688673257827759e-01 -8.9006565511226654e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 933 -3.6047215107828379e-03</internalNodes>
          <leafValues>
            2.7542102336883545e-01 -1.0634675621986389e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 -1.1534212157130241e-02</internalNodes>
          <leafValues>
            -5.5281609296798706e-01 4.9770243465900421e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 2.7643658686429262e-03</internalNodes>
          <leafValues>
            2.9803691431879997e-02 -7.3425543308258057e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 -2.6033269241452217e-03</internalNodes>
          <leafValues>
            -4.4093501567840576e-01 5.0419628620147705e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 4.8673897981643677e-02</internalNodes>
          <leafValues>
            5.8146391063928604e-02 -3.7859597802162170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 -4.4065229594707489e-03</internalNodes>
          <leafValues>
            -6.9404369592666626e-01 3.1734105199575424e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 973 1.6997805796563625e-03</internalNodes>
          <leafValues>
            3.6800261586904526e-02 -5.4090088605880737e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 1.8069827929139137e-03</internalNodes>
          <leafValues>
            -8.9325174689292908e-02 2.5629612803459167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 -4.4371248222887516e-03</internalNodes>
          <leafValues>
            2.3971243202686310e-01 -1.0280106216669083e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 973 -2.2968063130974770e-03</internalNodes>
          <leafValues>
            -5.8439761400222778e-01 4.1789893060922623e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 1.4388319104909897e-03</internalNodes>
          <leafValues>
            -1.2928913533687592e-01 1.6424950957298279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 685 -6.5411212854087353e-03</internalNodes>
          <leafValues>
            1.7306149005889893e-01 -1.3702909648418427e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 -9.8783060908317566e-02</internalNodes>
          <leafValues>
            4.2455860972404480e-01 -5.3996428847312927e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 533 -1.5466672182083130e-01</internalNodes>
          <leafValues>
            -4.6163687109947205e-01 5.4335389286279678e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 626 -2.3389626294374466e-03</internalNodes>
          <leafValues>
            -6.5777504444122314e-01 2.6686858385801315e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 5.0454838201403618e-03</internalNodes>
          <leafValues>
            4.9083609133958817e-02 -3.7508815526962280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 2.5655250996351242e-02</internalNodes>
          <leafValues>
            -5.0410125404596329e-02 4.6358433365821838e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 2.6888975407928228e-03</internalNodes>
          <leafValues>
            -8.7988443672657013e-02 2.4659486114978790e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 -3.9576226845383644e-03</internalNodes>
          <leafValues>
            -3.4467720985412598e-01 6.0443773865699768e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 3.9240214973688126e-03</internalNodes>
          <leafValues>
            -7.2165921330451965e-02 3.6404970288276672e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 1.3846965739503503e-03</internalNodes>
          <leafValues>
            5.7182963937520981e-02 -3.8552245497703552e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 945 -8.4817763417959213e-03</internalNodes>
          <leafValues>
            1.8708378076553345e-01 -1.1441195011138916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 944 2.6850812137126923e-03</internalNodes>
          <leafValues>
            3.1117379665374756e-02 -6.2151008844375610e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 1.8728687427937984e-03</internalNodes>
          <leafValues>
            2.9517510905861855e-02 -5.8275890350341797e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 -1.4214152470231056e-03</internalNodes>
          <leafValues>
            2.1085265278816223e-01 -8.4942653775215149e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 -1.4164925087243319e-03</internalNodes>
          <leafValues>
            -5.0255894660949707e-01 3.4562669694423676e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 572 2.7755820192396641e-03</internalNodes>
          <leafValues>
            -8.5956700146198273e-02 2.1839313209056854e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 517 6.0641965828835964e-03</internalNodes>
          <leafValues>
            3.4184314310550690e-02 -5.6067311763763428e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 -2.9663506429642439e-03</internalNodes>
          <leafValues>
            2.6388403773307800e-01 -7.4524797499179840e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 986 -1.7851786687970161e-02</internalNodes>
          <leafValues>
            -3.5538297891616821e-01 5.5470395833253860e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 -7.7814348042011261e-03</internalNodes>
          <leafValues>
            2.5654977560043335e-01 -7.7634811401367188e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 6.2794378027319908e-04</internalNodes>
          <leafValues>
            -1.1543263494968414e-01 1.7257589101791382e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 -2.0482162944972515e-03</internalNodes>
          <leafValues>
            2.0133562386035919e-01 -9.2291958630084991e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 852 -2.0528757013380527e-03</internalNodes>
          <leafValues>
            2.4054610729217529e-01 -9.5314949750900269e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 1.9264804432168603e-03</internalNodes>
          <leafValues>
            -8.8165275752544403e-02 2.1321870386600494e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 1.0841939598321915e-02</internalNodes>
          <leafValues>
            3.4955434501171112e-02 -5.3944343328475952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 -7.5648901984095573e-03</internalNodes>
          <leafValues>
            -7.1435016393661499e-01 2.2735377773642540e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -7.4579543434083462e-04</internalNodes>
          <leafValues>
            2.0245671272277832e-01 -9.3201741576194763e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 -1.2499685399234295e-03</internalNodes>
          <leafValues>
            1.4646218717098236e-01 -1.3543428480625153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 5.8323808480054140e-04</internalNodes>
          <leafValues>
            -9.3358881771564484e-02 2.0619191229343414e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 439 1.9810695201158524e-02</internalNodes>
          <leafValues>
            2.7015892788767815e-02 -7.5727492570877075e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 5.9376070275902748e-03</internalNodes>
          <leafValues>
            -1.2178353220224380e-01 1.6173928976058960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 1.5689490828663111e-03</internalNodes>
          <leafValues>
            3.1975947320461273e-02 -6.0493367910385132e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 575 2.5749076157808304e-03</internalNodes>
          <leafValues>
            -9.0526103973388672e-02 2.2666496038436890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 7.9413484781980515e-03</internalNodes>
          <leafValues>
            5.1273416727781296e-02 -3.9284336566925049e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 3.2074376940727234e-02</internalNodes>
          <leafValues>
            2.7649452909827232e-02 -6.5098905563354492e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 -4.8892917111515999e-03</internalNodes>
          <leafValues>
            -4.7998306155204773e-01 3.3806797116994858e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 475 -5.4039997048676014e-03</internalNodes>
          <leafValues>
            2.8928443789482117e-01 -6.7743733525276184e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 6.3710450194776058e-03</internalNodes>
          <leafValues>
            -9.2935405671596527e-02 1.9695936143398285e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 578 -1.8336625071242452e-03</internalNodes>
          <leafValues>
            -3.5492643713951111e-01 5.1711678504943848e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 917 2.1252036094665527e-03</internalNodes>
          <leafValues>
            2.0353749394416809e-02 -7.2820025682449341e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 1.1279534548521042e-02</internalNodes>
          <leafValues>
            2.5639204308390617e-02 -5.8714842796325684e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 -1.1507571488618851e-01</internalNodes>
          <leafValues>
            -7.1453052759170532e-01 2.0069327205419540e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 757 -1.9626193679869175e-03</internalNodes>
          <leafValues>
            1.6723833978176117e-01 -1.0802425444126129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 756 4.7044735401868820e-03</internalNodes>
          <leafValues>
            -5.0709329545497894e-02 3.6821383237838745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 951 1.8476420082151890e-03</internalNodes>
          <leafValues>
            2.8631281107664108e-02 -6.8395125865936279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 546 -5.4538771510124207e-03</internalNodes>
          <leafValues>
            -6.8269199132919312e-01 2.0947692915797234e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 -1.0074324905872345e-02</internalNodes>
          <leafValues>
            -5.0528079271316528e-01 2.8627410531044006e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 -3.2069636508822441e-03</internalNodes>
          <leafValues>
            1.8038518726825714e-01 -9.3303814530372620e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 3.5637444816529751e-03</internalNodes>
          <leafValues>
            -6.5284818410873413e-02 3.2510238885879517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 2.1060477010905743e-03</internalNodes>
          <leafValues>
            3.0474457889795303e-02 -5.9379291534423828e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 4.2934687808156013e-03</internalNodes>
          <leafValues>
            -7.1521542966365814e-02 2.4279323220252991e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 8.2498760893940926e-03</internalNodes>
          <leafValues>
            3.7760157138109207e-02 -4.5278856158256531e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 2.5047136005014181e-03</internalNodes>
          <leafValues>
            -7.9689919948577881e-02 2.0523649454116821e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 651 -1.7786353128030896e-03</internalNodes>
          <leafValues>
            -6.7214471101760864e-01 2.7108855545520782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 5.6851857900619507e-01</internalNodes>
          <leafValues>
            -2.9719989746809006e-02 6.2848883867263794e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -3.9951098151504993e-03</internalNodes>
          <leafValues>
            1.6902630031108856e-01 -9.6066430211067200e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 790 -1.1912260204553604e-02</internalNodes>
          <leafValues>
            1.8001109361648560e-01 -1.0152278095483780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -3.2837040722370148e-02</internalNodes>
          <leafValues>
            2.2790163755416870e-01 -7.5773715972900391e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 -9.5821768045425415e-03</internalNodes>
          <leafValues>
            2.8319683670997620e-01 -6.0501150786876678e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 584 -7.0154434069991112e-03</internalNodes>
          <leafValues>
            3.7711927294731140e-01 -4.5414235442876816e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 598 2.2930791601538658e-03</internalNodes>
          <leafValues>
            -9.7957342863082886e-02 1.8666461110115051e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 3.6181495524942875e-03</internalNodes>
          <leafValues>
            -8.5584357380867004e-02 2.1272744238376617e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 -3.5851418506354094e-03</internalNodes>
          <leafValues>
            -3.2166537642478943e-01 5.0518564879894257e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 736 2.0358990877866745e-03</internalNodes>
          <leafValues>
            -9.0195730328559875e-02 1.7850238084793091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 3.0855672433972359e-02</internalNodes>
          <leafValues>
            7.0852726697921753e-02 -2.3407098650932312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 543 -2.8650071471929550e-03</internalNodes>
          <leafValues>
            2.5149047374725342e-01 -7.0797137916088104e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 658 6.9189509376883507e-03</internalNodes>
          <leafValues>
            -7.6844424009323120e-02 2.2756692767143250e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 831 3.2940365374088287e-02</internalNodes>
          <leafValues>
            -6.0664962977170944e-02 2.7875399589538574e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 8.9268945157527924e-03</internalNodes>
          <leafValues>
            -8.5170723497867584e-02 2.3205895721912384e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 -7.8886147821322083e-04</internalNodes>
          <leafValues>
            -2.9130771756172180e-01 6.2974251806735992e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -5.6759864091873169e-03</internalNodes>
          <leafValues>
            -4.1244068741798401e-01 3.6539580672979355e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 1.2777841184288263e-03</internalNodes>
          <leafValues>
            -8.5783556103706360e-02 1.8866100907325745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 3.7156887352466583e-02</internalNodes>
          <leafValues>
            -1.0894140601158142e-01 1.4975252747535706e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 -5.7424330711364746e-01</internalNodes>
          <leafValues>
            7.2750979661941528e-01 -2.4812746793031693e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 -1.3634916394948959e-03</internalNodes>
          <leafValues>
            2.2405619919300079e-01 -6.9666981697082520e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 3.3075414597988129e-02</internalNodes>
          <leafValues>
            -5.6832231581211090e-02 3.0118697881698608e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.5318367481231689e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 770 -2.2907990496605635e-03</internalNodes>
          <leafValues>
            5.9861046075820923e-01 8.8971912860870361e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 3.1865492928773165e-03</internalNodes>
          <leafValues>
            -1.5604956448078156e-01 3.8612595200538635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 561 -8.0397585406899452e-03</internalNodes>
          <leafValues>
            2.8996115922927856e-01 -1.8175528943538666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 850 4.3595694005489349e-03</internalNodes>
          <leafValues>
            -8.0427564680576324e-02 5.0874835252761841e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 2.8643116354942322e-02</internalNodes>
          <leafValues>
            -1.7203453183174133e-01 3.2913634181022644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 -3.1282915733754635e-03</internalNodes>
          <leafValues>
            3.7550333142280579e-01 -9.1347120702266693e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 778 1.4058926608413458e-03</internalNodes>
          <leafValues>
            -1.9007267057895660e-01 1.6663856804370880e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 1.0209476575255394e-02</internalNodes>
          <leafValues>
            -1.2146373838186264e-01 2.3719595372676849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 6.0191084630787373e-03</internalNodes>
          <leafValues>
            5.1051452755928040e-02 -5.5820345878601074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -4.7453744336962700e-03</internalNodes>
          <leafValues>
            1.6992042958736420e-01 -1.6199907660484314e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 1.5875545796006918e-03</internalNodes>
          <leafValues>
            -1.0143589228391647e-01 2.6255446672439575e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 -2.7861427515745163e-03</internalNodes>
          <leafValues>
            -4.0354993939399719e-01 6.7644119262695312e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 4.0722191333770752e-03</internalNodes>
          <leafValues>
            4.1441403329372406e-02 -5.5184590816497803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 953 1.0626179864630103e-03</internalNodes>
          <leafValues>
            -1.4222721755504608e-01 1.7192882299423218e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 4.7259610146284103e-03</internalNodes>
          <leafValues>
            5.1823709160089493e-02 -3.9256933331489563e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 -2.6500346139073372e-03</internalNodes>
          <leafValues>
            -3.5642188787460327e-01 6.6784784197807312e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 3.9345081895589828e-03</internalNodes>
          <leafValues>
            -6.1840496957302094e-02 3.9278426766395569e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 8.9293124619871378e-04</internalNodes>
          <leafValues>
            -1.0518563538789749e-01 2.3271512985229492e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 5.7920545339584351e-02</internalNodes>
          <leafValues>
            -6.8776324391365051e-02 3.3801963925361633e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 529 -6.6577367484569550e-02</internalNodes>
          <leafValues>
            4.2277967929840088e-01 -6.3239939510822296e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 -5.5271089076995850e-03</internalNodes>
          <leafValues>
            -4.1861197352409363e-01 6.0344558209180832e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 3.3537389244884253e-03</internalNodes>
          <leafValues>
            -1.5910768508911133e-01 1.7853063344955444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 -2.3959013633430004e-03</internalNodes>
          <leafValues>
            2.5072932243347168e-01 -8.5364922881126404e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 -2.1867034956812859e-03</internalNodes>
          <leafValues>
            -5.6374865770339966e-01 3.5474341362714767e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 602 2.4597872979938984e-03</internalNodes>
          <leafValues>
            3.1019955873489380e-02 -5.5415368080139160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 577 -2.9149088077247143e-03</internalNodes>
          <leafValues>
            -4.6299308538436890e-01 3.7638399749994278e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 905 -1.5585927758365870e-03</internalNodes>
          <leafValues>
            1.4355151355266571e-01 -1.3060651719570160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 -3.1853761523962021e-02</internalNodes>
          <leafValues>
            -4.6954944729804993e-01 3.7520393729209900e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 472 -5.1449546590447426e-03</internalNodes>
          <leafValues>
            2.7591976523399353e-01 -6.6930308938026428e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 -1.7139092087745667e-03</internalNodes>
          <leafValues>
            -3.8028663396835327e-01 4.9521040171384811e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 -1.0931883752346039e-01</internalNodes>
          <leafValues>
            -4.1068795323371887e-01 4.0003888309001923e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 -2.5099035352468491e-02</internalNodes>
          <leafValues>
            4.3478006124496460e-01 -5.5282033979892731e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 435 8.1472359597682953e-03</internalNodes>
          <leafValues>
            -4.5095365494489670e-02 3.9842218160629272e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 -1.9677784293889999e-03</internalNodes>
          <leafValues>
            1.8696348369121552e-01 -1.1214685440063477e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 -2.6076552458107471e-03</internalNodes>
          <leafValues>
            2.2791831195354462e-01 -8.7665997445583344e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 490 -4.1761510074138641e-03</internalNodes>
          <leafValues>
            -6.8348854780197144e-01 2.8925698250532150e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 774 1.3159511610865593e-02</internalNodes>
          <leafValues>
            2.1792927756905556e-02 -7.0727092027664185e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 548 -5.1895831711590290e-03</internalNodes>
          <leafValues>
            -6.6959971189498901e-01 2.1171124652028084e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 -1.5662111341953278e-02</internalNodes>
          <leafValues>
            -3.6895245313644409e-01 4.1785925626754761e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 -2.4020818527787924e-03</internalNodes>
          <leafValues>
            1.7773237824440002e-01 -1.0461435467004776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 631 1.9489541649818420e-02</internalNodes>
          <leafValues>
            3.1142184510827065e-02 -5.4499447345733643e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 4.9672335386276245e-01</internalNodes>
          <leafValues>
            -2.4833207949995995e-02 7.6724356412887573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 3.2806373201310635e-03</internalNodes>
          <leafValues>
            -6.8504363298416138e-02 2.2050221264362335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 638 2.1590515971183777e-03</internalNodes>
          <leafValues>
            2.5587400421500206e-02 -6.2128585577011108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 604 5.2638887427747250e-04</internalNodes>
          <leafValues>
            -9.3414209783077240e-02 1.7369781434535980e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 510 -2.1490573417395353e-03</internalNodes>
          <leafValues>
            -4.4394543766975403e-01 3.9975218474864960e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 1.9845911301672459e-03</internalNodes>
          <leafValues>
            -8.5414819419384003e-02 2.0132684707641602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 -3.2773464918136597e-03</internalNodes>
          <leafValues>
            2.4408425390720367e-01 -7.3116399347782135e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 523 2.1245577372610569e-03</internalNodes>
          <leafValues>
            2.9403384774923325e-02 -5.7607394456863403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 1.4500851975753903e-03</internalNodes>
          <leafValues>
            -6.6218085587024689e-02 2.4264883995056152e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 -4.5542549341917038e-03</internalNodes>
          <leafValues>
            -5.8058720827102661e-01 2.8668973594903946e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 4.7045715153217316e-02</internalNodes>
          <leafValues>
            3.6376014351844788e-02 -4.1573047637939453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 7.7028926461935043e-03</internalNodes>
          <leafValues>
            -7.7403679490089417e-02 2.1597269177436829e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 -4.1527286171913147e-02</internalNodes>
          <leafValues>
            -3.4835410118103027e-01 4.8530772328376770e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 748 -9.1361545491963625e-04</internalNodes>
          <leafValues>
            1.8357329070568085e-01 -9.0061083436012268e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 -5.0414498895406723e-02</internalNodes>
          <leafValues>
            2.5009948015213013e-01 -6.4314760267734528e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 398 7.4737053364515305e-03</internalNodes>
          <leafValues>
            -5.7431813329458237e-02 2.8318390250205994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 654 2.3173280060291290e-03</internalNodes>
          <leafValues>
            3.7970397621393204e-02 -5.6861978769302368e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 4.5855477219447494e-04</internalNodes>
          <leafValues>
            -1.3554716110229492e-01 1.1426483094692230e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 1.8236173782497644e-03</internalNodes>
          <leafValues>
            2.6509260758757591e-02 -5.5975830554962158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 732 -1.3565555214881897e-02</internalNodes>
          <leafValues>
            3.5292169451713562e-01 -4.7534435987472534e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 -1.2756066862493753e-03</internalNodes>
          <leafValues>
            2.5920400023460388e-01 -5.7072717696428299e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 6.7418040707707405e-03</internalNodes>
          <leafValues>
            3.2604463398456573e-02 -4.5861816406250000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 827 6.5548229031264782e-04</internalNodes>
          <leafValues>
            -8.4788605570793152e-02 1.8131427466869354e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 2.1027602255344391e-02</internalNodes>
          <leafValues>
            -6.9927647709846497e-02 2.4611653387546539e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 695 1.8613610416650772e-02</internalNodes>
          <leafValues>
            -3.6128688603639603e-02 4.4170859456062317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 -4.9805632233619690e-01</internalNodes>
          <leafValues>
            5.9478044509887695e-01 -2.6719097048044205e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 620 -3.2723334152251482e-03</internalNodes>
          <leafValues>
            2.2165246307849884e-01 -6.8788610398769379e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 583 -3.5375617444515228e-03</internalNodes>
          <leafValues>
            -6.1987191438674927e-01 2.6597078889608383e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 632 3.9524696767330170e-03</internalNodes>
          <leafValues>
            -7.5478851795196533e-02 2.1777774393558502e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 681 -9.3436334282159805e-03</internalNodes>
          <leafValues>
            -8.0256491899490356e-01 2.2864339873194695e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 671 -1.0151248425245285e-03</internalNodes>
          <leafValues>
            -3.1884235143661499e-01 4.3938383460044861e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 516 -7.0278989151120186e-03</internalNodes>
          <leafValues>
            1.4721503853797913e-01 -1.1400235444307327e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 611 -9.1165518388152122e-03</internalNodes>
          <leafValues>
            2.6830977201461792e-01 -6.4460486173629761e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 3.3993504941463470e-02</internalNodes>
          <leafValues>
            2.7748649939894676e-02 -5.5171352624893188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 557 -2.6347138918936253e-03</internalNodes>
          <leafValues>
            1.7853704094886780e-01 -8.4178321063518524e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 -1.3291095383465290e-03</internalNodes>
          <leafValues>
            -2.8526228666305542e-01 5.6452732533216476e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 719 1.2167419772595167e-03</internalNodes>
          <leafValues>
            -7.7132880687713623e-02 1.9538262486457825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 856 1.9806078635156155e-03</internalNodes>
          <leafValues>
            2.6976436376571655e-02 -5.4301941394805908e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 617 5.0783145707100630e-04</internalNodes>
          <leafValues>
            -9.5295064151287079e-02 1.5398529171943665e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 -5.0868269056081772e-02</internalNodes>
          <leafValues>
            -6.3152486085891724e-01 2.3585954681038857e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -1.4806092949584126e-03</internalNodes>
          <leafValues>
            2.6132494211196899e-01 -6.3322558999061584e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -1.8584117293357849e-02</internalNodes>
          <leafValues>
            1.6097819805145264e-01 -1.1777820438146591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 250 2.7171591296792030e-02</internalNodes>
          <leafValues>
            -8.3719044923782349e-02 1.9814659655094147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -6.8197408691048622e-03</internalNodes>
          <leafValues>
            -5.4431658983230591e-01 2.8722483664751053e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 696 -9.1905370354652405e-03</internalNodes>
          <leafValues>
            1.5037034451961517e-01 -1.0166583955287933e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 -9.7952038049697876e-04</internalNodes>
          <leafValues>
            -2.9014238715171814e-01 4.9301091581583023e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 -2.1513784304261208e-03</internalNodes>
          <leafValues>
            2.4622270464897156e-01 -6.0794923454523087e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 -2.0869732834398746e-03</internalNodes>
          <leafValues>
            2.0386496186256409e-01 -8.8516488671302795e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 -1.2946429662406445e-02</internalNodes>
          <leafValues>
            -5.2757191658020020e-01 2.9759777709841728e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 985 -3.5145760048180819e-03</internalNodes>
          <leafValues>
            -4.7929930686950684e-01 2.8995612636208534e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 2.4212854914367199e-03</internalNodes>
          <leafValues>
            -6.4840331673622131e-02 2.4126507341861725e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 2.1736249327659607e-03</internalNodes>
          <leafValues>
            2.2003039717674255e-02 -7.0873755216598511e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 -3.5567809827625751e-03</internalNodes>
          <leafValues>
            2.9770645499229431e-01 -5.5486857891082764e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 -1.9639974460005760e-02</internalNodes>
          <leafValues>
            -7.8722274303436279e-01 2.1807981655001640e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 653 8.1145912408828735e-03</internalNodes>
          <leafValues>
            2.0471598953008652e-02 -5.9867942333221436e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 706 1.2241180054843426e-02</internalNodes>
          <leafValues>
            1.6892330721020699e-02 -7.4894833564758301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 794 3.2565161585807800e-02</internalNodes>
          <leafValues>
            1.8086636438965797e-02 -6.3382810354232788e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 498 1.2451345100998878e-02</internalNodes>
          <leafValues>
            -6.6715493798255920e-02 2.1516454219818115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 -2.9989825561642647e-02</internalNodes>
          <leafValues>
            2.0229698717594147e-01 -6.8314045667648315e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.6178516149520874e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 499 -3.4503117203712463e-03</internalNodes>
          <leafValues>
            6.1788332462310791e-01 1.1437324434518814e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 4.8226695507764816e-03</internalNodes>
          <leafValues>
            -1.0551112145185471e-01 4.8962607979774475e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 772 5.1433052867650986e-03</internalNodes>
          <leafValues>
            -1.1823723465204239e-01 4.1509538888931274e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 786 1.8649311736226082e-02</internalNodes>
          <leafValues>
            -1.4054079353809357e-01 4.4154295325279236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 -2.9543964192271233e-03</internalNodes>
          <leafValues>
            2.5874784588813782e-01 -1.4265626668930054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 3.1133145093917847e-03</internalNodes>
          <leafValues>
            -8.1695765256881714e-02 3.6735343933105469e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 -3.0071800574660301e-03</internalNodes>
          <leafValues>
            -3.8455182313919067e-01 7.1067951619625092e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 -1.9453115761280060e-02</internalNodes>
          <leafValues>
            2.1075683832168579e-01 -1.2884819507598877e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 -4.8584998585283756e-03</internalNodes>
          <leafValues>
            1.6113398969173431e-01 -1.7580857872962952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 8.2634367048740387e-02</internalNodes>
          <leafValues>
            -5.9177018702030182e-02 4.0204021334648132e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 4.5589236542582512e-03</internalNodes>
          <leafValues>
            -1.5169224143028259e-01 2.1497711539268494e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 5.7522351853549480e-03</internalNodes>
          <leafValues>
            -1.0400034487247467e-01 2.3692265152931213e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 1.0916464030742645e-02</internalNodes>
          <leafValues>
            3.2482013106346130e-02 -6.6799944639205933e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 1.2513624969869852e-03</internalNodes>
          <leafValues>
            -9.2966683208942413e-02 2.5203207135200500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 8.5552372038364410e-03</internalNodes>
          <leafValues>
            4.2838018387556076e-02 -5.4950177669525146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 1.9793706014752388e-03</internalNodes>
          <leafValues>
            -1.0328737646341324e-01 2.2137698531150818e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 -2.3129612207412720e-02</internalNodes>
          <leafValues>
            -5.9918802976608276e-01 3.7120997905731201e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 -2.5169795844703913e-03</internalNodes>
          <leafValues>
            -4.0429180860519409e-01 4.7458905726671219e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 -2.7680140919983387e-03</internalNodes>
          <leafValues>
            -4.1959136724472046e-01 4.7375876456499100e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 4.4881463982164860e-03</internalNodes>
          <leafValues>
            -5.3955338895320892e-02 4.0468615293502808e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 1.1571742361411452e-03</internalNodes>
          <leafValues>
            6.1304513365030289e-02 -3.6215540766716003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 3.6775614134967327e-03</internalNodes>
          <leafValues>
            3.5641182214021683e-02 -5.1911950111389160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 2.4001342244446278e-03</internalNodes>
          <leafValues>
            -8.0022528767585754e-02 2.6706510782241821e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 823 -1.4416232006624341e-03</internalNodes>
          <leafValues>
            2.5601235032081604e-01 -8.7470635771751404e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 5.0148535519838333e-03</internalNodes>
          <leafValues>
            5.7711403816938400e-02 -3.5666841268539429e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 2.9201959259808064e-03</internalNodes>
          <leafValues>
            -7.3214575648307800e-02 2.7563962340354919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 -2.8012858820147812e-05</internalNodes>
          <leafValues>
            1.3044390082359314e-01 -1.5220497548580170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 962 2.6261559687554836e-03</internalNodes>
          <leafValues>
            6.3371658325195312e-02 -2.9989096522331238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 -1.9985539838671684e-02</internalNodes>
          <leafValues>
            -7.0204716920852661e-01 2.5756308808922768e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 4.0448461659252644e-03</internalNodes>
          <leafValues>
            -9.9051415920257568e-02 2.0554924011230469e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 7.6400241814553738e-03</internalNodes>
          <leafValues>
            4.8610400408506393e-02 -4.0662041306495667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 252 1.5262419357895851e-02</internalNodes>
          <leafValues>
            -9.2194251716136932e-02 2.0623819530010223e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 -6.1596641317009926e-03</internalNodes>
          <leafValues>
            -5.0260418653488159e-01 3.7245232611894608e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 785 -2.5804866105318069e-02</internalNodes>
          <leafValues>
            -5.2190864086151123e-01 3.0063979327678680e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 -4.6107484959065914e-03</internalNodes>
          <leafValues>
            -4.2041480541229248e-01 3.7473026663064957e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 -1.6811741515994072e-03</internalNodes>
          <leafValues>
            3.3520191907882690e-01 -5.3664822131395340e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 1.0210975306108594e-03</internalNodes>
          <leafValues>
            -7.0165649056434631e-02 2.7230393886566162e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 -2.7257478795945644e-03</internalNodes>
          <leafValues>
            -3.7600108981132507e-01 4.8897936940193176e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 1.7022072570398450e-03</internalNodes>
          <leafValues>
            3.2578211277723312e-02 -4.8195156455039978e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 2.0580790005624294e-03</internalNodes>
          <leafValues>
            -1.1316970735788345e-01 1.4754198491573334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 520 1.9031974952667952e-03</internalNodes>
          <leafValues>
            6.1289772391319275e-02 -2.7776253223419189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 -4.5437026768922806e-02</internalNodes>
          <leafValues>
            2.8187385201454163e-01 -6.1310045421123505e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 -2.0433391630649567e-01</internalNodes>
          <leafValues>
            -4.8491853475570679e-01 3.7197910249233246e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 902 -2.2007026709616184e-03</internalNodes>
          <leafValues>
            -4.8433649539947510e-01 2.8523173183202744e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 506 2.4706670083105564e-03</internalNodes>
          <leafValues>
            -8.0774910748004913e-02 2.0636586844921112e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 337 -1.1496900115162134e-03</internalNodes>
          <leafValues>
            2.0466096699237823e-01 -7.8325189650058746e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -4.3232389725744724e-03</internalNodes>
          <leafValues>
            -3.4593367576599121e-01 4.9537312239408493e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 -9.3772150576114655e-03</internalNodes>
          <leafValues>
            -5.5802655220031738e-01 2.5828598067164421e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 -1.6863006167113781e-03</internalNodes>
          <leafValues>
            1.9700750708580017e-01 -8.0926463007926941e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 5.5908015929162502e-03</internalNodes>
          <leafValues>
            -5.8355998247861862e-02 3.0854061245918274e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 483 -4.9319159006699920e-04</internalNodes>
          <leafValues>
            1.3386693596839905e-01 -1.1287388950586319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 -5.3560961037874222e-02</internalNodes>
          <leafValues>
            3.3912947773933411e-01 -4.4598836451768875e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 -2.4220649152994156e-02</internalNodes>
          <leafValues>
            -4.5232787728309631e-01 4.1364260017871857e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 798 1.2709838338196278e-03</internalNodes>
          <leafValues>
            -8.8080756366252899e-02 1.8180713057518005e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 -4.4361655600368977e-03</internalNodes>
          <leafValues>
            -4.2694598436355591e-01 3.6063931882381439e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -6.5719988197088242e-04</internalNodes>
          <leafValues>
            1.8804629147052765e-01 -8.5146181285381317e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 1.8579278141260147e-02</internalNodes>
          <leafValues>
            4.4604945927858353e-02 -3.7216106057167053e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 857 3.0188630335032940e-03</internalNodes>
          <leafValues>
            -9.7823068499565125e-02 1.5584464371204376e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 868 1.6309421043843031e-03</internalNodes>
          <leafValues>
            3.5910408943891525e-02 -4.3541318178176880e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 1.3791467994451523e-02</internalNodes>
          <leafValues>
            1.7177715897560120e-02 -7.7653616666793823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 1.0393885895609856e-03</internalNodes>
          <leafValues>
            -1.2292464822530746e-01 1.1997509002685547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 417 -1.8992213299497962e-03</internalNodes>
          <leafValues>
            -5.8449220657348633e-01 2.3935828357934952e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 -4.4655447709374130e-04</internalNodes>
          <leafValues>
            1.8246568739414215e-01 -8.1576324999332428e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 1.1696915607899427e-03</internalNodes>
          <leafValues>
            4.1298836469650269e-02 -3.7100258469581604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 906 -8.9874223340302706e-04</internalNodes>
          <leafValues>
            1.3932932913303375e-01 -1.0641934722661972e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 862 5.9534879401326180e-03</internalNodes>
          <leafValues>
            2.6781413704156876e-02 -6.1212611198425293e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 861 -8.0954860895872116e-03</internalNodes>
          <leafValues>
            2.6603493094444275e-01 -5.9750139713287354e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -8.2374701742082834e-04</internalNodes>
          <leafValues>
            2.1638387441635132e-01 -6.4249947667121887e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 -7.6250307029113173e-04</internalNodes>
          <leafValues>
            2.0882584154605865e-01 -8.5345618426799774e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 959 2.3917213547974825e-03</internalNodes>
          <leafValues>
            2.9081748798489571e-02 -5.5320137739181519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 957 -5.6062731891870499e-03</internalNodes>
          <leafValues>
            -3.1231331825256348e-01 4.6577330678701401e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 807 1.2068089097738266e-02</internalNodes>
          <leafValues>
            -6.9983117282390594e-02 2.1360129117965698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 664 -6.1361752450466156e-03</internalNodes>
          <leafValues>
            1.5866123139858246e-01 -8.9951172471046448e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 2.7342736721038818e-03</internalNodes>
          <leafValues>
            4.6424146741628647e-02 -3.2302507758140564e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 2.5015277788043022e-02</internalNodes>
          <leafValues>
            -9.2339992523193359e-02 1.6995115578174591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 3.6676183342933655e-02</internalNodes>
          <leafValues>
            -8.1868082284927368e-02 2.0542381703853607e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 2.9560746625065804e-03</internalNodes>
          <leafValues>
            4.2714115232229233e-02 -3.9473703503608704e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 -5.4626376368105412e-04</internalNodes>
          <leafValues>
            1.7010760307312012e-01 -9.1078221797943115e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 479 3.5485455300658941e-03</internalNodes>
          <leafValues>
            -7.2080396115779877e-02 2.2900597751140594e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 485 -3.1973507720977068e-03</internalNodes>
          <leafValues>
            2.0531810820102692e-01 -8.5912480950355530e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 867 -2.5095739401876926e-03</internalNodes>
          <leafValues>
            -3.3782237768173218e-01 5.3629480302333832e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 1.2365024536848068e-02</internalNodes>
          <leafValues>
            2.7145428583025932e-02 -5.5763113498687744e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 2.0736612379550934e-02</internalNodes>
          <leafValues>
            -6.0063906013965607e-02 2.5700190663337708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 2.6504596695303917e-02</internalNodes>
          <leafValues>
            3.4878112375736237e-02 -4.7980275750160217e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 976 -9.5798689872026443e-03</internalNodes>
          <leafValues>
            -6.1055964231491089e-01 2.1064205095171928e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 1.9744917750358582e-02</internalNodes>
          <leafValues>
            -4.9393177032470703e-02 3.0676594376564026e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 865 3.0523580498993397e-03</internalNodes>
          <leafValues>
            4.1798073798418045e-02 -3.5442468523979187e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 612 -4.3955976143479347e-03</internalNodes>
          <leafValues>
            2.5482681393623352e-01 -5.6209251284599304e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 469 2.2425290662795305e-03</internalNodes>
          <leafValues>
            -9.7824580967426300e-02 1.7274166643619537e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 541 7.1967259049415588e-02</internalNodes>
          <leafValues>
            -3.9488561451435089e-02 3.6034339666366577e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 8.4581580013036728e-03</internalNodes>
          <leafValues>
            3.5755772143602371e-02 -4.4762039184570312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 665 3.3080112189054489e-03</internalNodes>
          <leafValues>
            2.2785754874348640e-02 -5.3823727369308472e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 437 -1.1890231398865581e-03</internalNodes>
          <leafValues>
            1.7143265902996063e-01 -8.1015840172767639e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 -1.7523975111544132e-03</internalNodes>
          <leafValues>
            2.5996673107147217e-01 -6.9269210100173950e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 5.4229743545874953e-04</internalNodes>
          <leafValues>
            -7.4078343808650970e-02 2.0903676748275757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 963 3.9791758172214031e-03</internalNodes>
          <leafValues>
            4.0985044091939926e-02 -3.6837655305862427e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 829 -6.9316523149609566e-03</internalNodes>
          <leafValues>
            -4.1581609845161438e-01 3.2475329935550690e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 4.1018679738044739e-02</internalNodes>
          <leafValues>
            -6.6409081220626831e-02 2.3296032845973969e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 3.3051387872546911e-03</internalNodes>
          <leafValues>
            -8.2739837467670441e-02 1.9939082860946655e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 -1.3641032390296459e-02</internalNodes>
          <leafValues>
            1.6623613238334656e-01 -8.6717613041400909e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.6119387149810791e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 508 -1.0795817710459232e-02</internalNodes>
          <leafValues>
            5.7589554786682129e-01 9.1795757412910461e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 -1.4518407406285405e-03</internalNodes>
          <leafValues>
            3.8668358325958252e-01 -1.4567533135414124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 1.2619893066585064e-02</internalNodes>
          <leafValues>
            -1.3285328447818756e-01 3.8423144817352295e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 -8.6567001417279243e-03</internalNodes>
          <leafValues>
            2.0455244183540344e-01 -1.9482232630252838e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 8.7269591167569160e-03</internalNodes>
          <leafValues>
            -9.0128563344478607e-02 4.0668380260467529e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 846 -4.8108389601111412e-03</internalNodes>
          <leafValues>
            4.0858918428421021e-01 -7.6686508953571320e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 441 2.7277414119453169e-05</internalNodes>
          <leafValues>
            -2.1661256253719330e-01 1.3865883648395538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 -9.6549151930958033e-04</internalNodes>
          <leafValues>
            1.9036890566349030e-01 -1.3512735068798065e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 -1.1629101354628801e-03</internalNodes>
          <leafValues>
            2.7597144246101379e-01 -8.5875771939754486e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 708 3.2347193919122219e-03</internalNodes>
          <leafValues>
            -1.4792887866497040e-01 1.6230462491512299e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 -6.0261571779847145e-03</internalNodes>
          <leafValues>
            -5.2146345376968384e-01 3.9669245481491089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 502 3.7499368190765381e-03</internalNodes>
          <leafValues>
            4.7719169408082962e-02 -4.2560356855392456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 432 -2.2638911381363869e-02</internalNodes>
          <leafValues>
            2.7776387333869934e-01 -8.2894414663314819e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 2.8850757516920567e-03</internalNodes>
          <leafValues>
            -7.3187254369258881e-02 3.4045669436454773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 743 9.2617822811007500e-03</internalNodes>
          <leafValues>
            -6.1159532517194748e-02 3.4422287344932556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 419 4.7564059495925903e-03</internalNodes>
          <leafValues>
            4.2927626520395279e-02 -5.0712525844573975e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 588 1.0375332832336426e-01</internalNodes>
          <leafValues>
            3.7820540368556976e-02 -5.4858410358428955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 -1.1948650702834129e-02</internalNodes>
          <leafValues>
            3.0298843979835510e-01 -7.0214085280895233e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 1.7851736396551132e-02</internalNodes>
          <leafValues>
            -8.9291095733642578e-02 2.2659333050251007e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 928 1.7700281459838152e-03</internalNodes>
          <leafValues>
            -9.0894356369972229e-02 2.3938187956809998e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 1.0000608861446381e-02</internalNodes>
          <leafValues>
            5.4091196507215500e-02 -4.5715424418449402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 5.1841903477907181e-03</internalNodes>
          <leafValues>
            4.4081535190343857e-02 -4.0113139152526855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 1.9869320094585419e-03</internalNodes>
          <leafValues>
            -8.6456976830959320e-02 2.3278492689132690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 7.3318472132086754e-03</internalNodes>
          <leafValues>
            4.4677142053842545e-02 -4.3628835678100586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 5.3855171427130699e-03</internalNodes>
          <leafValues>
            3.1241770833730698e-02 -5.7641702890396118e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -5.8841239660978317e-04</internalNodes>
          <leafValues>
            2.1748071908950806e-01 -9.8720036447048187e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 -4.6296482905745506e-03</internalNodes>
          <leafValues>
            -5.0439667701721191e-01 3.9307218044996262e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 1.0425396263599396e-02</internalNodes>
          <leafValues>
            -6.9303810596466064e-02 2.8114342689514160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 821 3.4709304571151733e-02</internalNodes>
          <leafValues>
            -4.4065892696380615e-02 4.7260922193527222e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 -1.9787646830081940e-02</internalNodes>
          <leafValues>
            -6.3054060935974121e-01 3.7138473242521286e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 832 -1.0857213288545609e-02</internalNodes>
          <leafValues>
            -3.4433662891387939e-01 4.5778658241033554e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 -3.3750114962458611e-03</internalNodes>
          <leafValues>
            -4.4760662317276001e-01 3.7368919700384140e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 7.1516213938593864e-04</internalNodes>
          <leafValues>
            -1.4026457071304321e-01 1.2475384026765823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 694 -3.0070471111685038e-03</internalNodes>
          <leafValues>
            -5.2588617801666260e-01 3.0897416174411774e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 634 -3.0869825277477503e-03</internalNodes>
          <leafValues>
            2.8596574068069458e-01 -6.7343741655349731e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 -4.3112646788358688e-02</internalNodes>
          <leafValues>
            -7.0135027170181274e-01 2.6632267981767654e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 -1.2020026333630085e-03</internalNodes>
          <leafValues>
            -3.8874247670173645e-01 4.1288472712039948e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 716 -3.8164458237588406e-04</internalNodes>
          <leafValues>
            1.3130629062652588e-01 -1.3220198452472687e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 -4.4994866475462914e-03</internalNodes>
          <leafValues>
            -2.8277575969696045e-01 6.8065464496612549e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 586 -4.2400006204843521e-03</internalNodes>
          <leafValues>
            -5.7234168052673340e-01 2.4768881499767303e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 610 3.3328742720186710e-03</internalNodes>
          <leafValues>
            -6.9700233638286591e-02 2.3259970545768738e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 -1.4152936637401581e-02</internalNodes>
          <leafValues>
            -6.5485191345214844e-01 2.5028359144926071e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 -3.3239413052797318e-02</internalNodes>
          <leafValues>
            2.1122130751609802e-01 -8.0384172499179840e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 3.9529884234070778e-03</internalNodes>
          <leafValues>
            -7.4974447488784790e-02 2.7394378185272217e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 2.0498572848737240e-03</internalNodes>
          <leafValues>
            3.5124473273754120e-02 -5.0805884599685669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 1.3978367205709219e-03</internalNodes>
          <leafValues>
            -8.5583955049514771e-02 1.9296622276306152e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 983 3.1700683757662773e-03</internalNodes>
          <leafValues>
            4.6254437416791916e-02 -3.5503390431404114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 -1.0263657895848155e-03</internalNodes>
          <leafValues>
            1.3199952244758606e-01 -1.2064760923385620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 -4.1109144687652588e-02</internalNodes>
          <leafValues>
            1.7420990765094757e-01 -9.8242506384849548e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 618 1.5759501606225967e-02</internalNodes>
          <leafValues>
            -7.5842045247554779e-02 2.3157498240470886e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 -9.5934671116992831e-04</internalNodes>
          <leafValues>
            1.8444137275218964e-01 -9.2052407562732697e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 864 5.4162740707397461e-03</internalNodes>
          <leafValues>
            3.1357165426015854e-02 -5.3519624471664429e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 3.4875022247433662e-03</internalNodes>
          <leafValues>
            4.8432532697916031e-02 -3.3630362153053284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 660 1.3441047631204128e-03</internalNodes>
          <leafValues>
            -8.3214677870273590e-02 2.0162117481231689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 684 -2.3109107278287411e-03</internalNodes>
          <leafValues>
            1.8354012072086334e-01 -8.8427804410457611e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 481 5.1613273099064827e-03</internalNodes>
          <leafValues>
            -6.8671047687530518e-02 2.2440080344676971e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 -2.3844663053750992e-02</internalNodes>
          <leafValues>
            -6.2796258926391602e-01 2.7813719585537910e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 421 3.7013965193182230e-03</internalNodes>
          <leafValues>
            -6.7407652735710144e-02 2.7093955874443054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 633 3.9885155856609344e-03</internalNodes>
          <leafValues>
            3.4067343920469284e-02 -5.4420226812362671e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 1.8910076469182968e-02</internalNodes>
          <leafValues>
            4.2769759893417358e-02 -3.3686736226081848e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 808 1.2143498286604881e-02</internalNodes>
          <leafValues>
            1.9569551572203636e-02 -7.1214914321899414e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 -5.6570172309875488e-03</internalNodes>
          <leafValues>
            -3.6661344766616821e-01 3.4494820982217789e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 570 2.1571468096226454e-03</internalNodes>
          <leafValues>
            -8.9639738202095032e-02 1.5742646157741547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 1.8860616255551577e-03</internalNodes>
          <leafValues>
            -8.5441410541534424e-02 1.7696820199489594e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 1.0152467293664813e-03</internalNodes>
          <leafValues>
            3.8969900459051132e-02 -3.7170857191085815e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 -7.2436146438121796e-03</internalNodes>
          <leafValues>
            1.7777322232723236e-01 -8.3921253681182861e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 860 -3.7075001746416092e-03</internalNodes>
          <leafValues>
            1.8386960029602051e-01 -9.2291206121444702e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 607 -1.4369469135999680e-03</internalNodes>
          <leafValues>
            -2.7023202180862427e-01 5.5822439491748810e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 -1.7798715271055698e-03</internalNodes>
          <leafValues>
            1.4900380373001099e-01 -9.8068036139011383e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 1.9487005192786455e-03</internalNodes>
          <leafValues>
            -7.6331101357936859e-02 1.8935331702232361e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 2.8823004104197025e-03</internalNodes>
          <leafValues>
            4.2902354151010513e-02 -3.5214039683341980e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 -5.6123267859220505e-03</internalNodes>
          <leafValues>
            -4.7632521390914917e-01 2.6048270985484123e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 1.6383089125156403e-02</internalNodes>
          <leafValues>
            -8.9606925845146179e-02 1.4743074774742126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 3.4455281496047974e-01</internalNodes>
          <leafValues>
            -2.0530648529529572e-02 7.2817444801330566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 2.7680541388690472e-03</internalNodes>
          <leafValues>
            -3.8598377257585526e-02 3.5294523835182190e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 3.8404709193855524e-03</internalNodes>
          <leafValues>
            -8.0346472561359406e-02 1.8624457716941833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -4.3602049350738525e-02</internalNodes>
          <leafValues>
            2.4986675381660461e-01 -7.1408227086067200e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -3.6010099574923515e-03</internalNodes>
          <leafValues>
            1.6942474246025085e-01 -1.0419391095638275e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 -6.1740418896079063e-03</internalNodes>
          <leafValues>
            -4.6275594830513000e-01 3.1903993338346481e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 3.4251257311552763e-03</internalNodes>
          <leafValues>
            2.5748182088136673e-02 -4.8371604084968567e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 841 -3.9031119085848331e-03</internalNodes>
          <leafValues>
            1.6738632321357727e-01 -8.4549814462661743e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 -7.3322677053511143e-04</internalNodes>
          <leafValues>
            2.1091395616531372e-01 -7.2518013417720795e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 932 2.7150693349540234e-03</internalNodes>
          <leafValues>
            -5.7143334299325943e-02 2.7224695682525635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 1.7057932913303375e-02</internalNodes>
          <leafValues>
            -6.3262723386287689e-02 2.4493633210659027e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 968 2.2439109161496162e-03</internalNodes>
          <leafValues>
            6.1605937778949738e-02 -2.5090345740318298e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 9.2767914757132530e-03</internalNodes>
          <leafValues>
            3.9061944931745529e-02 -3.7177914381027222e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 -1.3303438900038600e-03</internalNodes>
          <leafValues>
            2.2685268521308899e-01 -6.3722826540470123e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 -2.8938084840774536e-02</internalNodes>
          <leafValues>
            -6.6085141897201538e-01 2.1666957065463066e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 -5.0926357507705688e-03</internalNodes>
          <leafValues>
            1.7695400118827820e-01 -7.9500846564769745e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 749 5.3870421834290028e-03</internalNodes>
          <leafValues>
            -5.5856045335531235e-02 2.7346748113632202e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 524 -8.0796808470040560e-04</internalNodes>
          <leafValues>
            -2.2386504709720612e-01 6.8852454423904419e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 3.9214566349983215e-03</internalNodes>
          <leafValues>
            2.2929171100258827e-02 -5.9076148271560669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 691 -6.5161995589733124e-03</internalNodes>
          <leafValues>
            -5.5175542831420898e-01 2.0790172740817070e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 -1.6703434521332383e-03</internalNodes>
          <leafValues>
            -3.5234490036964417e-01 3.4917417913675308e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 -3.2307719811797142e-03</internalNodes>
          <leafValues>
            1.5907490253448486e-01 -7.8541077673435211e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 5.0736088305711746e-03</internalNodes>
          <leafValues>
            -7.3796175420284271e-02 1.7231710255146027e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -1.9738268107175827e-02</internalNodes>
          <leafValues>
            2.4731338024139404e-01 -5.3791344165802002e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 6.9891922175884247e-03</internalNodes>
          <leafValues>
            -6.1806734651327133e-02 2.1923109889030457e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 -2.6465239934623241e-03</internalNodes>
          <leafValues>
            2.8577965497970581e-01 -6.6506840288639069e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 3.3399839885532856e-03</internalNodes>
          <leafValues>
            3.4735739231109619e-02 -3.9448723196983337e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          0 0 6 1 -1.</_>
        <_>
          3 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 6 2 -1.</_>
        <_>
          3 0 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 1 -1.</_>
        <_>
          4 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 4 -1.</_>
        <_>
          0 0 4 2 2.</_>
        <_>
          4 2 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 6 -1.</_>
        <_>
          0 0 4 3 2.</_>
        <_>
          4 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 12 -1.</_>
        <_>
          0 0 4 6 2.</_>
        <_>
          4 6 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 16 -1.</_>
        <_>
          0 0 4 8 2.</_>
        <_>
          4 8 4 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 3 -1.</_>
        <_>
          5 0 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 6 -1.</_>
        <_>
          0 0 5 3 2.</_>
        <_>
          5 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 10 -1.</_>
        <_>
          0 0 5 5 2.</_>
        <_>
          5 5 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 18 14 -1.</_>
        <_>
          6 0 6 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 14 10 -1.</_>
        <_>
          0 0 7 5 2.</_>
        <_>
          7 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          8 0 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 8 -1.</_>
        <_>
          0 0 8 4 2.</_>
        <_>
          8 4 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 10 -1.</_>
        <_>
          0 0 8 5 2.</_>
        <_>
          8 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          12 0 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 24 2 -1.</_>
        <_>
          8 1 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 16 8 -1.</_>
        <_>
          0 1 8 4 2.</_>
        <_>
          8 5 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 16 18 -1.</_>
        <_>
          0 7 16 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 24 2 -1.</_>
        <_>
          0 2 12 1 2.</_>
        <_>
          12 3 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 24 19 -1.</_>
        <_>
          12 2 12 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 3 8 3 -1.</_>
        <_>
          0 4 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 3 24 2 -1.</_>
        <_>
          0 3 12 1 2.</_>
        <_>
          12 4 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 6 9 -1.</_>
        <_>
          0 7 6 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 24 2 -1.</_>
        <_>
          0 4 12 1 2.</_>
        <_>
          12 5 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 13 15 -1.</_>
        <_>
          0 9 13 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 14 3 -1.</_>
        <_>
          0 5 14 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 23 6 -1.</_>
        <_>
          0 6 23 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 9 9 -1.</_>
        <_>
          0 8 9 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 24 2 -1.</_>
        <_>
          0 5 12 1 2.</_>
        <_>
          12 6 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 2 -1.</_>
        <_>
          0 6 12 1 2.</_>
        <_>
          12 7 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 18 8 -1.</_>
        <_>
          0 10 18 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 3 -1.</_>
        <_>
          0 7 24 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 8 2 -1.</_>
        <_>
          0 7 4 1 2.</_>
        <_>
          4 8 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 9 6 -1.</_>
        <_>
          0 9 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 22 4 -1.</_>
        <_>
          0 7 11 2 2.</_>
        <_>
          11 9 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 2 -1.</_>
        <_>
          0 7 12 1 2.</_>
        <_>
          12 8 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 4 -1.</_>
        <_>
          0 7 12 2 2.</_>
        <_>
          12 9 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 20 6 -1.</_>
        <_>
          0 9 20 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 6 14 -1.</_>
        <_>
          3 8 3 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 8 2 -1.</_>
        <_>
          0 8 4 1 2.</_>
        <_>
          4 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 1 -1.</_>
        <_>
          8 8 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 2 -1.</_>
        <_>
          0 8 12 1 2.</_>
        <_>
          12 9 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 4 -1.</_>
        <_>
          0 8 12 2 2.</_>
        <_>
          12 10 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 9 -1.</_>
        <_>
          12 8 12 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 4 1 -1.</_>
        <_>
          2 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 6 12 -1.</_>
        <_>
          0 9 3 6 2.</_>
        <_>
          3 15 3 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 4 9 -1.</_>
        <_>
          0 12 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 7 2 -1.</_>
        <_>
          0 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 11 2 -1.</_>
        <_>
          0 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 22 2 -1.</_>
        <_>
          0 9 11 1 2.</_>
        <_>
          11 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 2 -1.</_>
        <_>
          0 9 12 1 2.</_>
        <_>
          12 10 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 4 -1.</_>
        <_>
          0 9 12 2 2.</_>
        <_>
          12 11 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 6 -1.</_>
        <_>
          0 9 12 3 2.</_>
        <_>
          12 12 12 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 15 3 -1.</_>
        <_>
          0 10 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 2 2 -1.</_>
        <_>
          0 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 3 3 -1.</_>
        <_>
          0 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 3 6 -1.</_>
        <_>
          0 12 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 6 8 -1.</_>
        <_>
          0 10 3 4 2.</_>
        <_>
          3 14 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 8 10 -1.</_>
        <_>
          0 10 4 5 2.</_>
        <_>
          4 15 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 8 11 -1.</_>
        <_>
          4 10 4 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 2 -1.</_>
        <_>
          0 10 12 1 2.</_>
        <_>
          12 11 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 6 2 -1.</_>
        <_>
          3 11 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 6 8 -1.</_>
        <_>
          0 11 3 4 2.</_>
        <_>
          3 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 8 8 -1.</_>
        <_>
          0 11 4 4 2.</_>
        <_>
          4 15 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 8 13 -1.</_>
        <_>
          4 11 4 13 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 24 2 -1.</_>
        <_>
          0 11 12 1 2.</_>
        <_>
          12 12 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 21 10 -1.</_>
        <_>
          0 16 21 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 8 4 -1.</_>
        <_>
          4 12 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 24 9 -1.</_>
        <_>
          12 12 12 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 13 24 4 -1.</_>
        <_>
          0 13 12 2 2.</_>
        <_>
          12 15 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 3 7 -1.</_>
        <_>
          1 14 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 10 8 -1.</_>
        <_>
          5 14 5 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 8 8 -1.</_>
        <_>
          0 15 4 4 2.</_>
        <_>
          4 19 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 12 8 -1.</_>
        <_>
          0 15 6 4 2.</_>
        <_>
          6 19 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 16 3 8 -1.</_>
        <_>
          1 16 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 16 8 6 -1.</_>
        <_>
          0 18 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 17 6 3 -1.</_>
        <_>
          3 17 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 17 4 6 -1.</_>
        <_>
          0 19 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 18 3 6 -1.</_>
        <_>
          1 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 19 3 4 -1.</_>
        <_>
          1 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 19 9 3 -1.</_>
        <_>
          0 20 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 20 4 3 -1.</_>
        <_>
          0 21 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 20 6 3 -1.</_>
        <_>
          0 21 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 21 3 3 -1.</_>
        <_>
          0 22 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 22 1 2 -1.</_>
        <_>
          0 23 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 10 1 -1.</_>
        <_>
          6 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 12 6 -1.</_>
        <_>
          1 0 6 3 2.</_>
        <_>
          7 3 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 7 9 -1.</_>
        <_>
          1 3 7 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 14 10 -1.</_>
        <_>
          1 0 7 5 2.</_>
        <_>
          8 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 22 2 -1.</_>
        <_>
          1 0 11 1 2.</_>
        <_>
          12 1 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 2 6 10 -1.</_>
        <_>
          1 2 3 5 2.</_>
        <_>
          4 7 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 2 22 20 -1.</_>
        <_>
          12 2 11 20 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 2 22 21 -1.</_>
        <_>
          12 2 11 21 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 15 3 -1.</_>
        <_>
          6 3 5 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 6 3 -1.</_>
        <_>
          1 4 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 22 2 -1.</_>
        <_>
          1 3 11 1 2.</_>
        <_>
          12 4 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 4 3 -1.</_>
        <_>
          1 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 12 17 -1.</_>
        <_>
          5 4 4 17 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 20 20 -1.</_>
        <_>
          11 4 10 20 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 22 2 -1.</_>
        <_>
          1 4 11 1 2.</_>
        <_>
          12 5 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 4 22 19 -1.</_>
        <_>
          12 4 11 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 5 22 2 -1.</_>
        <_>
          1 5 11 1 2.</_>
        <_>
          12 6 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 2 2 -1.</_>
        <_>
          1 6 1 1 2.</_>
        <_>
          2 7 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 16 17 -1.</_>
        <_>
          9 6 8 17 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 21 9 -1.</_>
        <_>
          1 9 21 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 22 6 -1.</_>
        <_>
          1 8 22 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 23 9 -1.</_>
        <_>
          1 9 23 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 22 6 -1.</_>
        <_>
          1 7 11 3 2.</_>
        <_>
          12 10 11 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 19 12 -1.</_>
        <_>
          1 12 19 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 6 2 -1.</_>
        <_>
          4 9 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 12 3 -1.</_>
        <_>
          5 9 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 3 8 -1.</_>
        <_>
          2 10 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 20 2 -1.</_>
        <_>
          1 10 10 1 2.</_>
        <_>
          11 11 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 12 3 -1.</_>
        <_>
          1 11 12 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 11 3 8 -1.</_>
        <_>
          2 11 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 11 20 2 -1.</_>
        <_>
          1 11 10 1 2.</_>
        <_>
          11 12 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 11 22 4 -1.</_>
        <_>
          1 11 11 2 2.</_>
        <_>
          12 13 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 3 11 -1.</_>
        <_>
          2 12 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 6 1 -1.</_>
        <_>
          4 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 12 20 12 -1.</_>
        <_>
          11 12 10 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 3 5 -1.</_>
        <_>
          2 13 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 22 2 -1.</_>
        <_>
          1 13 11 1 2.</_>
        <_>
          12 14 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 14 4 4 -1.</_>
        <_>
          3 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 14 22 2 -1.</_>
        <_>
          1 14 11 1 2.</_>
        <_>
          12 15 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 14 23 6 -1.</_>
        <_>
          1 17 23 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 15 14 2 -1.</_>
        <_>
          1 15 7 1 2.</_>
        <_>
          8 16 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 17 3 5 -1.</_>
        <_>
          2 17 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 17 3 7 -1.</_>
        <_>
          2 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 18 10 6 -1.</_>
        <_>
          1 18 5 3 2.</_>
        <_>
          6 21 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 18 9 3 -1.</_>
        <_>
          1 19 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 21 12 3 -1.</_>
        <_>
          7 21 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 3 2 -1.</_>
        <_>
          3 0 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 6 1 -1.</_>
        <_>
          5 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 9 2 -1.</_>
        <_>
          5 0 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 21 1 -1.</_>
        <_>
          9 0 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 1 3 23 -1.</_>
        <_>
          3 1 1 23 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 1 4 2 -1.</_>
        <_>
          4 1 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 2 22 10 -1.</_>
        <_>
          2 2 11 5 2.</_>
        <_>
          13 7 11 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 3 22 2 -1.</_>
        <_>
          2 3 11 1 2.</_>
        <_>
          13 4 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 6 1 -1.</_>
        <_>
          5 4 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 3 3 -1.</_>
        <_>
          2 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 20 2 -1.</_>
        <_>
          2 4 10 1 2.</_>
        <_>
          12 5 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 22 16 -1.</_>
        <_>
          13 4 11 16 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 18 6 -1.</_>
        <_>
          2 6 18 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 3 3 -1.</_>
        <_>
          2 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 4 3 -1.</_>
        <_>
          2 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 20 2 -1.</_>
        <_>
          2 6 10 1 2.</_>
        <_>
          12 7 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 22 2 -1.</_>
        <_>
          2 6 11 1 2.</_>
        <_>
          13 7 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 21 6 -1.</_>
        <_>
          2 9 21 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 22 2 -1.</_>
        <_>
          2 8 22 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 6 2 -1.</_>
        <_>
          2 8 3 1 2.</_>
        <_>
          5 9 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 6 3 -1.</_>
        <_>
          2 9 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 21 2 -1.</_>
        <_>
          9 8 7 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 7 9 -1.</_>
        <_>
          2 11 7 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 22 4 -1.</_>
        <_>
          2 8 11 2 2.</_>
        <_>
          13 10 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 4 1 -1.</_>
        <_>
          4 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 9 1 -1.</_>
        <_>
          5 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 9 2 -1.</_>
        <_>
          5 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 6 4 -1.</_>
        <_>
          2 9 3 2 2.</_>
        <_>
          5 11 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 7 3 -1.</_>
        <_>
          2 10 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 18 2 -1.</_>
        <_>
          2 9 9 1 2.</_>
        <_>
          11 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 22 2 -1.</_>
        <_>
          2 9 11 1 2.</_>
        <_>
          13 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 21 3 -1.</_>
        <_>
          2 10 21 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 10 22 2 -1.</_>
        <_>
          2 10 11 1 2.</_>
        <_>
          13 11 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 3 6 -1.</_>
        <_>
          3 11 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 3 13 -1.</_>
        <_>
          3 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 22 2 -1.</_>
        <_>
          2 11 11 1 2.</_>
        <_>
          13 12 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 3 4 -1.</_>
        <_>
          3 12 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 3 5 -1.</_>
        <_>
          3 12 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 22 2 -1.</_>
        <_>
          2 12 11 1 2.</_>
        <_>
          13 13 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 13 10 10 -1.</_>
        <_>
          2 13 5 5 2.</_>
        <_>
          7 18 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 14 6 10 -1.</_>
        <_>
          2 14 3 5 2.</_>
        <_>
          5 19 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 2 6 -1.</_>
        <_>
          2 15 1 3 2.</_>
        <_>
          3 18 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 3 9 -1.</_>
        <_>
          3 15 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 6 4 -1.</_>
        <_>
          5 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 7 3 -1.</_>
        <_>
          2 16 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 17 3 4 -1.</_>
        <_>
          3 17 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 17 12 6 -1.</_>
        <_>
          2 17 6 3 2.</_>
        <_>
          8 20 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 21 14 3 -1.</_>
        <_>
          9 21 7 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 0 12 4 -1.</_>
        <_>
          3 0 6 2 2.</_>
        <_>
          9 2 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 1 18 2 -1.</_>
        <_>
          3 1 9 1 2.</_>
        <_>
          12 2 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 2 17 6 -1.</_>
        <_>
          3 5 17 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 3 3 -1.</_>
        <_>
          4 3 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 3 18 2 -1.</_>
        <_>
          3 3 9 1 2.</_>
        <_>
          12 4 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 1 3 -1.</_>
        <_>
          3 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 2 2 -1.</_>
        <_>
          3 4 1 1 2.</_>
        <_>
          4 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 3 3 -1.</_>
        <_>
          3 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 18 2 -1.</_>
        <_>
          3 4 9 1 2.</_>
        <_>
          12 5 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 18 4 -1.</_>
        <_>
          3 4 9 2 2.</_>
        <_>
          12 6 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 20 6 -1.</_>
        <_>
          3 6 20 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 2 1 -1.</_>
        <_>
          4 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 1 3 -1.</_>
        <_>
          3 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 2 2 -1.</_>
        <_>
          3 6 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 2 3 -1.</_>
        <_>
          3 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 20 2 -1.</_>
        <_>
          3 5 10 1 2.</_>
        <_>
          13 6 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 17 6 -1.</_>
        <_>
          3 7 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 17 9 -1.</_>
        <_>
          3 8 17 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 19 9 -1.</_>
        <_>
          3 8 19 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 2 2 -1.</_>
        <_>
          3 6 1 1 2.</_>
        <_>
          4 7 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 2 3 -1.</_>
        <_>
          3 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 9 6 -1.</_>
        <_>
          6 6 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 5 6 -1.</_>
        <_>
          3 8 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 18 2 -1.</_>
        <_>
          3 6 9 1 2.</_>
        <_>
          12 7 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 20 8 -1.</_>
        <_>
          3 6 10 4 2.</_>
        <_>
          13 10 10 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 18 3 -1.</_>
        <_>
          3 7 18 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 6 10 -1.</_>
        <_>
          3 7 3 5 2.</_>
        <_>
          6 12 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 5 2 -1.</_>
        <_>
          3 8 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 6 3 -1.</_>
        <_>
          3 8 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 18 4 -1.</_>
        <_>
          3 7 9 2 2.</_>
        <_>
          12 9 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 17 3 -1.</_>
        <_>
          3 8 17 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 18 2 -1.</_>
        <_>
          3 8 18 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 8 2 3 -1.</_>
        <_>
          4 8 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 8 18 3 -1.</_>
        <_>
          3 9 18 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 8 20 3 -1.</_>
        <_>
          3 9 20 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 1 -1.</_>
        <_>
          4 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 2 -1.</_>
        <_>
          4 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 4 6 -1.</_>
        <_>
          3 9 2 3 2.</_>
        <_>
          5 12 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 6 4 -1.</_>
        <_>
          3 11 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 8 9 -1.</_>
        <_>
          3 12 8 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 20 2 -1.</_>
        <_>
          3 9 10 1 2.</_>
        <_>
          13 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 19 3 -1.</_>
        <_>
          3 10 19 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 4 2 -1.</_>
        <_>
          3 10 2 1 2.</_>
        <_>
          5 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 18 6 -1.</_>
        <_>
          3 12 18 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 4 -1.</_>
        <_>
          4 11 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 5 -1.</_>
        <_>
          4 11 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 4 10 -1.</_>
        <_>
          3 16 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 7 8 -1.</_>
        <_>
          3 15 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 2 -1.</_>
        <_>
          4 12 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 10 -1.</_>
        <_>
          4 12 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 6 -1.</_>
        <_>
          3 15 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 4 8 -1.</_>
        <_>
          3 16 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 10 8 -1.</_>
        <_>
          3 12 5 4 2.</_>
        <_>
          8 16 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 12 18 2 -1.</_>
        <_>
          3 12 9 1 2.</_>
        <_>
          12 13 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 13 19 6 -1.</_>
        <_>
          3 16 19 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 14 4 9 -1.</_>
        <_>
          3 17 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 3 4 -1.</_>
        <_>
          4 16 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 4 3 -1.</_>
        <_>
          5 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 21 7 -1.</_>
        <_>
          10 16 7 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 18 3 6 -1.</_>
        <_>
          4 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 0 3 23 -1.</_>
        <_>
          5 0 1 23 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 0 6 5 -1.</_>
        <_>
          6 0 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 0 17 12 -1.</_>
        <_>
          4 4 17 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 1 16 12 -1.</_>
        <_>
          4 7 16 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 3 1 -1.</_>
        <_>
          5 3 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 16 10 -1.</_>
        <_>
          4 3 8 5 2.</_>
        <_>
          12 8 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 14 6 -1.</_>
        <_>
          4 6 14 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 1 3 -1.</_>
        <_>
          4 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 2 4 -1.</_>
        <_>
          4 6 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 6 2 -1.</_>
        <_>
          7 4 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 13 6 -1.</_>
        <_>
          4 7 13 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 6 4 -1.</_>
        <_>
          6 5 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 14 6 -1.</_>
        <_>
          4 7 14 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 16 6 -1.</_>
        <_>
          4 8 16 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 1 3 -1.</_>
        <_>
          4 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 2 3 -1.</_>
        <_>
          4 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 3 3 -1.</_>
        <_>
          4 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 2 -1.</_>
        <_>
          4 6 8 1 2.</_>
        <_>
          12 7 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 16 6 -1.</_>
        <_>
          4 6 8 3 2.</_>
        <_>
          12 9 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 10 8 -1.</_>
        <_>
          4 10 10 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 2 3 -1.</_>
        <_>
          4 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 5 6 -1.</_>
        <_>
          4 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 4 -1.</_>
        <_>
          4 7 8 2 2.</_>
        <_>
          12 9 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 18 2 -1.</_>
        <_>
          4 7 9 1 2.</_>
        <_>
          13 8 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 6 -1.</_>
        <_>
          4 9 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 17 6 -1.</_>
        <_>
          4 9 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 2 2 -1.</_>
        <_>
          4 8 1 1 2.</_>
        <_>
          5 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 16 2 -1.</_>
        <_>
          4 8 8 1 2.</_>
        <_>
          12 9 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 16 4 -1.</_>
        <_>
          4 8 8 2 2.</_>
        <_>
          12 10 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 15 6 -1.</_>
        <_>
          4 10 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 1 -1.</_>
        <_>
          5 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 1 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 2 -1.</_>
        <_>
          5 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 4 -1.</_>
        <_>
          4 9 1 2 2.</_>
        <_>
          5 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 6 1 -1.</_>
        <_>
          6 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 6 -1.</_>
        <_>
          4 11 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 8 6 -1.</_>
        <_>
          4 9 4 3 2.</_>
        <_>
          8 12 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 5 3 -1.</_>
        <_>
          4 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 15 2 -1.</_>
        <_>
          9 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 15 4 -1.</_>
        <_>
          9 9 5 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 16 2 -1.</_>
        <_>
          4 9 8 1 2.</_>
        <_>
          12 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 18 2 -1.</_>
        <_>
          4 9 9 1 2.</_>
        <_>
          13 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 15 15 -1.</_>
        <_>
          4 14 15 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 17 9 -1.</_>
        <_>
          4 12 17 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 20 6 -1.</_>
        <_>
          4 11 20 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 1 -1.</_>
        <_>
          5 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 1 -1.</_>
        <_>
          5 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 2 -1.</_>
        <_>
          4 10 1 1 2.</_>
        <_>
          5 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 5 -1.</_>
        <_>
          5 10 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 16 10 -1.</_>
        <_>
          4 10 8 5 2.</_>
        <_>
          12 15 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 2 -1.</_>
        <_>
          5 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 3 -1.</_>
        <_>
          5 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 5 -1.</_>
        <_>
          5 11 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 10 -1.</_>
        <_>
          4 16 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 16 2 -1.</_>
        <_>
          4 11 8 1 2.</_>
        <_>
          12 12 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 1 -1.</_>
        <_>
          5 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 2 -1.</_>
        <_>
          5 12 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 1 6 -1.</_>
        <_>
          4 15 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 4 4 -1.</_>
        <_>
          6 12 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 4 4 -1.</_>
        <_>
          4 14 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 18 8 -1.</_>
        <_>
          13 12 9 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 9 5 -1.</_>
        <_>
          7 13 3 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 14 4 2 -1.</_>
        <_>
          6 14 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 16 4 1 -1.</_>
        <_>
          6 16 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 16 9 2 -1.</_>
        <_>
          7 16 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 19 3 4 -1.</_>
        <_>
          5 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 22 12 2 -1.</_>
        <_>
          4 23 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 3 3 -1.</_>
        <_>
          6 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 4 12 -1.</_>
        <_>
          5 2 2 6 2.</_>
        <_>
          7 8 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 4 12 -1.</_>
        <_>
          7 2 2 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 14 2 -1.</_>
        <_>
          5 2 7 1 2.</_>
        <_>
          12 3 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 16 10 -1.</_>
        <_>
          5 7 16 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 6 2 -1.</_>
        <_>
          7 3 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 14 2 -1.</_>
        <_>
          5 3 7 1 2.</_>
        <_>
          12 4 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 5 3 -1.</_>
        <_>
          5 5 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 14 2 -1.</_>
        <_>
          5 4 7 1 2.</_>
        <_>
          12 5 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 16 18 -1.</_>
        <_>
          5 4 8 9 2.</_>
        <_>
          13 13 8 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 2 2 -1.</_>
        <_>
          5 5 1 1 2.</_>
        <_>
          6 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 2 -1.</_>
        <_>
          5 5 7 1 2.</_>
        <_>
          12 6 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 6 -1.</_>
        <_>
          5 7 14 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 14 2 -1.</_>
        <_>
          5 6 7 1 2.</_>
        <_>
          12 7 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 19 6 -1.</_>
        <_>
          5 8 19 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 1 3 -1.</_>
        <_>
          5 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 6 -1.</_>
        <_>
          7 7 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 2 -1.</_>
        <_>
          5 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 3 -1.</_>
        <_>
          5 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 6 -1.</_>
        <_>
          5 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 5 2 -1.</_>
        <_>
          5 8 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 14 10 -1.</_>
        <_>
          5 7 7 5 2.</_>
        <_>
          12 12 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 14 2 -1.</_>
        <_>
          5 8 14 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 15 4 -1.</_>
        <_>
          5 9 15 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 15 6 -1.</_>
        <_>
          5 9 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 16 2 -1.</_>
        <_>
          5 8 16 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 16 6 -1.</_>
        <_>
          5 9 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 1 3 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 2 4 -1.</_>
        <_>
          5 8 1 2 2.</_>
        <_>
          6 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 3 2 -1.</_>
        <_>
          5 9 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 3 1 -1.</_>
        <_>
          6 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 1 3 -1.</_>
        <_>
          5 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 1 9 -1.</_>
        <_>
          5 12 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 8 1 -1.</_>
        <_>
          9 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 15 2 -1.</_>
        <_>
          10 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 2 -1.</_>
        <_>
          5 9 7 1 2.</_>
        <_>
          12 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 8 -1.</_>
        <_>
          5 9 7 4 2.</_>
        <_>
          12 13 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 16 2 -1.</_>
        <_>
          5 9 8 1 2.</_>
        <_>
          13 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 2 2 -1.</_>
        <_>
          5 10 1 1 2.</_>
        <_>
          6 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 15 1 -1.</_>
        <_>
          10 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 2 -1.</_>
        <_>
          6 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 14 2 -1.</_>
        <_>
          5 12 14 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 12 17 2 -1.</_>
        <_>
          5 13 17 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 13 4 6 -1.</_>
        <_>
          5 13 2 3 2.</_>
        <_>
          7 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 13 18 10 -1.</_>
        <_>
          14 13 9 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 15 9 3 -1.</_>
        <_>
          8 15 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 17 3 7 -1.</_>
        <_>
          6 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 21 3 3 -1.</_>
        <_>
          6 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 0 3 6 -1.</_>
        <_>
          6 3 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 0 12 2 -1.</_>
        <_>
          12 0 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 0 14 12 -1.</_>
        <_>
          6 6 14 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 6 7 -1.</_>
        <_>
          8 1 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 6 11 -1.</_>
        <_>
          8 1 2 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 6 12 -1.</_>
        <_>
          8 2 2 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 12 4 -1.</_>
        <_>
          6 4 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 3 2 2 -1.</_>
        <_>
          6 4 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 3 14 18 -1.</_>
        <_>
          6 3 7 9 2.</_>
        <_>
          13 12 7 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 1 2 -1.</_>
        <_>
          6 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 3 3 -1.</_>
        <_>
          7 4 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 2 1 -1.</_>
        <_>
          7 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 2 9 -1.</_>
        <_>
          6 8 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 12 2 -1.</_>
        <_>
          6 5 6 1 2.</_>
        <_>
          12 6 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 12 6 -1.</_>
        <_>
          6 7 12 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 10 2 -1.</_>
        <_>
          6 6 5 1 2.</_>
        <_>
          11 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 12 6 -1.</_>
        <_>
          6 8 12 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 14 4 -1.</_>
        <_>
          6 8 14 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 1 3 -1.</_>
        <_>
          6 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 1 6 -1.</_>
        <_>
          6 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 3 -1.</_>
        <_>
          6 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 6 -1.</_>
        <_>
          6 9 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 3 6 -1.</_>
        <_>
          6 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 4 -1.</_>
        <_>
          7 8 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 3 -1.</_>
        <_>
          6 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 9 6 -1.</_>
        <_>
          9 8 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 3 -1.</_>
        <_>
          10 8 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 2 -1.</_>
        <_>
          6 8 6 1 2.</_>
        <_>
          12 9 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 8 12 -1.</_>
        <_>
          6 14 8 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 6 -1.</_>
        <_>
          6 11 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 3 3 -1.</_>
        <_>
          6 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 3 6 -1.</_>
        <_>
          6 12 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 6 6 -1.</_>
        <_>
          6 9 3 3 2.</_>
        <_>
          9 12 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          10 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 3 -1.</_>
        <_>
          10 9 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          6 9 6 1 2.</_>
        <_>
          12 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 14 2 -1.</_>
        <_>
          6 9 7 1 2.</_>
        <_>
          13 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 1 3 -1.</_>
        <_>
          6 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          7 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          6 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 6 3 -1.</_>
        <_>
          8 10 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 3 3 -1.</_>
        <_>
          6 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 2 -1.</_>
        <_>
          6 10 6 1 2.</_>
        <_>
          12 11 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 13 3 -1.</_>
        <_>
          6 11 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 2 2 -1.</_>
        <_>
          6 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 2 3 -1.</_>
        <_>
          6 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 2 -1.</_>
        <_>
          6 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 3 -1.</_>
        <_>
          6 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 4 3 -1.</_>
        <_>
          6 12 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 16 3 -1.</_>
        <_>
          6 12 16 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 14 1 3 -1.</_>
        <_>
          6 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 16 1 3 -1.</_>
        <_>
          6 17 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 17 3 6 -1.</_>
        <_>
          7 17 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 17 11 4 -1.</_>
        <_>
          6 19 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 18 12 2 -1.</_>
        <_>
          6 19 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 19 3 5 -1.</_>
        <_>
          7 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 20 14 4 -1.</_>
        <_>
          6 22 14 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 10 1 -1.</_>
        <_>
          12 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 7 2 -1.</_>
        <_>
          7 1 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 11 10 -1.</_>
        <_>
          7 5 11 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 10 1 -1.</_>
        <_>
          12 1 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 10 2 -1.</_>
        <_>
          7 1 5 1 2.</_>
        <_>
          12 2 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 2 2 -1.</_>
        <_>
          7 2 1 1 2.</_>
        <_>
          8 3 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 10 2 -1.</_>
        <_>
          7 2 5 1 2.</_>
        <_>
          12 3 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 3 10 2 -1.</_>
        <_>
          7 3 5 1 2.</_>
        <_>
          12 4 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 2 4 -1.</_>
        <_>
          8 4 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 3 4 -1.</_>
        <_>
          8 4 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 3 7 -1.</_>
        <_>
          8 4 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 2 8 -1.</_>
        <_>
          8 4 1 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 9 7 -1.</_>
        <_>
          10 4 3 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 10 2 -1.</_>
        <_>
          7 4 5 1 2.</_>
        <_>
          12 5 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 2 2 -1.</_>
        <_>
          8 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 2 -1.</_>
        <_>
          8 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 6 -1.</_>
        <_>
          8 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 7 -1.</_>
        <_>
          8 5 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 6 -1.</_>
        <_>
          7 7 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 10 2 -1.</_>
        <_>
          7 5 5 1 2.</_>
        <_>
          12 6 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 11 6 -1.</_>
        <_>
          7 7 11 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 10 2 -1.</_>
        <_>
          7 6 5 1 2.</_>
        <_>
          12 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 1 3 -1.</_>
        <_>
          7 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 1 6 -1.</_>
        <_>
          7 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 12 15 -1.</_>
        <_>
          7 12 12 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 1 3 -1.</_>
        <_>
          7 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 1 2.</_>
        <_>
          8 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 3 16 -1.</_>
        <_>
          8 8 1 16 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 4 16 -1.</_>
        <_>
          9 8 2 16 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 1 2 -1.</_>
        <_>
          7 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 2 2 -1.</_>
        <_>
          7 9 1 1 2.</_>
        <_>
          8 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 2 3 -1.</_>
        <_>
          7 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 4 6 -1.</_>
        <_>
          7 9 2 3 2.</_>
        <_>
          9 12 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 6 10 -1.</_>
        <_>
          7 9 3 5 2.</_>
        <_>
          10 14 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 12 2 -1.</_>
        <_>
          11 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 10 2 -1.</_>
        <_>
          7 9 5 1 2.</_>
        <_>
          12 10 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 3 1 -1.</_>
        <_>
          8 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 1 3 -1.</_>
        <_>
          7 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 2 3 -1.</_>
        <_>
          7 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 4 4 -1.</_>
        <_>
          7 10 2 2 2.</_>
        <_>
          9 12 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 4 4 -1.</_>
        <_>
          9 10 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 6 5 -1.</_>
        <_>
          9 10 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 9 7 -1.</_>
        <_>
          10 10 3 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 10 2 -1.</_>
        <_>
          7 10 5 1 2.</_>
        <_>
          12 11 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 2 1 -1.</_>
        <_>
          8 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 6 4 -1.</_>
        <_>
          9 11 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 13 12 3 -1.</_>
        <_>
          11 13 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 13 13 10 -1.</_>
        <_>
          7 18 13 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 15 6 3 -1.</_>
        <_>
          9 15 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 16 1 6 -1.</_>
        <_>
          7 19 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 16 9 2 -1.</_>
        <_>
          10 16 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 17 11 6 -1.</_>
        <_>
          7 20 11 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 18 5 6 -1.</_>
        <_>
          7 20 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 18 9 6 -1.</_>
        <_>
          7 21 9 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 18 10 6 -1.</_>
        <_>
          7 20 10 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 18 11 6 -1.</_>
        <_>
          7 21 11 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 3 4 -1.</_>
        <_>
          8 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 9 3 -1.</_>
        <_>
          7 21 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 1 -1.</_>
        <_>
          12 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 10 1 -1.</_>
        <_>
          13 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 2 -1.</_>
        <_>
          8 1 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 8 -1.</_>
        <_>
          8 0 8 4 2.</_>
        <_>
          16 4 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 10 -1.</_>
        <_>
          8 0 8 5 2.</_>
        <_>
          16 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 9 2 -1.</_>
        <_>
          8 1 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 1 9 10 -1.</_>
        <_>
          8 6 9 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 3 6 10 -1.</_>
        <_>
          10 3 2 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 3 1 -1.</_>
        <_>
          9 4 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 3 5 -1.</_>
        <_>
          9 4 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 2 7 -1.</_>
        <_>
          9 4 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 3 9 -1.</_>
        <_>
          9 4 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 8 2 -1.</_>
        <_>
          8 4 4 1 2.</_>
        <_>
          12 5 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 1 4 -1.</_>
        <_>
          8 7 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 2 -1.</_>
        <_>
          9 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 5 -1.</_>
        <_>
          9 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 6 -1.</_>
        <_>
          9 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 6 -1.</_>
        <_>
          9 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 7 -1.</_>
        <_>
          9 5 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 7 -1.</_>
        <_>
          9 5 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 3 -1.</_>
        <_>
          8 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 4 3 -1.</_>
        <_>
          8 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 8 2 -1.</_>
        <_>
          8 5 4 1 2.</_>
        <_>
          12 6 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 8 19 -1.</_>
        <_>
          12 5 4 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 3 5 -1.</_>
        <_>
          9 6 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 8 2 -1.</_>
        <_>
          8 6 4 1 2.</_>
        <_>
          12 7 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 10 2 -1.</_>
        <_>
          8 6 5 1 2.</_>
        <_>
          13 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 7 12 6 -1.</_>
        <_>
          12 7 4 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 7 16 1 -1.</_>
        <_>
          16 7 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 8 1 6 -1.</_>
        <_>
          8 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 2 4 -1.</_>
        <_>
          8 9 1 2 2.</_>
        <_>
          9 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 2 6 -1.</_>
        <_>
          8 9 1 3 2.</_>
        <_>
          9 12 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 9 8 2 -1.</_>
        <_>
          8 9 4 1 2.</_>
        <_>
          12 10 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 1 -1.</_>
        <_>
          9 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 1 -1.</_>
        <_>
          9 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 2 -1.</_>
        <_>
          9 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 8 -1.</_>
        <_>
          8 10 1 4 2.</_>
        <_>
          9 14 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 4 2 -1.</_>
        <_>
          10 10 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 4 8 -1.</_>
        <_>
          8 10 2 4 2.</_>
        <_>
          10 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 4 8 -1.</_>
        <_>
          8 11 2 4 2.</_>
        <_>
          10 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 14 1 3 -1.</_>
        <_>
          8 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 14 3 10 -1.</_>
        <_>
          9 14 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 16 9 3 -1.</_>
        <_>
          11 16 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 16 9 4 -1.</_>
        <_>
          11 16 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 10 6 -1.</_>
        <_>
          8 19 10 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 10 6 -1.</_>
        <_>
          8 20 10 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 18 9 6 -1.</_>
        <_>
          11 18 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 3 5 -1.</_>
        <_>
          9 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 7 3 -1.</_>
        <_>
          8 20 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 3 3 -1.</_>
        <_>
          9 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 3 4 -1.</_>
        <_>
          9 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 7 4 -1.</_>
        <_>
          8 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 12 4 -1.</_>
        <_>
          8 22 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 22 3 1 -1.</_>
        <_>
          9 22 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 22 3 2 -1.</_>
        <_>
          9 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 1 2 -1.</_>
        <_>
          9 1 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 5 10 -1.</_>
        <_>
          9 5 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 4 -1.</_>
        <_>
          9 2 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 8 -1.</_>
        <_>
          9 4 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 7 10 -1.</_>
        <_>
          9 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 14 10 -1.</_>
        <_>
          9 0 7 5 2.</_>
        <_>
          16 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 8 4 -1.</_>
        <_>
          9 2 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 2 3 -1.</_>
        <_>
          10 1 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 15 15 -1.</_>
        <_>
          9 6 15 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 2 2 -1.</_>
        <_>
          10 2 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 6 1 -1.</_>
        <_>
          12 2 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 6 22 -1.</_>
        <_>
          12 2 3 22 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 6 8 -1.</_>
        <_>
          9 6 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 4 9 -1.</_>
        <_>
          11 4 2 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 3 3 -1.</_>
        <_>
          9 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 3 6 -1.</_>
        <_>
          9 7 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 6 18 -1.</_>
        <_>
          12 4 3 18 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 4 12 2 -1.</_>
        <_>
          13 4 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 2 6 -1.</_>
        <_>
          10 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 6 5 -1.</_>
        <_>
          11 5 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 4 15 -1.</_>
        <_>
          11 5 2 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 3 -1.</_>
        <_>
          9 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 4 9 -1.</_>
        <_>
          11 6 2 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 3 3 -1.</_>
        <_>
          9 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 4 3 -1.</_>
        <_>
          9 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 7 6 2 -1.</_>
        <_>
          9 7 3 1 2.</_>
        <_>
          12 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 7 8 6 -1.</_>
        <_>
          13 7 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 8 9 9 -1.</_>
        <_>
          12 8 3 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 8 4 3 -1.</_>
        <_>
          9 9 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 5 3 -1.</_>
        <_>
          9 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 9 5 -1.</_>
        <_>
          12 10 3 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 15 3 -1.</_>
        <_>
          9 11 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 11 3 5 -1.</_>
        <_>
          10 11 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 15 6 1 -1.</_>
        <_>
          11 15 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 15 6 2 -1.</_>
        <_>
          11 15 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 15 9 9 -1.</_>
        <_>
          12 15 3 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 3 -1.</_>
        <_>
          11 16 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 4 -1.</_>
        <_>
          11 16 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 7 -1.</_>
        <_>
          11 16 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 17 6 4 -1.</_>
        <_>
          11 17 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 17 5 3 -1.</_>
        <_>
          9 18 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 18 8 6 -1.</_>
        <_>
          9 20 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 3 5 -1.</_>
        <_>
          10 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 6 3 -1.</_>
        <_>
          9 20 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 6 4 -1.</_>
        <_>
          9 21 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 8 3 -1.</_>
        <_>
          9 20 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 3 4 -1.</_>
        <_>
          10 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 5 3 -1.</_>
        <_>
          9 21 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 3 2 -1.</_>
        <_>
          10 21 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 3 3 -1.</_>
        <_>
          10 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 6 2 -1.</_>
        <_>
          9 22 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 22 3 2 -1.</_>
        <_>
          10 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 1 -1.</_>
        <_>
          12 0 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 2 -1.</_>
        <_>
          10 0 2 1 2.</_>
        <_>
          12 1 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 4 -1.</_>
        <_>
          10 0 2 2 2.</_>
        <_>
          12 2 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 3 6 -1.</_>
        <_>
          10 3 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 14 10 -1.</_>
        <_>
          10 0 7 5 2.</_>
        <_>
          17 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 1 2 9 -1.</_>
        <_>
          11 1 1 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 1 4 2 -1.</_>
        <_>
          10 1 2 1 2.</_>
        <_>
          12 2 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 2 9 -1.</_>
        <_>
          11 3 1 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 9 1 -1.</_>
        <_>
          13 3 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 3 6 2 -1.</_>
        <_>
          10 3 3 1 2.</_>
        <_>
          13 4 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 3 3 -1.</_>
        <_>
          10 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 7 15 -1.</_>
        <_>
          10 9 7 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 3 3 -1.</_>
        <_>
          10 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 7 3 -1.</_>
        <_>
          10 7 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 2 3 -1.</_>
        <_>
          10 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 4 14 -1.</_>
        <_>
          12 7 2 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 3 3 -1.</_>
        <_>
          10 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 4 3 -1.</_>
        <_>
          10 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 8 12 -1.</_>
        <_>
          10 13 8 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 10 8 -1.</_>
        <_>
          10 9 5 4 2.</_>
        <_>
          15 13 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 6 8 -1.</_>
        <_>
          10 13 6 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 2 6 -1.</_>
        <_>
          11 10 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 2 3 -1.</_>
        <_>
          10 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 10 14 4 -1.</_>
        <_>
          10 12 14 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 11 2 3 -1.</_>
        <_>
          10 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 12 1 3 -1.</_>
        <_>
          10 13 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 15 8 3 -1.</_>
        <_>
          14 15 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 16 3 2 -1.</_>
        <_>
          11 16 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 4 3 -1.</_>
        <_>
          10 18 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 18 12 2 -1.</_>
        <_>
          10 18 6 1 2.</_>
        <_>
          16 19 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 2 -1.</_>
        <_>
          11 20 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 3 -1.</_>
        <_>
          11 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 4 -1.</_>
        <_>
          11 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 4 3 -1.</_>
        <_>
          10 21 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 4 4 -1.</_>
        <_>
          10 22 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 5 3 -1.</_>
        <_>
          10 21 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 5 4 -1.</_>
        <_>
          10 22 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 10 4 -1.</_>
        <_>
          10 20 5 2 2.</_>
        <_>
          15 22 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 14 4 -1.</_>
        <_>
          10 20 7 2 2.</_>
        <_>
          17 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 3 3 -1.</_>
        <_>
          11 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 2 2 -1.</_>
        <_>
          10 22 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 14 2 -1.</_>
        <_>
          17 21 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 22 3 2 -1.</_>
        <_>
          11 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 3 7 -1.</_>
        <_>
          12 1 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 6 1 -1.</_>
        <_>
          13 1 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 4 15 -1.</_>
        <_>
          13 1 2 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 3 4 -1.</_>
        <_>
          12 2 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 2 12 -1.</_>
        <_>
          12 2 1 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 2 2 13 -1.</_>
        <_>
          12 2 1 13 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 1 3 -1.</_>
        <_>
          11 4 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 5 -1.</_>
        <_>
          12 3 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 7 -1.</_>
        <_>
          12 3 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 6 12 -1.</_>
        <_>
          13 3 2 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 4 13 -1.</_>
        <_>
          13 3 2 13 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 9 2 -1.</_>
        <_>
          14 3 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 5 -1.</_>
        <_>
          12 4 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 3 -1.</_>
        <_>
          11 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 3 3 -1.</_>
        <_>
          12 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 3 4 -1.</_>
        <_>
          12 5 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 4 10 -1.</_>
        <_>
          13 5 2 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 4 3 -1.</_>
        <_>
          11 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 1 3 -1.</_>
        <_>
          11 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 2 3 -1.</_>
        <_>
          11 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 6 5 -1.</_>
        <_>
          13 6 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 3 3 -1.</_>
        <_>
          11 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 9 11 -1.</_>
        <_>
          14 6 3 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 4 3 -1.</_>
        <_>
          11 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 5 3 -1.</_>
        <_>
          11 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 1 3 -1.</_>
        <_>
          11 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 2 3 -1.</_>
        <_>
          11 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 5 3 -1.</_>
        <_>
          11 8 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 1 3 -1.</_>
        <_>
          11 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 2 3 -1.</_>
        <_>
          11 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 3 3 -1.</_>
        <_>
          11 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 2 2 -1.</_>
        <_>
          11 9 1 1 2.</_>
        <_>
          12 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 3 3 -1.</_>
        <_>
          11 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 4 3 -1.</_>
        <_>
          11 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 1 3 -1.</_>
        <_>
          11 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 4 -1.</_>
        <_>
          13 10 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 8 2 -1.</_>
        <_>
          11 10 4 1 2.</_>
        <_>
          15 11 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 11 1 3 -1.</_>
        <_>
          11 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 12 6 3 -1.</_>
        <_>
          13 12 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 14 9 3 -1.</_>
        <_>
          14 14 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 16 3 3 -1.</_>
        <_>
          11 17 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 18 5 6 -1.</_>
        <_>
          11 20 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 18 12 6 -1.</_>
        <_>
          11 18 6 3 2.</_>
        <_>
          17 21 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 19 2 4 -1.</_>
        <_>
          11 21 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 19 12 4 -1.</_>
        <_>
          11 19 6 2 2.</_>
        <_>
          17 21 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 2 -1.</_>
        <_>
          12 20 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 3 -1.</_>
        <_>
          12 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 20 3 4 -1.</_>
        <_>
          12 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 1 -1.</_>
        <_>
          12 21 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 2 2 -1.</_>
        <_>
          11 21 1 1 2.</_>
        <_>
          12 22 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 2 -1.</_>
        <_>
          11 22 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 22 3 1 -1.</_>
        <_>
          12 22 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 23 3 1 -1.</_>
        <_>
          12 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 12 1 -1.</_>
        <_>
          18 0 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 12 10 -1.</_>
        <_>
          12 0 6 5 2.</_>
        <_>
          18 5 6 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 12 12 -1.</_>
        <_>
          12 0 6 6 2.</_>
        <_>
          18 6 6 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 2 12 -1.</_>
        <_>
          13 1 1 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 6 7 -1.</_>
        <_>
          14 1 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 6 15 -1.</_>
        <_>
          14 1 2 15 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 1 9 13 -1.</_>
        <_>
          15 1 3 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 9 4 -1.</_>
        <_>
          15 2 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 6 1 -1.</_>
        <_>
          14 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 6 9 -1.</_>
        <_>
          14 3 2 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 6 3 -1.</_>
        <_>
          12 4 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 4 2 6 -1.</_>
        <_>
          13 4 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 4 8 6 -1.</_>
        <_>
          12 4 4 3 2.</_>
        <_>
          16 7 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 3 3 -1.</_>
        <_>
          12 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 1 3 -1.</_>
        <_>
          12 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 1 3 -1.</_>
        <_>
          12 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 2 3 -1.</_>
        <_>
          12 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 3 3 -1.</_>
        <_>
          12 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 4 3 -1.</_>
        <_>
          12 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 3 3 -1.</_>
        <_>
          12 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 1 3 -1.</_>
        <_>
          12 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 2 3 -1.</_>
        <_>
          12 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 4 6 -1.</_>
        <_>
          12 9 2 3 2.</_>
        <_>
          14 12 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 6 7 -1.</_>
        <_>
          14 9 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 6 6 -1.</_>
        <_>
          12 9 3 3 2.</_>
        <_>
          15 12 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 6 2 -1.</_>
        <_>
          14 10 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 4 10 -1.</_>
        <_>
          12 10 2 5 2.</_>
        <_>
          14 15 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 4 8 -1.</_>
        <_>
          12 11 2 4 2.</_>
        <_>
          14 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 6 10 -1.</_>
        <_>
          12 11 3 5 2.</_>
        <_>
          15 16 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 8 8 -1.</_>
        <_>
          12 11 4 4 2.</_>
        <_>
          16 15 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 12 9 3 -1.</_>
        <_>
          15 12 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 12 10 8 -1.</_>
        <_>
          12 12 5 4 2.</_>
        <_>
          17 16 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 13 4 8 -1.</_>
        <_>
          12 13 2 4 2.</_>
        <_>
          14 17 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 14 3 7 -1.</_>
        <_>
          13 14 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 14 8 4 -1.</_>
        <_>
          12 14 4 2 2.</_>
        <_>
          16 16 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 16 4 3 -1.</_>
        <_>
          14 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 19 3 5 -1.</_>
        <_>
          13 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 20 3 4 -1.</_>
        <_>
          13 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 21 3 3 -1.</_>
        <_>
          13 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 22 3 2 -1.</_>
        <_>
          13 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 0 1 10 -1.</_>
        <_>
          13 5 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 1 2 12 -1.</_>
        <_>
          14 1 1 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 1 6 8 -1.</_>
        <_>
          15 1 2 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 2 3 -1.</_>
        <_>
          14 2 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 2 6 -1.</_>
        <_>
          13 2 1 3 2.</_>
        <_>
          14 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 1 4 -1.</_>
        <_>
          13 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 2 4 -1.</_>
        <_>
          13 3 1 2 2.</_>
        <_>
          14 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 6 8 -1.</_>
        <_>
          15 3 2 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 6 9 -1.</_>
        <_>
          15 4 2 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 5 -1.</_>
        <_>
          14 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 6 -1.</_>
        <_>
          14 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 6 2 3 -1.</_>
        <_>
          13 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 2 4 -1.</_>
        <_>
          14 7 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 8 2 3 -1.</_>
        <_>
          13 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 8 6 8 -1.</_>
        <_>
          13 8 3 4 2.</_>
        <_>
          16 12 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 9 2 -1.</_>
        <_>
          16 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 3 1 -1.</_>
        <_>
          14 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 3 2 -1.</_>
        <_>
          14 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 2 3 -1.</_>
        <_>
          13 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 4 8 -1.</_>
        <_>
          13 10 2 4 2.</_>
        <_>
          15 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 9 1 -1.</_>
        <_>
          16 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 11 2 3 -1.</_>
        <_>
          13 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 11 4 2 -1.</_>
        <_>
          13 11 2 1 2.</_>
        <_>
          15 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 15 6 1 -1.</_>
        <_>
          15 15 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 17 8 1 -1.</_>
        <_>
          17 17 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 19 3 5 -1.</_>
        <_>
          14 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 20 3 2 -1.</_>
        <_>
          14 20 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 21 3 3 -1.</_>
        <_>
          14 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 1 2 -1.</_>
        <_>
          14 1 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 6 -1.</_>
        <_>
          14 0 5 3 2.</_>
        <_>
          19 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 1 1 8 -1.</_>
        <_>
          14 5 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 3 8 -1.</_>
        <_>
          15 2 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 3 12 -1.</_>
        <_>
          15 2 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 7 3 -1.</_>
        <_>
          14 3 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 3 6 3 -1.</_>
        <_>
          14 4 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 1 4 -1.</_>
        <_>
          14 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 6 -1.</_>
        <_>
          15 4 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 7 -1.</_>
        <_>
          15 4 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 3 3 -1.</_>
        <_>
          14 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 7 3 -1.</_>
        <_>
          14 5 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 8 3 -1.</_>
        <_>
          14 5 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 4 10 6 -1.</_>
        <_>
          14 6 10 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 2 -1.</_>
        <_>
          15 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 3 -1.</_>
        <_>
          15 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 4 -1.</_>
        <_>
          15 5 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 2 6 -1.</_>
        <_>
          15 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 6 -1.</_>
        <_>
          15 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 5 6 -1.</_>
        <_>
          14 7 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 7 6 16 -1.</_>
        <_>
          16 7 2 16 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 8 2 6 -1.</_>
        <_>
          14 8 1 3 2.</_>
        <_>
          15 11 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 3 -1.</_>
        <_>
          15 9 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 8 -1.</_>
        <_>
          14 9 1 4 2.</_>
        <_>
          15 13 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 4 1 -1.</_>
        <_>
          16 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 9 2 -1.</_>
        <_>
          14 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 1 -1.</_>
        <_>
          15 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 1 -1.</_>
        <_>
          15 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          15 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 2 -1.</_>
        <_>
          15 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 3 -1.</_>
        <_>
          15 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 6 -1.</_>
        <_>
          14 10 1 3 2.</_>
        <_>
          15 13 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 8 -1.</_>
        <_>
          14 10 1 4 2.</_>
        <_>
          15 14 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 13 3 8 -1.</_>
        <_>
          15 13 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 18 8 6 -1.</_>
        <_>
          14 18 4 3 2.</_>
        <_>
          18 21 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 22 3 2 -1.</_>
        <_>
          15 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 0 3 11 -1.</_>
        <_>
          16 0 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 2 4 4 -1.</_>
        <_>
          15 4 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 3 2 3 -1.</_>
        <_>
          15 4 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 2 9 -1.</_>
        <_>
          16 4 1 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 6 6 -1.</_>
        <_>
          15 6 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 2 2 -1.</_>
        <_>
          16 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 3 -1.</_>
        <_>
          16 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 6 3 1 -1.</_>
        <_>
          16 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 6 3 8 -1.</_>
        <_>
          15 10 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 4 6 -1.</_>
        <_>
          15 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 6 3 -1.</_>
        <_>
          15 8 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 6 6 -1.</_>
        <_>
          15 9 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 7 3 -1.</_>
        <_>
          15 8 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 9 6 -1.</_>
        <_>
          15 9 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 2 2 -1.</_>
        <_>
          15 8 1 1 2.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 1 10 -1.</_>
        <_>
          15 14 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 5 -1.</_>
        <_>
          16 9 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 6 4 -1.</_>
        <_>
          18 9 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 15 -1.</_>
        <_>
          15 14 3 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 6 3 -1.</_>
        <_>
          15 10 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 2 1 -1.</_>
        <_>
          16 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 4 -1.</_>
        <_>
          16 10 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 3 -1.</_>
        <_>
          15 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 11 3 2 -1.</_>
        <_>
          15 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 11 3 12 -1.</_>
        <_>
          15 15 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 11 9 6 -1.</_>
        <_>
          15 13 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 14 2 10 -1.</_>
        <_>
          15 19 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 14 8 3 -1.</_>
        <_>
          19 14 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 14 5 3 -1.</_>
        <_>
          15 15 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 16 6 8 -1.</_>
        <_>
          15 16 3 4 2.</_>
        <_>
          18 20 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 17 7 3 -1.</_>
        <_>
          15 18 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 19 9 3 -1.</_>
        <_>
          15 20 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 1 -1.</_>
        <_>
          20 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 2 -1.</_>
        <_>
          20 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 6 -1.</_>
        <_>
          16 0 4 3 2.</_>
        <_>
          20 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 8 -1.</_>
        <_>
          16 0 4 4 2.</_>
        <_>
          20 4 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 12 -1.</_>
        <_>
          16 0 4 6 2.</_>
        <_>
          20 6 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 3 2 4 -1.</_>
        <_>
          16 3 1 2 2.</_>
        <_>
          17 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 3 7 3 -1.</_>
        <_>
          16 4 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 4 2 9 -1.</_>
        <_>
          16 7 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 5 4 10 -1.</_>
        <_>
          16 5 2 5 2.</_>
        <_>
          18 10 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 5 8 6 -1.</_>
        <_>
          16 5 4 3 2.</_>
        <_>
          20 8 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 2 6 -1.</_>
        <_>
          16 8 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 3 9 -1.</_>
        <_>
          16 9 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 6 6 -1.</_>
        <_>
          16 8 6 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 1 3 -1.</_>
        <_>
          16 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 1 6 -1.</_>
        <_>
          16 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 16 -1.</_>
        <_>
          17 7 1 16 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 1 -1.</_>
        <_>
          18 7 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 2 -1.</_>
        <_>
          16 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 3 -1.</_>
        <_>
          16 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 2 -1.</_>
        <_>
          18 7 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 6 -1.</_>
        <_>
          16 9 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 15 -1.</_>
        <_>
          16 12 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 6 -1.</_>
        <_>
          18 7 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 3 -1.</_>
        <_>
          16 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 6 -1.</_>
        <_>
          16 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 6 -1.</_>
        <_>
          16 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 1 2 -1.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 8 1 1 2.</_>
        <_>
          17 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 5 -1.</_>
        <_>
          17 8 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 6 2 -1.</_>
        <_>
          16 8 3 1 2.</_>
        <_>
          19 9 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 6 3 -1.</_>
        <_>
          16 9 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 1 3 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 6 3 -1.</_>
        <_>
          16 10 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 1 3 -1.</_>
        <_>
          16 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 3 -1.</_>
        <_>
          16 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 6 2 -1.</_>
        <_>
          16 10 3 1 2.</_>
        <_>
          19 11 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 4 2 -1.</_>
        <_>
          16 11 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 6 4 -1.</_>
        <_>
          16 12 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 2 -1.</_>
        <_>
          17 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 2 -1.</_>
        <_>
          16 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 3 -1.</_>
        <_>
          16 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 3 -1.</_>
        <_>
          16 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 4 2 -1.</_>
        <_>
          16 12 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 8 6 -1.</_>
        <_>
          16 11 4 3 2.</_>
        <_>
          20 14 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 12 4 8 -1.</_>
        <_>
          16 12 2 4 2.</_>
        <_>
          18 16 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 13 2 2 -1.</_>
        <_>
          16 13 1 1 2.</_>
        <_>
          17 14 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 13 4 6 -1.</_>
        <_>
          16 13 2 3 2.</_>
        <_>
          18 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 13 8 8 -1.</_>
        <_>
          20 13 4 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 14 8 5 -1.</_>
        <_>
          20 14 4 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 4 4 -1.</_>
        <_>
          18 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 4 8 -1.</_>
        <_>
          16 15 2 4 2.</_>
        <_>
          18 19 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 3 3 -1.</_>
        <_>
          16 16 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 15 6 3 -1.</_>
        <_>
          19 15 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 16 3 3 -1.</_>
        <_>
          16 17 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 18 3 3 -1.</_>
        <_>
          17 18 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 18 3 6 -1.</_>
        <_>
          17 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 20 3 4 -1.</_>
        <_>
          17 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 22 3 1 -1.</_>
        <_>
          17 22 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 0 1 8 -1.</_>
        <_>
          17 4 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 0 6 1 -1.</_>
        <_>
          20 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 2 3 3 -1.</_>
        <_>
          18 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 1 -1.</_>
        <_>
          18 3 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 4 15 -1.</_>
        <_>
          17 8 4 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 2 6 -1.</_>
        <_>
          17 4 1 3 2.</_>
        <_>
          18 7 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 5 1 9 -1.</_>
        <_>
          17 8 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 3 3 -1.</_>
        <_>
          17 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 4 3 -1.</_>
        <_>
          17 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 3 -1.</_>
        <_>
          17 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 6 -1.</_>
        <_>
          17 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 2 -1.</_>
        <_>
          17 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 3 -1.</_>
        <_>
          17 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 6 -1.</_>
        <_>
          17 9 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 3 -1.</_>
        <_>
          17 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 1 3 -1.</_>
        <_>
          17 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 2 2 -1.</_>
        <_>
          17 8 1 1 2.</_>
        <_>
          18 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 1 4 -1.</_>
        <_>
          17 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 5 14 -1.</_>
        <_>
          17 15 5 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 3 1 -1.</_>
        <_>
          18 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 1 4 -1.</_>
        <_>
          17 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 4 6 -1.</_>
        <_>
          17 11 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 7 2 -1.</_>
        <_>
          17 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 1 3 -1.</_>
        <_>
          17 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 3 14 -1.</_>
        <_>
          18 10 1 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 1 -1.</_>
        <_>
          19 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 3 -1.</_>
        <_>
          17 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 2 -1.</_>
        <_>
          17 10 2 1 2.</_>
        <_>
          19 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 6 5 -1.</_>
        <_>
          20 10 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 2 -1.</_>
        <_>
          18 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 3 -1.</_>
        <_>
          18 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 11 -1.</_>
        <_>
          18 11 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 4 -1.</_>
        <_>
          17 13 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 12 3 1 -1.</_>
        <_>
          18 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 13 4 5 -1.</_>
        <_>
          19 13 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 4 1 -1.</_>
        <_>
          19 15 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 4 3 -1.</_>
        <_>
          19 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 16 4 1 -1.</_>
        <_>
          19 16 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 18 7 3 -1.</_>
        <_>
          17 19 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 20 3 4 -1.</_>
        <_>
          18 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 3 4 -1.</_>
        <_>
          19 0 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 12 -1.</_>
        <_>
          18 0 3 6 2.</_>
        <_>
          21 6 3 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 14 -1.</_>
        <_>
          18 0 3 7 2.</_>
        <_>
          21 7 3 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 3 3 3 -1.</_>
        <_>
          19 3 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 3 2 9 -1.</_>
        <_>
          18 6 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 4 2 2 -1.</_>
        <_>
          18 4 1 1 2.</_>
        <_>
          19 5 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 4 3 2 -1.</_>
        <_>
          19 4 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 4 4 3 -1.</_>
        <_>
          18 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 5 2 2 -1.</_>
        <_>
          18 5 1 1 2.</_>
        <_>
          19 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 3 -1.</_>
        <_>
          18 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 3 3 -1.</_>
        <_>
          18 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 5 3 -1.</_>
        <_>
          18 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 1 2 -1.</_>
        <_>
          18 8 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 1 -1.</_>
        <_>
          19 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 2 2 -1.</_>
        <_>
          19 7 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 2 -1.</_>
        <_>
          19 7 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 2 3 -1.</_>
        <_>
          18 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 3 -1.</_>
        <_>
          18 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 1 3 -1.</_>
        <_>
          18 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 2 3 -1.</_>
        <_>
          18 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 6 8 -1.</_>
        <_>
          21 8 3 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 1 -1.</_>
        <_>
          19 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 2 2 -1.</_>
        <_>
          18 9 1 1 2.</_>
        <_>
          19 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 2 -1.</_>
        <_>
          19 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 4 2 -1.</_>
        <_>
          20 9 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 9 -1.</_>
        <_>
          18 12 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 1 -1.</_>
        <_>
          19 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 2 -1.</_>
        <_>
          18 10 1 1 2.</_>
        <_>
          19 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 8 -1.</_>
        <_>
          18 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 6 4 -1.</_>
        <_>
          21 10 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 2 -1.</_>
        <_>
          19 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 12 -1.</_>
        <_>
          19 11 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 3 3 -1.</_>
        <_>
          19 12 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 6 1 -1.</_>
        <_>
          21 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 3 6 -1.</_>
        <_>
          18 15 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 15 1 3 -1.</_>
        <_>
          18 16 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 15 6 5 -1.</_>
        <_>
          21 15 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 16 4 2 -1.</_>
        <_>
          20 16 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 17 3 6 -1.</_>
        <_>
          19 17 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 18 3 5 -1.</_>
        <_>
          19 18 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 0 3 1 -1.</_>
        <_>
          20 0 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 2 4 -1.</_>
        <_>
          19 6 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 5 2 2 -1.</_>
        <_>
          19 5 1 1 2.</_>
        <_>
          20 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 1 3 -1.</_>
        <_>
          19 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 2 3 -1.</_>
        <_>
          19 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 9 2 1 -1.</_>
        <_>
          20 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 10 3 14 -1.</_>
        <_>
          20 10 1 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 10 4 1 -1.</_>
        <_>
          21 10 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 11 3 12 -1.</_>
        <_>
          20 11 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 12 3 10 -1.</_>
        <_>
          20 12 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 13 3 1 -1.</_>
        <_>
          20 13 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 17 3 7 -1.</_>
        <_>
          20 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 18 3 5 -1.</_>
        <_>
          20 18 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 18 3 6 -1.</_>
        <_>
          20 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 20 4 3 -1.</_>
        <_>
          19 21 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 1 4 4 -1.</_>
        <_>
          22 1 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 4 2 3 -1.</_>
        <_>
          20 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 2 2 -1.</_>
        <_>
          20 5 1 1 2.</_>
        <_>
          21 6 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 5 2 3 -1.</_>
        <_>
          20 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 11 4 3 -1.</_>
        <_>
          20 12 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 12 3 8 -1.</_>
        <_>
          21 12 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 12 3 11 -1.</_>
        <_>
          21 12 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 12 3 12 -1.</_>
        <_>
          21 12 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 18 3 1 -1.</_>
        <_>
          21 18 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 4 1 3 -1.</_>
        <_>
          21 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 9 3 3 -1.</_>
        <_>
          21 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 9 3 6 -1.</_>
        <_>
          21 11 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 10 2 4 -1.</_>
        <_>
          21 12 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 12 3 9 -1.</_>
        <_>
          22 12 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 13 3 11 -1.</_>
        <_>
          22 13 1 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 15 3 4 -1.</_>
        <_>
          22 15 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 15 3 9 -1.</_>
        <_>
          22 15 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 3 -1.</_>
        <_>
          22 16 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 8 -1.</_>
        <_>
          22 16 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 20 3 4 -1.</_>
        <_>
          22 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 22 3 2 -1.</_>
        <_>
          22 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 10 2 3 -1.</_>
        <_>
          22 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 12 2 12 -1.</_>
        <_>
          22 16 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 20 2 3 -1.</_>
        <_>
          22 21 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 10 1 3 -1.</_>
        <_>
          23 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 17 1 6 -1.</_>
        <_>
          23 19 1 2 3.</_></rects>
      <tilted>0</tilted></_></features></cascade>
</opencv_storage>
