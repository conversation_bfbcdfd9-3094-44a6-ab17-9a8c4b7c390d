
//
// This file is auto-generated. Please don't modify it!
//
package org.opencv.video;



// C++: class DualTVL1OpticalFlow
//javadoc: DualTVL1OpticalFlow
public class DualTVL1OpticalFlow extends DenseOpticalFlow {

    protected DualTVL1OpticalFlow(long addr) { super(addr); }


    @Override
    protected void finalize() throws Throwable {
        delete(nativeObj);
    }



    // native support for java finalize()
    private static native void delete(long nativeObj);

}
