<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:51 MSK 2015 -->
<title>DescriptorExtractor</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DescriptorExtractor";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/DescriptorExtractor.html" target="_top">Frames</a></li>
<li><a href="DescriptorExtractor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class DescriptorExtractor" class="title">Class DescriptorExtractor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.DescriptorExtractor</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DescriptorExtractor</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#AKAZE">AKAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#BRIEF">BRIEF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#BRISK">BRISK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#FREAK">FREAK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_AKAZE">OPPONENT_AKAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_BRIEF">OPPONENT_BRIEF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_BRISK">OPPONENT_BRISK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_FREAK">OPPONENT_FREAK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_ORB">OPPONENT_ORB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_SIFT">OPPONENT_SIFT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#OPPONENT_SURF">OPPONENT_SURF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#ORB">ORB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#SIFT">SIFT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#SURF">SURF</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#compute-java.util.List-java.util.List-java.util.List-">compute</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
       java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">compute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
       <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/DescriptorExtractor.html" title="class in org.opencv.features2d">DescriptorExtractor</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#create-int-">create</a></span>(int&nbsp;extractorType)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#descriptorSize--">descriptorSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#descriptorType--">descriptorType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#empty--">empty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#read-java.lang.String-">read</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorExtractor.html#write-java.lang.String-">write</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="AKAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AKAZE</h4>
<pre>public static final&nbsp;int AKAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.AKAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRIEF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRIEF</h4>
<pre>public static final&nbsp;int BRIEF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.BRIEF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRISK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRISK</h4>
<pre>public static final&nbsp;int BRISK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.BRISK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FREAK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FREAK</h4>
<pre>public static final&nbsp;int FREAK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.FREAK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_AKAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_AKAZE</h4>
<pre>public static final&nbsp;int OPPONENT_AKAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_AKAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_BRIEF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_BRIEF</h4>
<pre>public static final&nbsp;int OPPONENT_BRIEF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_BRIEF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_BRISK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_BRISK</h4>
<pre>public static final&nbsp;int OPPONENT_BRISK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_BRISK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_FREAK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_FREAK</h4>
<pre>public static final&nbsp;int OPPONENT_FREAK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_FREAK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_ORB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_ORB</h4>
<pre>public static final&nbsp;int OPPONENT_ORB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_ORB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_SIFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_SIFT</h4>
<pre>public static final&nbsp;int OPPONENT_SIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_SIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPPONENT_SURF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPPONENT_SURF</h4>
<pre>public static final&nbsp;int OPPONENT_SURF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.OPPONENT_SURF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ORB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ORB</h4>
<pre>public static final&nbsp;int ORB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.ORB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SIFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SIFT</h4>
<pre>public static final&nbsp;int SIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.SIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SURF">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SURF</h4>
<pre>public static final&nbsp;int SURF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorExtractor.SURF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="compute-java.util.List-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                    java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
                    java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</pre>
</li>
</ul>
<a name="compute-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                    <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;descriptors)</pre>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/DescriptorExtractor.html" title="class in org.opencv.features2d">DescriptorExtractor</a>&nbsp;create(int&nbsp;extractorType)</pre>
</li>
</ul>
<a name="descriptorSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>descriptorSize</h4>
<pre>public&nbsp;int&nbsp;descriptorSize()</pre>
</li>
</ul>
<a name="descriptorType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>descriptorType</h4>
<pre>public&nbsp;int&nbsp;descriptorType()</pre>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
</li>
</ul>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;void&nbsp;read(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
<a name="write-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/DescriptorExtractor.html" target="_top">Frames</a></li>
<li><a href="DescriptorExtractor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
