<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>Subdiv2D</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Subdiv2D";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/Moments.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgproc/Subdiv2D.html" target="_top">Frames</a></li>
<li><a href="Subdiv2D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.imgproc</div>
<h2 title="Class Subdiv2D" class="title">Class Subdiv2D</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.imgproc.Subdiv2D</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Subdiv2D</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_DST">NEXT_AROUND_DST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_LEFT">NEXT_AROUND_LEFT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_ORG">NEXT_AROUND_ORG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#NEXT_AROUND_RIGHT">NEXT_AROUND_RIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_DST">PREV_AROUND_DST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_LEFT">PREV_AROUND_LEFT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_ORG">PREV_AROUND_ORG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PREV_AROUND_RIGHT">PREV_AROUND_RIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PTLOC_ERROR">PTLOC_ERROR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PTLOC_INSIDE">PTLOC_INSIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PTLOC_ON_EDGE">PTLOC_ON_EDGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PTLOC_OUTSIDE_RECT">PTLOC_OUTSIDE_RECT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#PTLOC_VERTEX">PTLOC_VERTEX</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#Subdiv2D--">Subdiv2D</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#Subdiv2D-org.opencv.core.Rect-">Subdiv2D</a></span>(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#edgeDst-int-">edgeDst</a></span>(int&nbsp;edge)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#edgeDst-int-org.opencv.core.Point-">edgeDst</a></span>(int&nbsp;edge,
       <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;dstpt)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#edgeOrg-int-">edgeOrg</a></span>(int&nbsp;edge)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#edgeOrg-int-org.opencv.core.Point-">edgeOrg</a></span>(int&nbsp;edge,
       <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;orgpt)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#findNearest-org.opencv.core.Point-">findNearest</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#findNearest-org.opencv.core.Point-org.opencv.core.Point-">findNearest</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
           <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;nearestPt)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#getEdge-int-int-">getEdge</a></span>(int&nbsp;edge,
       int&nbsp;nextEdgeType)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#getEdgeList-org.opencv.core.MatOfFloat4-">getEdgeList</a></span>(<a href="../../../org/opencv/core/MatOfFloat4.html" title="class in org.opencv.core">MatOfFloat4</a>&nbsp;edgeList)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#getTriangleList-org.opencv.core.MatOfFloat6-">getTriangleList</a></span>(<a href="../../../org/opencv/core/MatOfFloat6.html" title="class in org.opencv.core">MatOfFloat6</a>&nbsp;triangleList)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#getVertex-int-">getVertex</a></span>(int&nbsp;vertex)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#getVertex-int-int:A-">getVertex</a></span>(int&nbsp;vertex,
         int[]&nbsp;firstEdge)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#getVoronoiFacetList-org.opencv.core.MatOfInt-java.util.List-org.opencv.core.MatOfPoint2f-">getVoronoiFacetList</a></span>(<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;idx,
                   java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;facetList,
                   <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;facetCenters)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#initDelaunay-org.opencv.core.Rect-">initDelaunay</a></span>(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#insert-org.opencv.core.MatOfPoint2f-">insert</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;ptvec)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#insert-org.opencv.core.Point-">insert</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#locate-org.opencv.core.Point-int:A-int:A-">locate</a></span>(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
      int[]&nbsp;edge,
      int[]&nbsp;vertex)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#nextEdge-int-">nextEdge</a></span>(int&nbsp;edge)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#rotateEdge-int-int-">rotateEdge</a></span>(int&nbsp;edge,
          int&nbsp;rotate)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Subdiv2D.html#symEdge-int-">symEdge</a></span>(int&nbsp;edge)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NEXT_AROUND_DST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEXT_AROUND_DST</h4>
<pre>public static final&nbsp;int NEXT_AROUND_DST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_DST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NEXT_AROUND_LEFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEXT_AROUND_LEFT</h4>
<pre>public static final&nbsp;int NEXT_AROUND_LEFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_LEFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NEXT_AROUND_ORG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEXT_AROUND_ORG</h4>
<pre>public static final&nbsp;int NEXT_AROUND_ORG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_ORG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NEXT_AROUND_RIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEXT_AROUND_RIGHT</h4>
<pre>public static final&nbsp;int NEXT_AROUND_RIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.NEXT_AROUND_RIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREV_AROUND_DST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREV_AROUND_DST</h4>
<pre>public static final&nbsp;int PREV_AROUND_DST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_DST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREV_AROUND_LEFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREV_AROUND_LEFT</h4>
<pre>public static final&nbsp;int PREV_AROUND_LEFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_LEFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREV_AROUND_ORG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREV_AROUND_ORG</h4>
<pre>public static final&nbsp;int PREV_AROUND_ORG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_ORG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PREV_AROUND_RIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREV_AROUND_RIGHT</h4>
<pre>public static final&nbsp;int PREV_AROUND_RIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PREV_AROUND_RIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PTLOC_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTLOC_ERROR</h4>
<pre>public static final&nbsp;int PTLOC_ERROR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_ERROR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PTLOC_INSIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTLOC_INSIDE</h4>
<pre>public static final&nbsp;int PTLOC_INSIDE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_INSIDE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PTLOC_ON_EDGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTLOC_ON_EDGE</h4>
<pre>public static final&nbsp;int PTLOC_ON_EDGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_ON_EDGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PTLOC_OUTSIDE_RECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTLOC_OUTSIDE_RECT</h4>
<pre>public static final&nbsp;int PTLOC_OUTSIDE_RECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_OUTSIDE_RECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PTLOC_VERTEX">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PTLOC_VERTEX</h4>
<pre>public static final&nbsp;int PTLOC_VERTEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgproc.Subdiv2D.PTLOC_VERTEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Subdiv2D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Subdiv2D</h4>
<pre>public&nbsp;Subdiv2D()</pre>
</li>
</ul>
<a name="Subdiv2D-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Subdiv2D</h4>
<pre>public&nbsp;Subdiv2D(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="edgeDst-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgeDst</h4>
<pre>public&nbsp;int&nbsp;edgeDst(int&nbsp;edge)</pre>
</li>
</ul>
<a name="edgeDst-int-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgeDst</h4>
<pre>public&nbsp;int&nbsp;edgeDst(int&nbsp;edge,
                   <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;dstpt)</pre>
</li>
</ul>
<a name="edgeOrg-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgeOrg</h4>
<pre>public&nbsp;int&nbsp;edgeOrg(int&nbsp;edge)</pre>
</li>
</ul>
<a name="edgeOrg-int-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgeOrg</h4>
<pre>public&nbsp;int&nbsp;edgeOrg(int&nbsp;edge,
                   <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;orgpt)</pre>
</li>
</ul>
<a name="findNearest-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findNearest</h4>
<pre>public&nbsp;int&nbsp;findNearest(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</pre>
</li>
</ul>
<a name="findNearest-org.opencv.core.Point-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findNearest</h4>
<pre>public&nbsp;int&nbsp;findNearest(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
                       <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;nearestPt)</pre>
</li>
</ul>
<a name="getEdge-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEdge</h4>
<pre>public&nbsp;int&nbsp;getEdge(int&nbsp;edge,
                   int&nbsp;nextEdgeType)</pre>
</li>
</ul>
<a name="getEdgeList-org.opencv.core.MatOfFloat4-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEdgeList</h4>
<pre>public&nbsp;void&nbsp;getEdgeList(<a href="../../../org/opencv/core/MatOfFloat4.html" title="class in org.opencv.core">MatOfFloat4</a>&nbsp;edgeList)</pre>
</li>
</ul>
<a name="getTriangleList-org.opencv.core.MatOfFloat6-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTriangleList</h4>
<pre>public&nbsp;void&nbsp;getTriangleList(<a href="../../../org/opencv/core/MatOfFloat6.html" title="class in org.opencv.core">MatOfFloat6</a>&nbsp;triangleList)</pre>
</li>
</ul>
<a name="getVertex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVertex</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;getVertex(int&nbsp;vertex)</pre>
</li>
</ul>
<a name="getVertex-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVertex</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;getVertex(int&nbsp;vertex,
                       int[]&nbsp;firstEdge)</pre>
</li>
</ul>
<a name="getVoronoiFacetList-org.opencv.core.MatOfInt-java.util.List-org.opencv.core.MatOfPoint2f-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVoronoiFacetList</h4>
<pre>public&nbsp;void&nbsp;getVoronoiFacetList(<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;idx,
                                java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;facetList,
                                <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;facetCenters)</pre>
</li>
</ul>
<a name="initDelaunay-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initDelaunay</h4>
<pre>public&nbsp;void&nbsp;initDelaunay(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;rect)</pre>
</li>
</ul>
<a name="insert-org.opencv.core.MatOfPoint2f-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insert</h4>
<pre>public&nbsp;void&nbsp;insert(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;ptvec)</pre>
</li>
</ul>
<a name="insert-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insert</h4>
<pre>public&nbsp;int&nbsp;insert(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt)</pre>
</li>
</ul>
<a name="locate-org.opencv.core.Point-int:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>locate</h4>
<pre>public&nbsp;int&nbsp;locate(<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pt,
                  int[]&nbsp;edge,
                  int[]&nbsp;vertex)</pre>
</li>
</ul>
<a name="nextEdge-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextEdge</h4>
<pre>public&nbsp;int&nbsp;nextEdge(int&nbsp;edge)</pre>
</li>
</ul>
<a name="rotateEdge-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rotateEdge</h4>
<pre>public&nbsp;int&nbsp;rotateEdge(int&nbsp;edge,
                      int&nbsp;rotate)</pre>
</li>
</ul>
<a name="symEdge-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>symEdge</h4>
<pre>public&nbsp;int&nbsp;symEdge(int&nbsp;edge)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/Moments.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgproc/Subdiv2D.html" target="_top">Frames</a></li>
<li><a href="Subdiv2D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
