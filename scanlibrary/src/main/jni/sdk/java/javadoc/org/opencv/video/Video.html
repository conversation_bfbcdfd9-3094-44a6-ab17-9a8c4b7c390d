<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>Video</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Video";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/KalmanFilter.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/Video.html" target="_top">Frames</a></li>
<li><a href="Video.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class Video" class="title">Class Video</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.video.Video</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Video</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_AFFINE">MOTION_AFFINE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_EUCLIDEAN">MOTION_EUCLIDEAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_HOMOGRAPHY">MOTION_HOMOGRAPHY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#MOTION_TRANSLATION">MOTION_TRANSLATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#OPTFLOW_FARNEBACK_GAUSSIAN">OPTFLOW_FARNEBACK_GAUSSIAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#OPTFLOW_LK_GET_MIN_EIGENVALS">OPTFLOW_LK_GET_MIN_EIGENVALS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#OPTFLOW_USE_INITIAL_FLOW">OPTFLOW_USE_INITIAL_FLOW</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#Video--">Video</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-int-boolean-">buildOpticalFlowPyramid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                       java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                       int&nbsp;maxLevel,
                       boolean&nbsp;withDerivatives,
                       int&nbsp;pyrBorder,
                       int&nbsp;derivBorder,
                       boolean&nbsp;tryReuseInputImage)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowFarneback-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-int-int-int-double-int-">calcOpticalFlowFarneback</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prev,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;next,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow,
                        double&nbsp;pyr_scale,
                        int&nbsp;levels,
                        int&nbsp;winsize,
                        int&nbsp;iterations,
                        int&nbsp;poly_n,
                        double&nbsp;poly_sigma,
                        int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                    int&nbsp;maxLevel)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-int-double-">calcOpticalFlowPyrLK</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                    <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                    <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                    int&nbsp;maxLevel,
                    <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                    int&nbsp;flags,
                    double&nbsp;minEigThreshold)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#CamShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">CamShift</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
        <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
        <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorKNN--">createBackgroundSubtractorKNN</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorKNN-int-double-boolean-">createBackgroundSubtractorKNN</a></span>(int&nbsp;history,
                             double&nbsp;dist2Threshold,
                             boolean&nbsp;detectShadows)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorMOG2--">createBackgroundSubtractorMOG2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createBackgroundSubtractorMOG2-int-double-boolean-">createBackgroundSubtractorMOG2</a></span>(int&nbsp;history,
                              double&nbsp;varThreshold,
                              boolean&nbsp;detectShadows)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/video/DualTVL1OpticalFlow.html" title="class in org.opencv.video">DualTVL1OpticalFlow</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#createOptFlow_DualTVL1--">createOptFlow_DualTVL1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#estimateRigidTransform-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">estimateRigidTransform</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                      boolean&nbsp;fullAffine)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                int&nbsp;motionType)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-org.opencv.core.Mat-">findTransformECC</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                int&nbsp;motionType,
                <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/Video.html#meanShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">meanShift</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
         <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
         <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MOTION_AFFINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_AFFINE</h4>
<pre>public static final&nbsp;int MOTION_AFFINE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_AFFINE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MOTION_EUCLIDEAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_EUCLIDEAN</h4>
<pre>public static final&nbsp;int MOTION_EUCLIDEAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_EUCLIDEAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MOTION_HOMOGRAPHY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_HOMOGRAPHY</h4>
<pre>public static final&nbsp;int MOTION_HOMOGRAPHY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_HOMOGRAPHY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MOTION_TRANSLATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOTION_TRANSLATION</h4>
<pre>public static final&nbsp;int MOTION_TRANSLATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.MOTION_TRANSLATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPTFLOW_FARNEBACK_GAUSSIAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPTFLOW_FARNEBACK_GAUSSIAN</h4>
<pre>public static final&nbsp;int OPTFLOW_FARNEBACK_GAUSSIAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_FARNEBACK_GAUSSIAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPTFLOW_LK_GET_MIN_EIGENVALS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPTFLOW_LK_GET_MIN_EIGENVALS</h4>
<pre>public static final&nbsp;int OPTFLOW_LK_GET_MIN_EIGENVALS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_LK_GET_MIN_EIGENVALS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPTFLOW_USE_INITIAL_FLOW">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OPTFLOW_USE_INITIAL_FLOW</h4>
<pre>public static final&nbsp;int OPTFLOW_USE_INITIAL_FLOW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.video.Video.OPTFLOW_USE_INITIAL_FLOW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Video--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Video</h4>
<pre>public&nbsp;Video()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel)</pre>
</li>
</ul>
<a name="buildOpticalFlowPyramid-org.opencv.core.Mat-java.util.List-org.opencv.core.Size-int-boolean-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildOpticalFlowPyramid</h4>
<pre>public static&nbsp;int&nbsp;buildOpticalFlowPyramid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                          java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;pyramid,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                          int&nbsp;maxLevel,
                                          boolean&nbsp;withDerivatives,
                                          int&nbsp;pyrBorder,
                                          int&nbsp;derivBorder,
                                          boolean&nbsp;tryReuseInputImage)</pre>
</li>
</ul>
<a name="calcOpticalFlowFarneback-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-int-int-int-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowFarneback</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowFarneback(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prev,
                                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;next,
                                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;flow,
                                            double&nbsp;pyr_scale,
                                            int&nbsp;levels,
                                            int&nbsp;winsize,
                                            int&nbsp;iterations,
                                            int&nbsp;poly_n,
                                            double&nbsp;poly_sigma,
                                            int&nbsp;flags)</pre>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err)</pre>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                        int&nbsp;maxLevel)</pre>
</li>
</ul>
<a name="calcOpticalFlowPyrLK-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfByte-org.opencv.core.MatOfFloat-org.opencv.core.Size-int-org.opencv.core.TermCriteria-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcOpticalFlowPyrLK</h4>
<pre>public static&nbsp;void&nbsp;calcOpticalFlowPyrLK(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;prevImg,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nextImg,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;prevPts,
                                        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;nextPts,
                                        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;status,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;err,
                                        <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;winSize,
                                        int&nbsp;maxLevel,
                                        <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                                        int&nbsp;flags,
                                        double&nbsp;minEigThreshold)</pre>
</li>
</ul>
<a name="CamShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CamShift</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core">RotatedRect</a>&nbsp;CamShift(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
                                   <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
                                   <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
<a name="createBackgroundSubtractorKNN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorKNN</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a>&nbsp;createBackgroundSubtractorKNN()</pre>
</li>
</ul>
<a name="createBackgroundSubtractorKNN-int-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorKNN</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video">BackgroundSubtractorKNN</a>&nbsp;createBackgroundSubtractorKNN(int&nbsp;history,
                                                                    double&nbsp;dist2Threshold,
                                                                    boolean&nbsp;detectShadows)</pre>
</li>
</ul>
<a name="createBackgroundSubtractorMOG2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorMOG2</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;createBackgroundSubtractorMOG2()</pre>
</li>
</ul>
<a name="createBackgroundSubtractorMOG2-int-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundSubtractorMOG2</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html" title="class in org.opencv.video">BackgroundSubtractorMOG2</a>&nbsp;createBackgroundSubtractorMOG2(int&nbsp;history,
                                                                      double&nbsp;varThreshold,
                                                                      boolean&nbsp;detectShadows)</pre>
</li>
</ul>
<a name="createOptFlow_DualTVL1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createOptFlow_DualTVL1</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/video/DualTVL1OpticalFlow.html" title="class in org.opencv.video">DualTVL1OpticalFlow</a>&nbsp;createOptFlow_DualTVL1()</pre>
</li>
</ul>
<a name="estimateRigidTransform-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateRigidTransform</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;estimateRigidTransform(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                         boolean&nbsp;fullAffine)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                                      int&nbsp;motionType)</pre>
</li>
</ul>
<a name="findTransformECC-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findTransformECC</h4>
<pre>public static&nbsp;double&nbsp;findTransformECC(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;templateImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;warpMatrix,
                                      int&nbsp;motionType,
                                      <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inputMask)</pre>
</li>
</ul>
<a name="meanShift-org.opencv.core.Mat-org.opencv.core.Rect-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>meanShift</h4>
<pre>public static&nbsp;int&nbsp;meanShift(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;probImage,
                            <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;window,
                            <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/KalmanFilter.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/Video.html" target="_top">Frames</a></li>
<li><a href="Video.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
