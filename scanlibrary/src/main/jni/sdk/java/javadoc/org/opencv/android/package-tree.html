<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>org.opencv.android Class Hierarchy</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.android Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li><a href="../../../org/opencv/calib3d/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.android</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/BaseLoaderCallback.html" title="class in org.opencv.android"><span class="typeNameLink">BaseLoaderCallback</span></a> (implements org.opencv.android.<a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a>)</li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/FpsMeter.html" title="class in org.opencv.android"><span class="typeNameLink">FpsMeter</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">JavaCameraView.JavaCameraSizeAccessor</span></a> (implements org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a>)</li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android"><span class="typeNameLink">OpenCVLoader</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/Utils.html" title="class in org.opencv.android"><span class="typeNameLink">Utils</span></a></li>
<li type="circle">android.view.View (implements android.view.accessibility.AccessibilityEventSource, android.graphics.drawable.Drawable.Callback, android.view.KeyEvent.Callback)
<ul>
<li type="circle">android.view.SurfaceView
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase</span></a> (implements android.view.SurfaceHolder.Callback)
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/JavaCameraView.html" title="class in org.opencv.android"><span class="typeNameLink">JavaCameraView</span></a> (implements android.hardware.Camera.PreviewCallback)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewFrame.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.CvCameraViewFrame</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.CvCameraViewListener</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener2.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.CvCameraViewListener2</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android"><span class="typeNameLink">CameraBridgeViewBase.ListItemAccessor</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">InstallCallbackInterface</span></a></li>
<li type="circle">org.opencv.android.<a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">LoaderCallbackInterface</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li><a href="../../../org/opencv/calib3d/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
