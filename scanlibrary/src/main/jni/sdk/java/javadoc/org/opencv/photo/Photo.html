<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>Photo</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Photo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9,"i48":9,"i49":9,"i50":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/photo/Photo.html" target="_top">Frames</a></li>
<li><a href="Photo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.photo</div>
<h2 title="Class Photo" class="title">Class Photo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.photo.Photo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Photo</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#INPAINT_NS">INPAINT_NS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#INPAINT_TELEA">INPAINT_TELEA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#LDR_SIZE">LDR_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#MIXED_CLONE">MIXED_CLONE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#MONOCHROME_TRANSFER">MONOCHROME_TRANSFER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#NORMAL_CLONE">NORMAL_CLONE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#NORMCONV_FILTER">NORMCONV_FILTER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#RECURS_FILTER">RECURS_FILTER</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#Photo--">Photo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">colorChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">colorChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;red_mul,
           float&nbsp;green_mul,
           float&nbsp;blue_mul)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createAlignMTB--">createAlignMTB</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createAlignMTB-int-int-boolean-">createAlignMTB</a></span>(int&nbsp;max_bits,
              int&nbsp;exclude_range,
              boolean&nbsp;cut)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateDebevec--">createCalibrateDebevec</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateDebevec-int-float-boolean-">createCalibrateDebevec</a></span>(int&nbsp;samples,
                      float&nbsp;lambda,
                      boolean&nbsp;random)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateRobertson--">createCalibrateRobertson</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createCalibrateRobertson-int-float-">createCalibrateRobertson</a></span>(int&nbsp;max_iter,
                        float&nbsp;threshold)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeDebevec--">createMergeDebevec</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeMertens--">createMergeMertens</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeMertens-float-float-float-">createMergeMertens</a></span>(float&nbsp;contrast_weight,
                  float&nbsp;saturation_weight,
                  float&nbsp;exposure_weight)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createMergeRobertson--">createMergeRobertson</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemap--">createTonemap</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemap-float-">createTonemap</a></span>(float&nbsp;gamma)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDrago--">createTonemapDrago</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDrago-float-float-float-">createTonemapDrago</a></span>(float&nbsp;gamma,
                  float&nbsp;saturation,
                  float&nbsp;bias)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDurand.html" title="class in org.opencv.photo">TonemapDurand</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDurand--">createTonemapDurand</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapDurand.html" title="class in org.opencv.photo">TonemapDurand</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapDurand-float-float-float-float-float-">createTonemapDurand</a></span>(float&nbsp;gamma,
                   float&nbsp;contrast,
                   float&nbsp;saturation,
                   float&nbsp;sigma_space,
                   float&nbsp;sigma_color)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapMantiuk--">createTonemapMantiuk</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapMantiuk-float-float-float-">createTonemapMantiuk</a></span>(float&nbsp;gamma,
                    float&nbsp;scale,
                    float&nbsp;saturation)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard--">createTonemapReinhard</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#createTonemapReinhard-float-float-float-float-">createTonemapReinhard</a></span>(float&nbsp;gamma,
                     float&nbsp;intensity,
                     float&nbsp;light_adapt,
                     float&nbsp;color_adapt)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#decolor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decolor</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grayscale,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;color_boost)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#denoise_TVL1-java.util.List-org.opencv.core.Mat-">denoise_TVL1</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#denoise_TVL1-java.util.List-org.opencv.core.Mat-double-int-">denoise_TVL1</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
            double&nbsp;lambda,
            int&nbsp;niters)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-">detailEnhance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">detailEnhance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             float&nbsp;sigma_s,
             float&nbsp;sigma_r)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-">edgePreservingFilter</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-float-float-">edgePreservingFilter</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    int&nbsp;flags,
                    float&nbsp;sigma_s,
                    float&nbsp;sigma_r)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-int-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    float&nbsp;h,
                    int&nbsp;templateWindowSize,
                    int&nbsp;searchWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-int-int-">fastNlMeansDenoising</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                    int&nbsp;templateWindowSize,
                    int&nbsp;searchWindowSize,
                    int&nbsp;normType)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-int-">fastNlMeansDenoisingColored</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           float&nbsp;h,
                           float&nbsp;hColor,
                           int&nbsp;templateWindowSize,
                           int&nbsp;searchWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-int-int-">fastNlMeansDenoisingColoredMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                int&nbsp;imgToDenoiseIndex,
                                int&nbsp;temporalWindowSize,
                                float&nbsp;h,
                                float&nbsp;hColor,
                                int&nbsp;templateWindowSize,
                                int&nbsp;searchWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         float&nbsp;h,
                         int&nbsp;templateWindowSize,
                         int&nbsp;searchWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-int-int-">fastNlMeansDenoisingMulti</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         int&nbsp;imgToDenoiseIndex,
                         int&nbsp;temporalWindowSize,
                         <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                         int&nbsp;templateWindowSize,
                         int&nbsp;searchWindowSize,
                         int&nbsp;normType)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">illuminationChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">illuminationChange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                  float&nbsp;alpha,
                  float&nbsp;beta)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#inpaint-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">inpaint</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inpaintMask,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
       double&nbsp;inpaintRadius,
       int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">pencilSketch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">pencilSketch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
            float&nbsp;sigma_s,
            float&nbsp;sigma_r,
            float&nbsp;shade_factor)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#seamlessClone-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Point-org.opencv.core.Mat-int-">seamlessClone</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
             <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;p,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blend,
             int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#stylization-org.opencv.core.Mat-org.opencv.core.Mat-">stylization</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#stylization-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">stylization</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           float&nbsp;sigma_s,
           float&nbsp;sigma_r)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">textureFlattening</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/photo/Photo.html#textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-">textureFlattening</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                 float&nbsp;low_threshold,
                 float&nbsp;high_threshold,
                 int&nbsp;kernel_size)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="INPAINT_NS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INPAINT_NS</h4>
<pre>public static final&nbsp;int INPAINT_NS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.INPAINT_NS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INPAINT_TELEA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INPAINT_TELEA</h4>
<pre>public static final&nbsp;int INPAINT_TELEA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.INPAINT_TELEA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LDR_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LDR_SIZE</h4>
<pre>public static final&nbsp;int LDR_SIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.LDR_SIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MIXED_CLONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIXED_CLONE</h4>
<pre>public static final&nbsp;int MIXED_CLONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.MIXED_CLONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MONOCHROME_TRANSFER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MONOCHROME_TRANSFER</h4>
<pre>public static final&nbsp;int MONOCHROME_TRANSFER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.MONOCHROME_TRANSFER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORMAL_CLONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORMAL_CLONE</h4>
<pre>public static final&nbsp;int NORMAL_CLONE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMAL_CLONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORMCONV_FILTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORMCONV_FILTER</h4>
<pre>public static final&nbsp;int NORMCONV_FILTER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMCONV_FILTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RECURS_FILTER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RECURS_FILTER</h4>
<pre>public static final&nbsp;int RECURS_FILTER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.photo.Photo.RECURS_FILTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Photo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Photo</h4>
<pre>public&nbsp;Photo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorChange</h4>
<pre>public static&nbsp;void&nbsp;colorChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="colorChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorChange</h4>
<pre>public static&nbsp;void&nbsp;colorChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;red_mul,
                               float&nbsp;green_mul,
                               float&nbsp;blue_mul)</pre>
</li>
</ul>
<a name="createAlignMTB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlignMTB</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;createAlignMTB()</pre>
</li>
</ul>
<a name="createAlignMTB-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlignMTB</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a>&nbsp;createAlignMTB(int&nbsp;max_bits,
                                      int&nbsp;exclude_range,
                                      boolean&nbsp;cut)</pre>
</li>
</ul>
<a name="createCalibrateDebevec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a>&nbsp;createCalibrateDebevec()</pre>
</li>
</ul>
<a name="createCalibrateDebevec-int-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a>&nbsp;createCalibrateDebevec(int&nbsp;samples,
                                                      float&nbsp;lambda,
                                                      boolean&nbsp;random)</pre>
</li>
</ul>
<a name="createCalibrateRobertson--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a>&nbsp;createCalibrateRobertson()</pre>
</li>
</ul>
<a name="createCalibrateRobertson-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalibrateRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a>&nbsp;createCalibrateRobertson(int&nbsp;max_iter,
                                                          float&nbsp;threshold)</pre>
</li>
</ul>
<a name="createMergeDebevec--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeDebevec</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a>&nbsp;createMergeDebevec()</pre>
</li>
</ul>
<a name="createMergeMertens--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeMertens</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a>&nbsp;createMergeMertens()</pre>
</li>
</ul>
<a name="createMergeMertens-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeMertens</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a>&nbsp;createMergeMertens(float&nbsp;contrast_weight,
                                              float&nbsp;saturation_weight,
                                              float&nbsp;exposure_weight)</pre>
</li>
</ul>
<a name="createMergeRobertson--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMergeRobertson</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a>&nbsp;createMergeRobertson()</pre>
</li>
</ul>
<a name="createTonemap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemap</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a>&nbsp;createTonemap()</pre>
</li>
</ul>
<a name="createTonemap-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemap</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo">Tonemap</a>&nbsp;createTonemap(float&nbsp;gamma)</pre>
</li>
</ul>
<a name="createTonemapDrago--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDrago</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a>&nbsp;createTonemapDrago()</pre>
</li>
</ul>
<a name="createTonemapDrago-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDrago</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a>&nbsp;createTonemapDrago(float&nbsp;gamma,
                                              float&nbsp;saturation,
                                              float&nbsp;bias)</pre>
</li>
</ul>
<a name="createTonemapDurand--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDurand</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDurand.html" title="class in org.opencv.photo">TonemapDurand</a>&nbsp;createTonemapDurand()</pre>
</li>
</ul>
<a name="createTonemapDurand-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapDurand</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapDurand.html" title="class in org.opencv.photo">TonemapDurand</a>&nbsp;createTonemapDurand(float&nbsp;gamma,
                                                float&nbsp;contrast,
                                                float&nbsp;saturation,
                                                float&nbsp;sigma_space,
                                                float&nbsp;sigma_color)</pre>
</li>
</ul>
<a name="createTonemapMantiuk--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapMantiuk</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a>&nbsp;createTonemapMantiuk()</pre>
</li>
</ul>
<a name="createTonemapMantiuk-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapMantiuk</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a>&nbsp;createTonemapMantiuk(float&nbsp;gamma,
                                                  float&nbsp;scale,
                                                  float&nbsp;saturation)</pre>
</li>
</ul>
<a name="createTonemapReinhard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard()</pre>
</li>
</ul>
<a name="createTonemapReinhard-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTonemapReinhard</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/photo/TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a>&nbsp;createTonemapReinhard(float&nbsp;gamma,
                                                    float&nbsp;intensity,
                                                    float&nbsp;light_adapt,
                                                    float&nbsp;color_adapt)</pre>
</li>
</ul>
<a name="decolor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decolor</h4>
<pre>public static&nbsp;void&nbsp;decolor(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grayscale,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;color_boost)</pre>
</li>
</ul>
<a name="denoise_TVL1-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>denoise_TVL1</h4>
<pre>public static&nbsp;void&nbsp;denoise_TVL1(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</pre>
</li>
</ul>
<a name="denoise_TVL1-java.util.List-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>denoise_TVL1</h4>
<pre>public static&nbsp;void&nbsp;denoise_TVL1(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
                                double&nbsp;lambda,
                                int&nbsp;niters)</pre>
</li>
</ul>
<a name="detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detailEnhance</h4>
<pre>public static&nbsp;void&nbsp;detailEnhance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="detailEnhance-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detailEnhance</h4>
<pre>public static&nbsp;void&nbsp;detailEnhance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 float&nbsp;sigma_s,
                                 float&nbsp;sigma_r)</pre>
</li>
</ul>
<a name="edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgePreservingFilter</h4>
<pre>public static&nbsp;void&nbsp;edgePreservingFilter(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="edgePreservingFilter-org.opencv.core.Mat-org.opencv.core.Mat-int-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>edgePreservingFilter</h4>
<pre>public static&nbsp;void&nbsp;edgePreservingFilter(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        int&nbsp;flags,
                                        float&nbsp;sigma_s,
                                        float&nbsp;sigma_r)</pre>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        float&nbsp;h,
                                        int&nbsp;templateWindowSize,
                                        int&nbsp;searchWindowSize)</pre>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</pre>
</li>
</ul>
<a name="fastNlMeansDenoising-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfFloat-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoising</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoising(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                        int&nbsp;templateWindowSize,
                                        int&nbsp;searchWindowSize,
                                        int&nbsp;normType)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingColored-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColored</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColored(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                               float&nbsp;h,
                                               float&nbsp;hColor,
                                               int&nbsp;templateWindowSize,
                                               int&nbsp;searchWindowSize)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingColoredMulti-java.util.List-org.opencv.core.Mat-int-int-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingColoredMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingColoredMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                                    int&nbsp;imgToDenoiseIndex,
                                                    int&nbsp;temporalWindowSize,
                                                    float&nbsp;h,
                                                    float&nbsp;hColor,
                                                    int&nbsp;templateWindowSize,
                                                    int&nbsp;searchWindowSize)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             float&nbsp;h,
                                             int&nbsp;templateWindowSize,
                                             int&nbsp;searchWindowSize)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</pre>
</li>
</ul>
<a name="fastNlMeansDenoisingMulti-java.util.List-org.opencv.core.Mat-int-int-org.opencv.core.MatOfFloat-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastNlMeansDenoisingMulti</h4>
<pre>public static&nbsp;void&nbsp;fastNlMeansDenoisingMulti(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                             int&nbsp;imgToDenoiseIndex,
                                             int&nbsp;temporalWindowSize,
                                             <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
                                             int&nbsp;templateWindowSize,
                                             int&nbsp;searchWindowSize,
                                             int&nbsp;normType)</pre>
</li>
</ul>
<a name="illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>illuminationChange</h4>
<pre>public static&nbsp;void&nbsp;illuminationChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="illuminationChange-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>illuminationChange</h4>
<pre>public static&nbsp;void&nbsp;illuminationChange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                      float&nbsp;alpha,
                                      float&nbsp;beta)</pre>
</li>
</ul>
<a name="inpaint-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inpaint</h4>
<pre>public static&nbsp;void&nbsp;inpaint(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inpaintMask,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           double&nbsp;inpaintRadius,
                           int&nbsp;flags)</pre>
</li>
</ul>
<a name="pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pencilSketch</h4>
<pre>public static&nbsp;void&nbsp;pencilSketch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2)</pre>
</li>
</ul>
<a name="pencilSketch-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pencilSketch</h4>
<pre>public static&nbsp;void&nbsp;pencilSketch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
                                float&nbsp;sigma_s,
                                float&nbsp;sigma_r,
                                float&nbsp;shade_factor)</pre>
</li>
</ul>
<a name="seamlessClone-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Point-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>seamlessClone</h4>
<pre>public static&nbsp;void&nbsp;seamlessClone(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                 <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;p,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blend,
                                 int&nbsp;flags)</pre>
</li>
</ul>
<a name="stylization-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stylization</h4>
<pre>public static&nbsp;void&nbsp;stylization(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="stylization-org.opencv.core.Mat-org.opencv.core.Mat-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stylization</h4>
<pre>public static&nbsp;void&nbsp;stylization(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               float&nbsp;sigma_s,
                               float&nbsp;sigma_r)</pre>
</li>
</ul>
<a name="textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>textureFlattening</h4>
<pre>public static&nbsp;void&nbsp;textureFlattening(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="textureFlattening-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-float-float-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>textureFlattening</h4>
<pre>public static&nbsp;void&nbsp;textureFlattening(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                     float&nbsp;low_threshold,
                                     float&nbsp;high_threshold,
                                     int&nbsp;kernel_size)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/photo/MergeRobertson.html" title="class in org.opencv.photo"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/photo/Tonemap.html" title="class in org.opencv.photo"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/photo/Photo.html" target="_top">Frames</a></li>
<li><a href="Photo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
