<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>Moments</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Moments";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/LineSegmentDetector.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/imgproc/Subdiv2D.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgproc/Moments.html" target="_top">Frames</a></li>
<li><a href="Moments.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.imgproc</div>
<h2 title="Class Moments" class="title">Class Moments</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.imgproc.Moments</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Moments</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m00">m00</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m01">m01</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m02">m02</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m03">m03</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m10">m10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m11">m11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m12">m12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m20">m20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m21">m21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#m30">m30</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu02">mu02</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu03">mu03</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu11">mu11</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu12">mu12</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu20">mu20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu21">mu21</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#mu30">mu30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu02">nu02</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu03">nu03</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu11">nu11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu12">nu12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu20">nu20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu21">nu21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#nu30">nu30</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#Moments--">Moments</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#Moments-double:A-">Moments</a></span>(double[]&nbsp;vals)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#Moments-double-double-double-double-double-double-double-double-double-double-">Moments</a></span>(double&nbsp;m00,
       double&nbsp;m10,
       double&nbsp;m01,
       double&nbsp;m20,
       double&nbsp;m11,
       double&nbsp;m02,
       double&nbsp;m30,
       double&nbsp;m21,
       double&nbsp;m12,
       double&nbsp;m03)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m00--">get_m00</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m01--">get_m01</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m02--">get_m02</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m03--">get_m03</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m10--">get_m10</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m11--">get_m11</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m12--">get_m12</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m20--">get_m20</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m21--">get_m21</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_m30--">get_m30</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu02--">get_mu02</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu03--">get_mu03</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu11--">get_mu11</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu12--">get_mu12</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu20--">get_mu20</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu21--">get_mu21</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_mu30--">get_mu30</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu02--">get_nu02</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu03--">get_nu03</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu11--">get_nu11</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu12--">get_nu12</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu20--">get_nu20</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu21--">get_nu21</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#get_nu30--">get_nu30</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m00-double-">set_m00</a></span>(double&nbsp;m00)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m01-double-">set_m01</a></span>(double&nbsp;m01)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m02-double-">set_m02</a></span>(double&nbsp;m02)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m03-double-">set_m03</a></span>(double&nbsp;m03)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m10-double-">set_m10</a></span>(double&nbsp;m10)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m11-double-">set_m11</a></span>(double&nbsp;m11)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m12-double-">set_m12</a></span>(double&nbsp;m12)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m20-double-">set_m20</a></span>(double&nbsp;m20)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m21-double-">set_m21</a></span>(double&nbsp;m21)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_m30-double-">set_m30</a></span>(double&nbsp;m30)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu02-double-">set_mu02</a></span>(double&nbsp;mu02)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu03-double-">set_mu03</a></span>(double&nbsp;mu03)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu11-double-">set_mu11</a></span>(double&nbsp;mu11)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu12-double-">set_mu12</a></span>(double&nbsp;mu12)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu20-double-">set_mu20</a></span>(double&nbsp;mu20)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu21-double-">set_mu21</a></span>(double&nbsp;mu21)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_mu30-double-">set_mu30</a></span>(double&nbsp;mu30)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu02-double-">set_nu02</a></span>(double&nbsp;nu02)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu03-double-">set_nu03</a></span>(double&nbsp;nu03)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu11-double-">set_nu11</a></span>(double&nbsp;nu11)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu12-double-">set_nu12</a></span>(double&nbsp;nu12)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu20-double-">set_nu20</a></span>(double&nbsp;nu20)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu21-double-">set_nu21</a></span>(double&nbsp;nu21)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set_nu30-double-">set_nu30</a></span>(double&nbsp;nu30)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#set-double:A-">set</a></span>(double[]&nbsp;vals)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgproc/Moments.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="m00">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m00</h4>
<pre>public&nbsp;double m00</pre>
</li>
</ul>
<a name="m01">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m01</h4>
<pre>public&nbsp;double m01</pre>
</li>
</ul>
<a name="m02">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m02</h4>
<pre>public&nbsp;double m02</pre>
</li>
</ul>
<a name="m03">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m03</h4>
<pre>public&nbsp;double m03</pre>
</li>
</ul>
<a name="m10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m10</h4>
<pre>public&nbsp;double m10</pre>
</li>
</ul>
<a name="m11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m11</h4>
<pre>public&nbsp;double m11</pre>
</li>
</ul>
<a name="m12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m12</h4>
<pre>public&nbsp;double m12</pre>
</li>
</ul>
<a name="m20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m20</h4>
<pre>public&nbsp;double m20</pre>
</li>
</ul>
<a name="m21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m21</h4>
<pre>public&nbsp;double m21</pre>
</li>
</ul>
<a name="m30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m30</h4>
<pre>public&nbsp;double m30</pre>
</li>
</ul>
<a name="mu02">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu02</h4>
<pre>public&nbsp;double mu02</pre>
</li>
</ul>
<a name="mu03">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu03</h4>
<pre>public&nbsp;double mu03</pre>
</li>
</ul>
<a name="mu11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu11</h4>
<pre>public&nbsp;double mu11</pre>
</li>
</ul>
<a name="mu12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu12</h4>
<pre>public&nbsp;double mu12</pre>
</li>
</ul>
<a name="mu20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu20</h4>
<pre>public&nbsp;double mu20</pre>
</li>
</ul>
<a name="mu21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu21</h4>
<pre>public&nbsp;double mu21</pre>
</li>
</ul>
<a name="mu30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mu30</h4>
<pre>public&nbsp;double mu30</pre>
</li>
</ul>
<a name="nu02">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nu02</h4>
<pre>public&nbsp;double nu02</pre>
</li>
</ul>
<a name="nu03">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nu03</h4>
<pre>public&nbsp;double nu03</pre>
</li>
</ul>
<a name="nu11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nu11</h4>
<pre>public&nbsp;double nu11</pre>
</li>
</ul>
<a name="nu12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nu12</h4>
<pre>public&nbsp;double nu12</pre>
</li>
</ul>
<a name="nu20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nu20</h4>
<pre>public&nbsp;double nu20</pre>
</li>
</ul>
<a name="nu21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nu21</h4>
<pre>public&nbsp;double nu21</pre>
</li>
</ul>
<a name="nu30">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>nu30</h4>
<pre>public&nbsp;double nu30</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Moments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Moments</h4>
<pre>public&nbsp;Moments()</pre>
</li>
</ul>
<a name="Moments-double:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Moments</h4>
<pre>public&nbsp;Moments(double[]&nbsp;vals)</pre>
</li>
</ul>
<a name="Moments-double-double-double-double-double-double-double-double-double-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Moments</h4>
<pre>public&nbsp;Moments(double&nbsp;m00,
               double&nbsp;m10,
               double&nbsp;m01,
               double&nbsp;m20,
               double&nbsp;m11,
               double&nbsp;m02,
               double&nbsp;m30,
               double&nbsp;m21,
               double&nbsp;m12,
               double&nbsp;m03)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="get_m00--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m00</h4>
<pre>public&nbsp;double&nbsp;get_m00()</pre>
</li>
</ul>
<a name="get_m01--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m01</h4>
<pre>public&nbsp;double&nbsp;get_m01()</pre>
</li>
</ul>
<a name="get_m02--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m02</h4>
<pre>public&nbsp;double&nbsp;get_m02()</pre>
</li>
</ul>
<a name="get_m03--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m03</h4>
<pre>public&nbsp;double&nbsp;get_m03()</pre>
</li>
</ul>
<a name="get_m10--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m10</h4>
<pre>public&nbsp;double&nbsp;get_m10()</pre>
</li>
</ul>
<a name="get_m11--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m11</h4>
<pre>public&nbsp;double&nbsp;get_m11()</pre>
</li>
</ul>
<a name="get_m12--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m12</h4>
<pre>public&nbsp;double&nbsp;get_m12()</pre>
</li>
</ul>
<a name="get_m20--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m20</h4>
<pre>public&nbsp;double&nbsp;get_m20()</pre>
</li>
</ul>
<a name="get_m21--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m21</h4>
<pre>public&nbsp;double&nbsp;get_m21()</pre>
</li>
</ul>
<a name="get_m30--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_m30</h4>
<pre>public&nbsp;double&nbsp;get_m30()</pre>
</li>
</ul>
<a name="get_mu02--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu02</h4>
<pre>public&nbsp;double&nbsp;get_mu02()</pre>
</li>
</ul>
<a name="get_mu03--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu03</h4>
<pre>public&nbsp;double&nbsp;get_mu03()</pre>
</li>
</ul>
<a name="get_mu11--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu11</h4>
<pre>public&nbsp;double&nbsp;get_mu11()</pre>
</li>
</ul>
<a name="get_mu12--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu12</h4>
<pre>public&nbsp;double&nbsp;get_mu12()</pre>
</li>
</ul>
<a name="get_mu20--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu20</h4>
<pre>public&nbsp;double&nbsp;get_mu20()</pre>
</li>
</ul>
<a name="get_mu21--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu21</h4>
<pre>public&nbsp;double&nbsp;get_mu21()</pre>
</li>
</ul>
<a name="get_mu30--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_mu30</h4>
<pre>public&nbsp;double&nbsp;get_mu30()</pre>
</li>
</ul>
<a name="get_nu02--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu02</h4>
<pre>public&nbsp;double&nbsp;get_nu02()</pre>
</li>
</ul>
<a name="get_nu03--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu03</h4>
<pre>public&nbsp;double&nbsp;get_nu03()</pre>
</li>
</ul>
<a name="get_nu11--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu11</h4>
<pre>public&nbsp;double&nbsp;get_nu11()</pre>
</li>
</ul>
<a name="get_nu12--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu12</h4>
<pre>public&nbsp;double&nbsp;get_nu12()</pre>
</li>
</ul>
<a name="get_nu20--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu20</h4>
<pre>public&nbsp;double&nbsp;get_nu20()</pre>
</li>
</ul>
<a name="get_nu21--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu21</h4>
<pre>public&nbsp;double&nbsp;get_nu21()</pre>
</li>
</ul>
<a name="get_nu30--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_nu30</h4>
<pre>public&nbsp;double&nbsp;get_nu30()</pre>
</li>
</ul>
<a name="set_m00-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m00</h4>
<pre>public&nbsp;void&nbsp;set_m00(double&nbsp;m00)</pre>
</li>
</ul>
<a name="set_m01-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m01</h4>
<pre>public&nbsp;void&nbsp;set_m01(double&nbsp;m01)</pre>
</li>
</ul>
<a name="set_m02-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m02</h4>
<pre>public&nbsp;void&nbsp;set_m02(double&nbsp;m02)</pre>
</li>
</ul>
<a name="set_m03-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m03</h4>
<pre>public&nbsp;void&nbsp;set_m03(double&nbsp;m03)</pre>
</li>
</ul>
<a name="set_m10-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m10</h4>
<pre>public&nbsp;void&nbsp;set_m10(double&nbsp;m10)</pre>
</li>
</ul>
<a name="set_m11-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m11</h4>
<pre>public&nbsp;void&nbsp;set_m11(double&nbsp;m11)</pre>
</li>
</ul>
<a name="set_m12-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m12</h4>
<pre>public&nbsp;void&nbsp;set_m12(double&nbsp;m12)</pre>
</li>
</ul>
<a name="set_m20-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m20</h4>
<pre>public&nbsp;void&nbsp;set_m20(double&nbsp;m20)</pre>
</li>
</ul>
<a name="set_m21-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m21</h4>
<pre>public&nbsp;void&nbsp;set_m21(double&nbsp;m21)</pre>
</li>
</ul>
<a name="set_m30-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_m30</h4>
<pre>public&nbsp;void&nbsp;set_m30(double&nbsp;m30)</pre>
</li>
</ul>
<a name="set_mu02-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu02</h4>
<pre>public&nbsp;void&nbsp;set_mu02(double&nbsp;mu02)</pre>
</li>
</ul>
<a name="set_mu03-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu03</h4>
<pre>public&nbsp;void&nbsp;set_mu03(double&nbsp;mu03)</pre>
</li>
</ul>
<a name="set_mu11-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu11</h4>
<pre>public&nbsp;void&nbsp;set_mu11(double&nbsp;mu11)</pre>
</li>
</ul>
<a name="set_mu12-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu12</h4>
<pre>public&nbsp;void&nbsp;set_mu12(double&nbsp;mu12)</pre>
</li>
</ul>
<a name="set_mu20-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu20</h4>
<pre>public&nbsp;void&nbsp;set_mu20(double&nbsp;mu20)</pre>
</li>
</ul>
<a name="set_mu21-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu21</h4>
<pre>public&nbsp;void&nbsp;set_mu21(double&nbsp;mu21)</pre>
</li>
</ul>
<a name="set_mu30-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_mu30</h4>
<pre>public&nbsp;void&nbsp;set_mu30(double&nbsp;mu30)</pre>
</li>
</ul>
<a name="set_nu02-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu02</h4>
<pre>public&nbsp;void&nbsp;set_nu02(double&nbsp;nu02)</pre>
</li>
</ul>
<a name="set_nu03-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu03</h4>
<pre>public&nbsp;void&nbsp;set_nu03(double&nbsp;nu03)</pre>
</li>
</ul>
<a name="set_nu11-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu11</h4>
<pre>public&nbsp;void&nbsp;set_nu11(double&nbsp;nu11)</pre>
</li>
</ul>
<a name="set_nu12-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu12</h4>
<pre>public&nbsp;void&nbsp;set_nu12(double&nbsp;nu12)</pre>
</li>
</ul>
<a name="set_nu20-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu20</h4>
<pre>public&nbsp;void&nbsp;set_nu20(double&nbsp;nu20)</pre>
</li>
</ul>
<a name="set_nu21-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu21</h4>
<pre>public&nbsp;void&nbsp;set_nu21(double&nbsp;nu21)</pre>
</li>
</ul>
<a name="set_nu30-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_nu30</h4>
<pre>public&nbsp;void&nbsp;set_nu30(double&nbsp;nu30)</pre>
</li>
</ul>
<a name="set-double:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre>public&nbsp;void&nbsp;set(double[]&nbsp;vals)</pre>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/imgproc/LineSegmentDetector.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/imgproc/Subdiv2D.html" title="class in org.opencv.imgproc"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgproc/Moments.html" target="_top">Frames</a></li>
<li><a href="Moments.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
