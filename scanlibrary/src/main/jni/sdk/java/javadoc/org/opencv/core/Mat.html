<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:51 MSK 2015 -->
<title>Mat</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Mat";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":9,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":9,"i32":9,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":9,"i49":9,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":9,"i79":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/Mat.html" target="_top">Frames</a></li>
<li><a href="Mat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.core</div>
<h2 title="Class Mat" class="title">Class Mat</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.core.Mat</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>, <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>, <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>, <a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>, <a href="../../../org/opencv/core/MatOfFloat4.html" title="class in org.opencv.core">MatOfFloat4</a>, <a href="../../../org/opencv/core/MatOfFloat6.html" title="class in org.opencv.core">MatOfFloat6</a>, <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>, <a href="../../../org/opencv/core/MatOfInt4.html" title="class in org.opencv.core">MatOfInt4</a>, <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>, <a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core">MatOfPoint</a>, <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>, <a href="../../../org/opencv/core/MatOfPoint3.html" title="class in org.opencv.core">MatOfPoint3</a>, <a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>, <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Mat</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#nativeObj">nativeObj</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat--">Mat</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-int-int-int-">Mat</a></span>(int&nbsp;rows,
   int&nbsp;cols,
   int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-int-int-int-org.opencv.core.Scalar-">Mat</a></span>(int&nbsp;rows,
   int&nbsp;cols,
   int&nbsp;type,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-long-">Mat</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-org.opencv.core.Mat-org.opencv.core.Range-">Mat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
   <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-org.opencv.core.Mat-org.opencv.core.Range-org.opencv.core.Range-">Mat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
   <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
   <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-org.opencv.core.Mat-org.opencv.core.Rect-">Mat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
   <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-org.opencv.core.Size-int-">Mat</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
   int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#Mat-org.opencv.core.Size-int-org.opencv.core.Scalar-">Mat</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
   int&nbsp;type,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#adjustROI-int-int-int-int-">adjustROI</a></span>(int&nbsp;dtop,
         int&nbsp;dbottom,
         int&nbsp;dleft,
         int&nbsp;dright)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#assignTo-org.opencv.core.Mat-">assignTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#assignTo-org.opencv.core.Mat-int-">assignTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
        int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#channels--">channels</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#checkVector-int-">checkVector</a></span>(int&nbsp;elemChannels)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#checkVector-int-int-">checkVector</a></span>(int&nbsp;elemChannels,
           int&nbsp;depth)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#checkVector-int-int-boolean-">checkVector</a></span>(int&nbsp;elemChannels,
           int&nbsp;depth,
           boolean&nbsp;requireContinuous)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#clone--">clone</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#col-int-">col</a></span>(int&nbsp;x)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#colRange-int-int-">colRange</a></span>(int&nbsp;startcol,
        int&nbsp;endcol)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#colRange-org.opencv.core.Range-">colRange</a></span>(<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#cols--">cols</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#convertTo-org.opencv.core.Mat-int-">convertTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
         int&nbsp;rtype)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#convertTo-org.opencv.core.Mat-int-double-">convertTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
         int&nbsp;rtype,
         double&nbsp;alpha)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#convertTo-org.opencv.core.Mat-int-double-double-">convertTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
         int&nbsp;rtype,
         double&nbsp;alpha,
         double&nbsp;beta)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#copyTo-org.opencv.core.Mat-">copyTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#copyTo-org.opencv.core.Mat-org.opencv.core.Mat-">copyTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#create-int-int-int-">create</a></span>(int&nbsp;rows,
      int&nbsp;cols,
      int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#create-org.opencv.core.Size-int-">create</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
      int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#cross-org.opencv.core.Mat-">cross</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#dataAddr--">dataAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#depth--">depth</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#diag--">diag</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#diag-int-">diag</a></span>(int&nbsp;d)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#diag-org.opencv.core.Mat-">diag</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;d)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#dims--">dims</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#dot-org.opencv.core.Mat-">dot</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#dump--">dump</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#elemSize--">elemSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#elemSize1--">elemSize1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#empty--">empty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#eye-int-int-int-">eye</a></span>(int&nbsp;rows,
   int&nbsp;cols,
   int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#eye-org.opencv.core.Size-int-">eye</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
   int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>double[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#get-int-int-">get</a></span>(int&nbsp;row,
   int&nbsp;col)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#get-int-int-byte:A-">get</a></span>(int&nbsp;row,
   int&nbsp;col,
   byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#get-int-int-double:A-">get</a></span>(int&nbsp;row,
   int&nbsp;col,
   double[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#get-int-int-float:A-">get</a></span>(int&nbsp;row,
   int&nbsp;col,
   float[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#get-int-int-int:A-">get</a></span>(int&nbsp;row,
   int&nbsp;col,
   int[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#get-int-int-short:A-">get</a></span>(int&nbsp;row,
   int&nbsp;col,
   short[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#height--">height</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#inv--">inv</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#inv-int-">inv</a></span>(int&nbsp;method)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#isContinuous--">isContinuous</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#isSubmatrix--">isSubmatrix</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#locateROI-org.opencv.core.Size-org.opencv.core.Point-">locateROI</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;wholeSize,
         <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;ofs)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#mul-org.opencv.core.Mat-">mul</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#mul-org.opencv.core.Mat-double-">mul</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
   double&nbsp;scale)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#ones-int-int-int-">ones</a></span>(int&nbsp;rows,
    int&nbsp;cols,
    int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#ones-org.opencv.core.Size-int-">ones</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
    int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#push_back-org.opencv.core.Mat-">push_back</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#put-int-int-byte:A-">put</a></span>(int&nbsp;row,
   int&nbsp;col,
   byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#put-int-int-double...-">put</a></span>(int&nbsp;row,
   int&nbsp;col,
   double...&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#put-int-int-float:A-">put</a></span>(int&nbsp;row,
   int&nbsp;col,
   float[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#put-int-int-int:A-">put</a></span>(int&nbsp;row,
   int&nbsp;col,
   int[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#put-int-int-short:A-">put</a></span>(int&nbsp;row,
   int&nbsp;col,
   short[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#release--">release</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#reshape-int-">reshape</a></span>(int&nbsp;cn)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#reshape-int-int-">reshape</a></span>(int&nbsp;cn,
       int&nbsp;rows)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#row-int-">row</a></span>(int&nbsp;y)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#rowRange-int-int-">rowRange</a></span>(int&nbsp;startrow,
        int&nbsp;endrow)</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#rowRange-org.opencv.core.Range-">rowRange</a></span>(<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#rows--">rows</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Mat-">setTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Mat-org.opencv.core.Mat-">setTo</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Scalar-">setTo</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#setTo-org.opencv.core.Scalar-org.opencv.core.Mat-">setTo</a></span>(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;value,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#size--">size</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#step1--">step1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#step1-int-">step1</a></span>(int&nbsp;i)</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#submat-int-int-int-int-">submat</a></span>(int&nbsp;rowStart,
      int&nbsp;rowEnd,
      int&nbsp;colStart,
      int&nbsp;colEnd)</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#submat-org.opencv.core.Range-org.opencv.core.Range-">submat</a></span>(<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
      <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</code>&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#submat-org.opencv.core.Rect-">submat</a></span>(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#t--">t</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#total--">total</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#type--">type</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#width--">width</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#zeros-int-int-int-">zeros</a></span>(int&nbsp;rows,
     int&nbsp;cols,
     int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Mat.html#zeros-org.opencv.core.Size-int-">zeros</a></span>(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
     int&nbsp;type)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="nativeObj">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>nativeObj</h4>
<pre>public final&nbsp;long nativeObj</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Mat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat()</pre>
</li>
</ul>
<a name="Mat-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(int&nbsp;rows,
           int&nbsp;cols,
           int&nbsp;type)</pre>
</li>
</ul>
<a name="Mat-int-int-int-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(int&nbsp;rows,
           int&nbsp;cols,
           int&nbsp;type,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</pre>
</li>
</ul>
<a name="Mat-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(long&nbsp;addr)</pre>
</li>
</ul>
<a name="Mat-org.opencv.core.Mat-org.opencv.core.Range-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
           <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange)</pre>
</li>
</ul>
<a name="Mat-org.opencv.core.Mat-org.opencv.core.Range-org.opencv.core.Range-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
           <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
           <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</pre>
</li>
</ul>
<a name="Mat-org.opencv.core.Mat-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
           <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</pre>
</li>
</ul>
<a name="Mat-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
           int&nbsp;type)</pre>
</li>
</ul>
<a name="Mat-org.opencv.core.Size-int-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Mat</h4>
<pre>public&nbsp;Mat(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
           int&nbsp;type,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="adjustROI-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>adjustROI</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;adjustROI(int&nbsp;dtop,
                     int&nbsp;dbottom,
                     int&nbsp;dleft,
                     int&nbsp;dright)</pre>
</li>
</ul>
<a name="assignTo-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignTo</h4>
<pre>public&nbsp;void&nbsp;assignTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="assignTo-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignTo</h4>
<pre>public&nbsp;void&nbsp;assignTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                     int&nbsp;type)</pre>
</li>
</ul>
<a name="channels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>channels</h4>
<pre>public&nbsp;int&nbsp;channels()</pre>
</li>
</ul>
<a name="checkVector-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkVector</h4>
<pre>public&nbsp;int&nbsp;checkVector(int&nbsp;elemChannels)</pre>
</li>
</ul>
<a name="checkVector-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkVector</h4>
<pre>public&nbsp;int&nbsp;checkVector(int&nbsp;elemChannels,
                       int&nbsp;depth)</pre>
</li>
</ul>
<a name="checkVector-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkVector</h4>
<pre>public&nbsp;int&nbsp;checkVector(int&nbsp;elemChannels,
                       int&nbsp;depth,
                       boolean&nbsp;requireContinuous)</pre>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;clone()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>clone</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="col-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>col</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;col(int&nbsp;x)</pre>
</li>
</ul>
<a name="colRange-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colRange</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;colRange(int&nbsp;startcol,
                    int&nbsp;endcol)</pre>
</li>
</ul>
<a name="colRange-org.opencv.core.Range-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colRange</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;colRange(<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</pre>
</li>
</ul>
<a name="cols--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cols</h4>
<pre>public&nbsp;int&nbsp;cols()</pre>
</li>
</ul>
<a name="convertTo-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertTo</h4>
<pre>public&nbsp;void&nbsp;convertTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                      int&nbsp;rtype)</pre>
</li>
</ul>
<a name="convertTo-org.opencv.core.Mat-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertTo</h4>
<pre>public&nbsp;void&nbsp;convertTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                      int&nbsp;rtype,
                      double&nbsp;alpha)</pre>
</li>
</ul>
<a name="convertTo-org.opencv.core.Mat-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertTo</h4>
<pre>public&nbsp;void&nbsp;convertTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                      int&nbsp;rtype,
                      double&nbsp;alpha,
                      double&nbsp;beta)</pre>
</li>
</ul>
<a name="copyTo-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public&nbsp;void&nbsp;copyTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="copyTo-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyTo</h4>
<pre>public&nbsp;void&nbsp;copyTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="create-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public&nbsp;void&nbsp;create(int&nbsp;rows,
                   int&nbsp;cols,
                   int&nbsp;type)</pre>
</li>
</ul>
<a name="create-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public&nbsp;void&nbsp;create(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                   int&nbsp;type)</pre>
</li>
</ul>
<a name="cross-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cross</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cross(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="dataAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dataAddr</h4>
<pre>public&nbsp;long&nbsp;dataAddr()</pre>
</li>
</ul>
<a name="depth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>depth</h4>
<pre>public&nbsp;int&nbsp;depth()</pre>
</li>
</ul>
<a name="diag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>diag</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diag()</pre>
</li>
</ul>
<a name="diag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>diag</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diag(int&nbsp;d)</pre>
</li>
</ul>
<a name="diag-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>diag</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;diag(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;d)</pre>
</li>
</ul>
<a name="dims--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dims</h4>
<pre>public&nbsp;int&nbsp;dims()</pre>
</li>
</ul>
<a name="dot-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dot</h4>
<pre>public&nbsp;double&nbsp;dot(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="dump--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dump</h4>
<pre>public&nbsp;java.lang.String&nbsp;dump()</pre>
</li>
</ul>
<a name="elemSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>elemSize</h4>
<pre>public&nbsp;long&nbsp;elemSize()</pre>
</li>
</ul>
<a name="elemSize1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>elemSize1</h4>
<pre>public&nbsp;long&nbsp;elemSize1()</pre>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
</li>
</ul>
<a name="eye-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eye</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eye(int&nbsp;rows,
                      int&nbsp;cols,
                      int&nbsp;type)</pre>
</li>
</ul>
<a name="eye-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eye</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eye(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                      int&nbsp;type)</pre>
</li>
</ul>
<a name="get-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;double[]&nbsp;get(int&nbsp;row,
                    int&nbsp;col)</pre>
</li>
</ul>
<a name="get-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;int&nbsp;get(int&nbsp;row,
               int&nbsp;col,
               byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="get-int-int-double:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;int&nbsp;get(int&nbsp;row,
               int&nbsp;col,
               double[]&nbsp;data)</pre>
</li>
</ul>
<a name="get-int-int-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;int&nbsp;get(int&nbsp;row,
               int&nbsp;col,
               float[]&nbsp;data)</pre>
</li>
</ul>
<a name="get-int-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;int&nbsp;get(int&nbsp;row,
               int&nbsp;col,
               int[]&nbsp;data)</pre>
</li>
</ul>
<a name="get-int-int-short:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;int&nbsp;get(int&nbsp;row,
               int&nbsp;col,
               short[]&nbsp;data)</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="height--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>height</h4>
<pre>public&nbsp;int&nbsp;height()</pre>
</li>
</ul>
<a name="inv--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inv</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inv()</pre>
</li>
</ul>
<a name="inv-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inv</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inv(int&nbsp;method)</pre>
</li>
</ul>
<a name="isContinuous--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isContinuous</h4>
<pre>public&nbsp;boolean&nbsp;isContinuous()</pre>
</li>
</ul>
<a name="isSubmatrix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSubmatrix</h4>
<pre>public&nbsp;boolean&nbsp;isSubmatrix()</pre>
</li>
</ul>
<a name="locateROI-org.opencv.core.Size-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>locateROI</h4>
<pre>public&nbsp;void&nbsp;locateROI(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;wholeSize,
                      <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;ofs)</pre>
</li>
</ul>
<a name="mul-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mul</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mul(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="mul-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mul</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mul(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
               double&nbsp;scale)</pre>
</li>
</ul>
<a name="ones-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ones</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ones(int&nbsp;rows,
                       int&nbsp;cols,
                       int&nbsp;type)</pre>
</li>
</ul>
<a name="ones-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ones</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;ones(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                       int&nbsp;type)</pre>
</li>
</ul>
<a name="push_back-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>push_back</h4>
<pre>public&nbsp;void&nbsp;push_back(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="put-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;int&nbsp;put(int&nbsp;row,
               int&nbsp;col,
               byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="put-int-int-double...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;int&nbsp;put(int&nbsp;row,
               int&nbsp;col,
               double...&nbsp;data)</pre>
</li>
</ul>
<a name="put-int-int-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;int&nbsp;put(int&nbsp;row,
               int&nbsp;col,
               float[]&nbsp;data)</pre>
</li>
</ul>
<a name="put-int-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;int&nbsp;put(int&nbsp;row,
               int&nbsp;col,
               int[]&nbsp;data)</pre>
</li>
</ul>
<a name="put-int-int-short:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;int&nbsp;put(int&nbsp;row,
               int&nbsp;col,
               short[]&nbsp;data)</pre>
</li>
</ul>
<a name="release--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>release</h4>
<pre>public&nbsp;void&nbsp;release()</pre>
</li>
</ul>
<a name="reshape-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reshape</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;reshape(int&nbsp;cn)</pre>
</li>
</ul>
<a name="reshape-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reshape</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;reshape(int&nbsp;cn,
                   int&nbsp;rows)</pre>
</li>
</ul>
<a name="row-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>row</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;row(int&nbsp;y)</pre>
</li>
</ul>
<a name="rowRange-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rowRange</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rowRange(int&nbsp;startrow,
                    int&nbsp;endrow)</pre>
</li>
</ul>
<a name="rowRange-org.opencv.core.Range-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rowRange</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rowRange(<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;r)</pre>
</li>
</ul>
<a name="rows--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rows</h4>
<pre>public&nbsp;int&nbsp;rows()</pre>
</li>
</ul>
<a name="setTo-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTo</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;setTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value)</pre>
</li>
</ul>
<a name="setTo-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTo</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;setTo(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;value,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="setTo-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTo</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;setTo(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</pre>
</li>
</ul>
<a name="setTo-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTo</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;setTo(<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;value,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size()</pre>
</li>
</ul>
<a name="step1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>step1</h4>
<pre>public&nbsp;long&nbsp;step1()</pre>
</li>
</ul>
<a name="step1-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>step1</h4>
<pre>public&nbsp;long&nbsp;step1(int&nbsp;i)</pre>
</li>
</ul>
<a name="submat-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>submat</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;submat(int&nbsp;rowStart,
                  int&nbsp;rowEnd,
                  int&nbsp;colStart,
                  int&nbsp;colEnd)</pre>
</li>
</ul>
<a name="submat-org.opencv.core.Range-org.opencv.core.Range-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>submat</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;submat(<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;rowRange,
                  <a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core">Range</a>&nbsp;colRange)</pre>
</li>
</ul>
<a name="submat-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>submat</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;submat(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi)</pre>
</li>
</ul>
<a name="t--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>t</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t()</pre>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="total--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>total</h4>
<pre>public&nbsp;long&nbsp;total()</pre>
</li>
</ul>
<a name="type--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>public&nbsp;int&nbsp;type()</pre>
</li>
</ul>
<a name="width--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>width</h4>
<pre>public&nbsp;int&nbsp;width()</pre>
</li>
</ul>
<a name="zeros-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zeros</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;zeros(int&nbsp;rows,
                        int&nbsp;cols,
                        int&nbsp;type)</pre>
</li>
</ul>
<a name="zeros-org.opencv.core.Size-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>zeros</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;zeros(<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                        int&nbsp;type)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/Mat.html" target="_top">Frames</a></li>
<li><a href="Mat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
