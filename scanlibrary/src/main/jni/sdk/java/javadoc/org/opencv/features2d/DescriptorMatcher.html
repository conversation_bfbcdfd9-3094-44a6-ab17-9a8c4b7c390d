<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:51 MSK 2015 -->
<title>DescriptorMatcher</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DescriptorMatcher";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/DescriptorExtractor.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/FeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/DescriptorMatcher.html" target="_top">Frames</a></li>
<li><a href="DescriptorMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class DescriptorMatcher" class="title">Class DescriptorMatcher</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.DescriptorMatcher</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DescriptorMatcher</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE">BRUTEFORCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMING">BRUTEFORCE_HAMMING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_HAMMINGLUT">BRUTEFORCE_HAMMINGLUT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_L1">BRUTEFORCE_L1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#BRUTEFORCE_SL2">BRUTEFORCE_SL2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#FLANNBASED">FLANNBASED</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#add-java.util.List-">add</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#clear--">clear</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#clone--">clone</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#clone-boolean-">clone</a></span>(boolean&nbsp;emptyTrainData)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#create-int-">create</a></span>(int&nbsp;matcherType)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#empty--">empty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#getTrainDescriptors--">getTrainDescriptors</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#isMaskSupported--">isMaskSupported</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-boolean-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k,
        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
        boolean&nbsp;compactResult)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-boolean-">knnMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
        int&nbsp;k,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
        boolean&nbsp;compactResult)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-java.util.List-">match</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
     <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-boolean-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
           boolean&nbsp;compactResult)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-boolean-">radiusMatch</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
           java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
           float&nbsp;maxDistance,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
           boolean&nbsp;compactResult)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#read-java.lang.String-">read</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#train--">train</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/DescriptorMatcher.html#write-java.lang.String-">write</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BRUTEFORCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE</h4>
<pre>public static final&nbsp;int BRUTEFORCE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_HAMMING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_HAMMING</h4>
<pre>public static final&nbsp;int BRUTEFORCE_HAMMING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_HAMMINGLUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_HAMMINGLUT</h4>
<pre>public static final&nbsp;int BRUTEFORCE_HAMMINGLUT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_HAMMINGLUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_L1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_L1</h4>
<pre>public static final&nbsp;int BRUTEFORCE_L1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_L1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRUTEFORCE_SL2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRUTEFORCE_SL2</h4>
<pre>public static final&nbsp;int BRUTEFORCE_SL2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.BRUTEFORCE_SL2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLANNBASED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FLANNBASED</h4>
<pre>public static final&nbsp;int FLANNBASED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.DescriptorMatcher.FLANNBASED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="add-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;void&nbsp;add(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;descriptors)</pre>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;clone()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>clone</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="clone-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;clone(boolean&nbsp;emptyTrainData)</pre>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d">DescriptorMatcher</a>&nbsp;create(int&nbsp;matcherType)</pre>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
</li>
</ul>
<a name="getTrainDescriptors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainDescriptors</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;getTrainDescriptors()</pre>
</li>
</ul>
<a name="isMaskSupported--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMaskSupported</h4>
<pre>public&nbsp;boolean&nbsp;isMaskSupported()</pre>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k)</pre>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-java.util.List-int-java.util.List-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k,
                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
                     boolean&nbsp;compactResult)</pre>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k)</pre>
</li>
</ul>
<a name="knnMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-int-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>knnMatch</h4>
<pre>public&nbsp;void&nbsp;knnMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                     int&nbsp;k,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                     boolean&nbsp;compactResult)</pre>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</pre>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches)</pre>
</li>
</ul>
<a name="match-org.opencv.core.Mat-org.opencv.core.MatOfDMatch-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;void&nbsp;match(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                  <a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&nbsp;matches,
                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</pre>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-java.util.List-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance)</pre>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-java.util.List-float-java.util.List-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance,
                        java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks,
                        boolean&nbsp;compactResult)</pre>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance)</pre>
</li>
</ul>
<a name="radiusMatch-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-float-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>radiusMatch</h4>
<pre>public&nbsp;void&nbsp;radiusMatch(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;queryDescriptors,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;trainDescriptors,
                        java.util.List&lt;<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core">MatOfDMatch</a>&gt;&nbsp;matches,
                        float&nbsp;maxDistance,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                        boolean&nbsp;compactResult)</pre>
</li>
</ul>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;void&nbsp;read(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
<a name="train--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>train</h4>
<pre>public&nbsp;void&nbsp;train()</pre>
</li>
</ul>
<a name="write-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/DescriptorExtractor.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/FeatureDetector.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/DescriptorMatcher.html" target="_top">Frames</a></li>
<li><a href="DescriptorMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
