<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:50 MSK 2015 -->
<title>JavaCameraView</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="JavaCameraView";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/JavaCameraView.html" target="_top">Frames</a></li>
<li><a href="JavaCameraView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.android.CameraBridgeViewBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.android</div>
<h2 title="Class JavaCameraView" class="title">Class JavaCameraView</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>android.view.View</li>
<li>
<ul class="inheritance">
<li>android.view.SurfaceView</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">org.opencv.android.CameraBridgeViewBase</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.android.JavaCameraView</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>android.graphics.drawable.Drawable.Callback, android.hardware.Camera.PreviewCallback, android.view.accessibility.AccessibilityEventSource, android.view.KeyEvent.Callback, android.view.SurfaceHolder.Callback</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">JavaCameraView</span>
extends <a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a>
implements android.hardware.Camera.PreviewCallback</pre>
<div class="block">This class is an implementation of the Bridge View between OpenCV and Java Camera.
 This class relays on the functionality available in base class and only implements
 required functions:
 connectCamera - opens Java camera and sets the PreviewCallback to be delivered.
 disconnectCamera - closes the camera and stops preview.
 When frame is delivered via callback from Camera - it processed via OpenCV to be
 converted to RGBA32 and then passed to the external callback for modifications if required.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android">JavaCameraView.JavaCameraSizeAccessor</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.org.opencv.android.CameraBridgeViewBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a></h3>
<code><a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewFrame.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewFrame</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.CvCameraViewListener2.html" title="interface in org.opencv.android">CameraBridgeViewBase.CvCameraViewListener2</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.ListItemAccessor.html" title="interface in org.opencv.android">CameraBridgeViewBase.ListItemAccessor</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.android.view.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;android.view.View</h3>
<code>android.view.View.AccessibilityDelegate, android.view.View.BaseSavedState, android.view.View.DragShadowBuilder, android.view.View.MeasureSpec, android.view.View.OnAttachStateChangeListener, android.view.View.OnClickListener, android.view.View.OnCreateContextMenuListener, android.view.View.OnDragListener, android.view.View.OnFocusChangeListener, android.view.View.OnGenericMotionListener, android.view.View.OnHoverListener, android.view.View.OnKeyListener, android.view.View.OnLayoutChangeListener, android.view.View.OnLongClickListener, android.view.View.OnSystemUiVisibilityChangeListener, android.view.View.OnTouchListener</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.android.CameraBridgeViewBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a></h3>
<code><a href="../../../org/opencv/android/CameraBridgeViewBase.html#CAMERA_ID_ANY">CAMERA_ID_ANY</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#CAMERA_ID_BACK">CAMERA_ID_BACK</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#CAMERA_ID_FRONT">CAMERA_ID_FRONT</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#GRAY">GRAY</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#RGBA">RGBA</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.android.view.View">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;android.view.View</h3>
<code>ALPHA, DRAWING_CACHE_QUALITY_AUTO, DRAWING_CACHE_QUALITY_HIGH, DRAWING_CACHE_QUALITY_LOW, FIND_VIEWS_WITH_CONTENT_DESCRIPTION, FIND_VIEWS_WITH_TEXT, FOCUS_BACKWARD, FOCUS_DOWN, FOCUS_FORWARD, FOCUS_LEFT, FOCUS_RIGHT, FOCUS_UP, FOCUSABLES_ALL, FOCUSABLES_TOUCH_MODE, GONE, HAPTIC_FEEDBACK_ENABLED, INVISIBLE, KEEP_SCREEN_ON, LAYER_TYPE_HARDWARE, LAYER_TYPE_NONE, LAYER_TYPE_SOFTWARE, MEASURED_HEIGHT_STATE_SHIFT, MEASURED_SIZE_MASK, MEASURED_STATE_MASK, MEASURED_STATE_TOO_SMALL, NO_ID, OVER_SCROLL_ALWAYS, OVER_SCROLL_IF_CONTENT_SCROLLS, OVER_SCROLL_NEVER, ROTATION, ROTATION_X, ROTATION_Y, SCALE_X, SCALE_Y, SCROLLBAR_POSITION_DEFAULT, SCROLLBAR_POSITION_LEFT, SCROLLBAR_POSITION_RIGHT, SCROLLBARS_INSIDE_INSET, SCROLLBARS_INSIDE_OVERLAY, SCROLLBARS_OUTSIDE_INSET, SCROLLBARS_OUTSIDE_OVERLAY, SOUND_EFFECTS_ENABLED, STATUS_BAR_HIDDEN, STATUS_BAR_VISIBLE, SYSTEM_UI_FLAG_HIDE_NAVIGATION, SYSTEM_UI_FLAG_LOW_PROFILE, SYSTEM_UI_FLAG_VISIBLE, TRANSLATION_X, TRANSLATION_Y, VISIBLE, X, Y</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/android/JavaCameraView.html#JavaCameraView-android.content.Context-android.util.AttributeSet-">JavaCameraView</a></span>(android.content.Context&nbsp;context,
              android.util.AttributeSet&nbsp;attrs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/android/JavaCameraView.html#JavaCameraView-android.content.Context-int-">JavaCameraView</a></span>(android.content.Context&nbsp;context,
              int&nbsp;cameraId)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/JavaCameraView.html#onPreviewFrame-byte:A-android.hardware.Camera-">onPreviewFrame</a></span>(byte[]&nbsp;frame,
              android.hardware.Camera&nbsp;arg1)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.android.CameraBridgeViewBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.android.<a href="../../../org/opencv/android/CameraBridgeViewBase.html" title="class in org.opencv.android">CameraBridgeViewBase</a></h3>
<code><a href="../../../org/opencv/android/CameraBridgeViewBase.html#disableFpsMeter--">disableFpsMeter</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#disableView--">disableView</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#enableFpsMeter--">enableFpsMeter</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#enableView--">enableView</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#setCameraIndex-int-">setCameraIndex</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#SetCaptureFormat-int-">SetCaptureFormat</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#setCvCameraViewListener-org.opencv.android.CameraBridgeViewBase.CvCameraViewListener-">setCvCameraViewListener</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#setCvCameraViewListener-org.opencv.android.CameraBridgeViewBase.CvCameraViewListener2-">setCvCameraViewListener</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#setMaxFrameSize-int-int-">setMaxFrameSize</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#surfaceChanged-android.view.SurfaceHolder-int-int-int-">surfaceChanged</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#surfaceCreated-android.view.SurfaceHolder-">surfaceCreated</a>, <a href="../../../org/opencv/android/CameraBridgeViewBase.html#surfaceDestroyed-android.view.SurfaceHolder-">surfaceDestroyed</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.view.SurfaceView">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;android.view.SurfaceView</h3>
<code>draw, gatherTransparentRegion, getHolder, setVisibility, setZOrderMediaOverlay, setZOrderOnTop</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.android.view.View">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;android.view.View</h3>
<code>addFocusables, addFocusables, addOnAttachStateChangeListener, addOnLayoutChangeListener, addTouchables, animate, bringToFront, buildDrawingCache, buildDrawingCache, buildLayer, cancelLongPress, canScrollHorizontally, canScrollVertically, checkInputConnectionProxy, clearAnimation, clearFocus, combineMeasuredStates, computeScroll, createAccessibilityNodeInfo, createContextMenu, destroyDrawingCache, dispatchConfigurationChanged, dispatchDisplayHint, dispatchDragEvent, dispatchGenericMotionEvent, dispatchKeyEvent, dispatchKeyEventPreIme, dispatchKeyShortcutEvent, dispatchPopulateAccessibilityEvent, dispatchSystemUiVisibilityChanged, dispatchTouchEvent, dispatchTrackballEvent, dispatchUnhandledMove, dispatchWindowFocusChanged, dispatchWindowVisibilityChanged, findFocus, findViewById, findViewsWithText, findViewWithTag, fitsSystemWindows, focusSearch, forceLayout, getAlpha, getAnimation, getApplicationWindowToken, getBackground, getBaseline, getBottom, getContentDescription, getContext, getDefaultSize, getDrawableState, getDrawingCache, getDrawingCache, getDrawingCacheBackgroundColor, getDrawingCacheQuality, getDrawingRect, getDrawingTime, getFilterTouchesWhenObscured, getFocusables, getFocusedRect, getGlobalVisibleRect, getGlobalVisibleRect, getHandler, getHeight, getHitRect, getHorizontalFadingEdgeLength, getId, getKeepScreenOn, getKeyDispatcherState, getLayerType, getLayoutParams, getLeft, getLocalVisibleRect, getLocationInWindow, getLocationOnScreen, getMatrix, getMeasuredHeight, getMeasuredHeightAndState, getMeasuredState, getMeasuredWidth, getMeasuredWidthAndState, getNextFocusDownId, getNextFocusForwardId, getNextFocusLeftId, getNextFocusRightId, getNextFocusUpId, getOnFocusChangeListener, getOverScrollMode, getPaddingBottom, getPaddingLeft, getPaddingRight, getPaddingTop, getParent, getPivotX, getPivotY, getResources, getRight, getRootView, getRotation, getRotationX, getRotationY, getScaleX, getScaleY, getScrollBarStyle, getScrollX, getScrollY, getSolidColor, getSystemUiVisibility, getTag, getTag, getTop, getTouchables, getTouchDelegate, getTranslationX, getTranslationY, getVerticalFadingEdgeLength, getVerticalScrollbarPosition, getVerticalScrollbarWidth, getViewTreeObserver, getVisibility, getWidth, getWindowToken, getWindowVisibility, getWindowVisibleDisplayFrame, getX, getY, hasFocus, hasFocusable, hasWindowFocus, inflate, invalidate, invalidate, invalidate, invalidateDrawable, isActivated, isClickable, isDirty, isDrawingCacheEnabled, isDuplicateParentStateEnabled, isEnabled, isFocusable, isFocusableInTouchMode, isFocused, isHapticFeedbackEnabled, isHardwareAccelerated, isHorizontalFadingEdgeEnabled, isHorizontalScrollBarEnabled, isHovered, isInEditMode, isInTouchMode, isLayoutRequested, isLongClickable, isOpaque, isPressed, isSaveEnabled, isSaveFromParentEnabled, isScrollbarFadingEnabled, isSelected, isShown, isSoundEffectsEnabled, isVerticalFadingEdgeEnabled, isVerticalScrollBarEnabled, jumpDrawablesToCurrentState, layout, measure, offsetLeftAndRight, offsetTopAndBottom, onCheckIsTextEditor, onCreateInputConnection, onDragEvent, onFilterTouchEventForSecurity, onFinishTemporaryDetach, onGenericMotionEvent, onHoverChanged, onHoverEvent, onInitializeAccessibilityEvent, onInitializeAccessibilityNodeInfo, onKeyDown, onKeyLongPress, onKeyMultiple, onKeyPreIme, onKeyShortcut, onKeyUp, onPopulateAccessibilityEvent, onStartTemporaryDetach, onTouchEvent, onTrackballEvent, onWindowFocusChanged, performClick, performHapticFeedback, performHapticFeedback, performLongClick, playSoundEffect, post, postDelayed, postInvalidate, postInvalidate, postInvalidateDelayed, postInvalidateDelayed, refreshDrawableState, removeCallbacks, removeOnAttachStateChangeListener, removeOnLayoutChangeListener, requestFocus, requestFocus, requestFocus, requestFocusFromTouch, requestLayout, requestRectangleOnScreen, requestRectangleOnScreen, resolveSize, resolveSizeAndState, restoreHierarchyState, saveHierarchyState, scheduleDrawable, scrollBy, scrollTo, sendAccessibilityEvent, sendAccessibilityEventUnchecked, setAccessibilityDelegate, setActivated, setAlpha, setAnimation, setBackgroundColor, setBackgroundDrawable, setBackgroundResource, setBottom, setCameraDistance, setClickable, setContentDescription, setDrawingCacheBackgroundColor, setDrawingCacheEnabled, setDrawingCacheQuality, setDuplicateParentStateEnabled, setEnabled, setFadingEdgeLength, setFilterTouchesWhenObscured, setFitsSystemWindows, setFocusable, setFocusableInTouchMode, setHapticFeedbackEnabled, setHorizontalFadingEdgeEnabled, setHorizontalScrollBarEnabled, setHovered, setId, setKeepScreenOn, setLayerType, setLayoutParams, setLeft, setLongClickable, setMinimumHeight, setMinimumWidth, setNextFocusDownId, setNextFocusForwardId, setNextFocusLeftId, setNextFocusRightId, setNextFocusUpId, setOnClickListener, setOnCreateContextMenuListener, setOnDragListener, setOnFocusChangeListener, setOnGenericMotionListener, setOnHoverListener, setOnKeyListener, setOnLongClickListener, setOnSystemUiVisibilityChangeListener, setOnTouchListener, setOverScrollMode, setPadding, setPivotX, setPivotY, setPressed, setRight, setRotation, setRotationX, setRotationY, setSaveEnabled, setSaveFromParentEnabled, setScaleX, setScaleY, setScrollbarFadingEnabled, setScrollBarStyle, setScrollContainer, setScrollX, setScrollY, setSelected, setSoundEffectsEnabled, setSystemUiVisibility, setTag, setTag, setTop, setTouchDelegate, setTranslationX, setTranslationY, setVerticalFadingEdgeEnabled, setVerticalScrollBarEnabled, setVerticalScrollbarPosition, setWillNotCacheDrawing, setWillNotDraw, setX, setY, showContextMenu, startActionMode, startAnimation, startDrag, unscheduleDrawable, unscheduleDrawable, willNotCacheDrawing, willNotDraw</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="JavaCameraView-android.content.Context-android.util.AttributeSet-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JavaCameraView</h4>
<pre>public&nbsp;JavaCameraView(android.content.Context&nbsp;context,
                      android.util.AttributeSet&nbsp;attrs)</pre>
</li>
</ul>
<a name="JavaCameraView-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>JavaCameraView</h4>
<pre>public&nbsp;JavaCameraView(android.content.Context&nbsp;context,
                      int&nbsp;cameraId)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onPreviewFrame-byte:A-android.hardware.Camera-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onPreviewFrame</h4>
<pre>public&nbsp;void&nbsp;onPreviewFrame(byte[]&nbsp;frame,
                           android.hardware.Camera&nbsp;arg1)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>onPreviewFrame</code>&nbsp;in interface&nbsp;<code>android.hardware.Camera.PreviewCallback</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/JavaCameraView.html" target="_top">Frames</a></li>
<li><a href="JavaCameraView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.opencv.android.CameraBridgeViewBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
