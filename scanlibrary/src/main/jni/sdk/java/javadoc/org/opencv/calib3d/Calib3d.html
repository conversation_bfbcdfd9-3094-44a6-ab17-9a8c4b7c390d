<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:51 MSK 2015 -->
<title>Calib3d</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Calib3d";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9,"i48":9,"i49":9,"i50":9,"i51":9,"i52":9,"i53":9,"i54":9,"i55":9,"i56":9,"i57":9,"i58":9,"i59":9,"i60":9,"i61":9,"i62":9,"i63":9,"i64":9,"i65":9,"i66":9,"i67":9,"i68":9,"i69":9,"i70":9,"i71":9,"i72":9,"i73":9,"i74":9,"i75":9,"i76":9,"i77":9,"i78":9,"i79":9,"i80":9,"i81":9,"i82":9,"i83":9,"i84":9,"i85":9,"i86":9,"i87":9,"i88":9,"i89":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/Calib3d.html" target="_top">Frames</a></li>
<li><a href="Calib3d.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.calib3d</div>
<h2 title="Class Calib3d" class="title">Class Calib3d</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.calib3d.Calib3d</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Calib3d</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_ADAPTIVE_THRESH">CALIB_CB_ADAPTIVE_THRESH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_ASYMMETRIC_GRID">CALIB_CB_ASYMMETRIC_GRID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_CLUSTERING">CALIB_CB_CLUSTERING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_FAST_CHECK">CALIB_CB_FAST_CHECK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_FILTER_QUADS">CALIB_CB_FILTER_QUADS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_NORMALIZE_IMAGE">CALIB_CB_NORMALIZE_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CB_SYMMETRIC_GRID">CALIB_CB_SYMMETRIC_GRID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_CHECK_COND">CALIB_CHECK_COND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_ASPECT_RATIO">CALIB_FIX_ASPECT_RATIO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_FOCAL_LENGTH">CALIB_FIX_FOCAL_LENGTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_INTRINSIC">CALIB_FIX_INTRINSIC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_K1">CALIB_FIX_K1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_K2">CALIB_FIX_K2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_K3">CALIB_FIX_K3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_K4">CALIB_FIX_K4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_K5">CALIB_FIX_K5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_K6">CALIB_FIX_K6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_PRINCIPAL_POINT">CALIB_FIX_PRINCIPAL_POINT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_S1_S2_S3_S4">CALIB_FIX_S1_S2_S3_S4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_SKEW">CALIB_FIX_SKEW</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_FIX_TAUX_TAUY">CALIB_FIX_TAUX_TAUY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_RATIONAL_MODEL">CALIB_RATIONAL_MODEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_RECOMPUTE_EXTRINSIC">CALIB_RECOMPUTE_EXTRINSIC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_SAME_FOCAL_LENGTH">CALIB_SAME_FOCAL_LENGTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_THIN_PRISM_MODEL">CALIB_THIN_PRISM_MODEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_TILTED_MODEL">CALIB_TILTED_MODEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_USE_INTRINSIC_GUESS">CALIB_USE_INTRINSIC_GUESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_USE_LU">CALIB_USE_LU</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_ZERO_DISPARITY">CALIB_ZERO_DISPARITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CALIB_ZERO_TANGENT_DIST">CALIB_ZERO_TANGENT_DIST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CV_DLS">CV_DLS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CV_EPNP">CV_EPNP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CV_ITERATIVE">CV_ITERATIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#CV_P3P">CV_P3P</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#FM_7POINT">FM_7POINT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#FM_8POINT">FM_8POINT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#FM_LMEDS">FM_LMEDS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#FM_RANSAC">FM_RANSAC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#LMEDS">LMEDS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#RANSAC">RANSAC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#RHO">RHO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#SOLVEPNP_DLS">SOLVEPNP_DLS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#SOLVEPNP_EPNP">SOLVEPNP_EPNP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#SOLVEPNP_ITERATIVE">SOLVEPNP_ITERATIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#SOLVEPNP_P3P">SOLVEPNP_P3P</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#SOLVEPNP_UPNP">SOLVEPNP_UPNP</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#Calib3d--">Calib3d</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrate-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-">calibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrate-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-">calibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
         int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrate-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-org.opencv.core.TermCriteria-">calibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
         int&nbsp;flags,
         <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrateCamera-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-">calibrateCamera</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrateCamera-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-">calibrateCamera</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
               int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrateCamera-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-org.opencv.core.TermCriteria-">calibrateCamera</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
               int&nbsp;flags,
               <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#calibrationMatrixValues-org.opencv.core.Mat-org.opencv.core.Size-double-double-double:A-double:A-double:A-org.opencv.core.Point-double:A-">calibrationMatrixValues</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                       double&nbsp;apertureWidth,
                       double&nbsp;apertureHeight,
                       double[]&nbsp;fovx,
                       double[]&nbsp;fovy,
                       double[]&nbsp;focalLength,
                       <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;principalPoint,
                       double[]&nbsp;aspectRatio)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#composeRT-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">composeRT</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec3,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec3)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#composeRT-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">composeRT</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec3,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec3,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dr1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dt1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dr2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dt2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dr1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dt1,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dr2,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dt2)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#computeCorrespondEpilines-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-">computeCorrespondEpilines</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                         int&nbsp;whichImage,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#convertPointsFromHomogeneous-org.opencv.core.Mat-org.opencv.core.Mat-">convertPointsFromHomogeneous</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#convertPointsToHomogeneous-org.opencv.core.Mat-org.opencv.core.Mat-">convertPointsToHomogeneous</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#correctMatches-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">correctMatches</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;newPoints1,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;newPoints2)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#decomposeEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decomposeEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#decomposeHomographyMat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-java.util.List-">decomposeHomographyMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rotations,
                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;translations,
                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;normals)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#decomposeProjectionMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decomposeProjectionMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;transVect)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#decomposeProjectionMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">decomposeProjectionMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;transVect,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrixX,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrixY,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrixZ,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eulerAngles)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#distortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">distortPoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#distortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">distortPoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
             double&nbsp;alpha)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#drawChessboardCorners-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.MatOfPoint2f-boolean-">drawChessboardCorners</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;corners,
                     boolean&nbsp;patternWasFound)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#estimateAffine3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">estimateAffine3D</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;out,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inliers)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#estimateAffine3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-double-">estimateAffine3D</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;out,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inliers,
                double&nbsp;ransacThreshold,
                double&nbsp;confidence)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#estimateNewCameraMatrixForUndistortRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-">estimateNewCameraMatrixForUndistortRectify</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#estimateNewCameraMatrixForUndistortRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Size-double-">estimateNewCameraMatrixForUndistortRectify</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P,
                                          double&nbsp;balance,
                                          <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;new_size,
                                          double&nbsp;fov_scale)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#filterSpeckles-org.opencv.core.Mat-double-int-double-">filterSpeckles</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
              double&nbsp;newVal,
              int&nbsp;maxSpeckleSize,
              double&nbsp;maxDiff)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#filterSpeckles-org.opencv.core.Mat-double-int-double-org.opencv.core.Mat-">filterSpeckles</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
              double&nbsp;newVal,
              int&nbsp;maxSpeckleSize,
              double&nbsp;maxDiff,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findChessboardCorners-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.MatOfPoint2f-">findChessboardCorners</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;corners)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findChessboardCorners-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.MatOfPoint2f-int-">findChessboardCorners</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;corners,
                     int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findCirclesGrid-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-">findCirclesGrid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;centers)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findCirclesGrid-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-int-">findCirclesGrid</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;centers,
               int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-">findEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-int-double-double-">findEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                double&nbsp;focal,
                <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp,
                int&nbsp;method,
                double&nbsp;prob,
                double&nbsp;threshold)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-int-double-double-org.opencv.core.Mat-">findEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                double&nbsp;focal,
                <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp,
                int&nbsp;method,
                double&nbsp;prob,
                double&nbsp;threshold,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">findEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-double-double-">findEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                int&nbsp;method,
                double&nbsp;prob,
                double&nbsp;threshold)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-double-double-org.opencv.core.Mat-">findEssentialMat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                int&nbsp;method,
                double&nbsp;prob,
                double&nbsp;threshold,
                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findFundamentalMat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-">findFundamentalMat</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points1,
                  <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points2)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findFundamentalMat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-double-">findFundamentalMat</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points1,
                  <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points2,
                  int&nbsp;method,
                  double&nbsp;param1,
                  double&nbsp;param2)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findFundamentalMat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-double-org.opencv.core.Mat-">findFundamentalMat</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points1,
                  <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points2,
                  int&nbsp;method,
                  double&nbsp;param1,
                  double&nbsp;param2,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findHomography-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-">findHomography</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;srcPoints,
              <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;dstPoints)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findHomography-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-">findHomography</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;srcPoints,
              <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;dstPoints,
              int&nbsp;method,
              double&nbsp;ransacReprojThreshold)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#findHomography-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-org.opencv.core.Mat-int-double-">findHomography</a></span>(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;srcPoints,
              <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;dstPoints,
              int&nbsp;method,
              double&nbsp;ransacReprojThreshold,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
              int&nbsp;maxIters,
              double&nbsp;confidence)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#getOptimalNewCameraMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-double-">getOptimalNewCameraMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                         double&nbsp;alpha)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#getOptimalNewCameraMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-double-org.opencv.core.Size-org.opencv.core.Rect-boolean-">getOptimalNewCameraMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                         double&nbsp;alpha,
                         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImgSize,
                         <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;validPixROI,
                         boolean&nbsp;centerPrincipalPoint)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#getValidDisparityROI-org.opencv.core.Rect-org.opencv.core.Rect-int-int-int-">getValidDisparityROI</a></span>(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1,
                    <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2,
                    int&nbsp;minDisparity,
                    int&nbsp;numberOfDisparities,
                    int&nbsp;SADWindowSize)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#initCameraMatrix2D-java.util.List-java.util.List-org.opencv.core.Size-">initCameraMatrix2D</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;objectPoints,
                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;imagePoints,
                  <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#initCameraMatrix2D-java.util.List-java.util.List-org.opencv.core.Size-double-">initCameraMatrix2D</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;objectPoints,
                  java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;imagePoints,
                  <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                  double&nbsp;aspectRatio)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#initUndistortRectifyMap-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-int-org.opencv.core.Mat-org.opencv.core.Mat-">initUndistortRectifyMap</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P,
                       <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                       int&nbsp;m1type,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;map1,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;map2)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#matMulDeriv-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">matMulDeriv</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;A,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;B,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dABdA,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dABdB)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfPoint2f-">projectPoints</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
             <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-double-">projectPoints</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
             <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
             <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;jacobian,
             double&nbsp;aspectRatio)</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">projectPoints</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
             <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Mat-">projectPoints</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
             <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
             double&nbsp;alpha,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;jacobian)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">recoverPose</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-">recoverPose</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t,
           double&nbsp;focal,
           <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp)</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-org.opencv.core.Mat-">recoverPose</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t,
           double&nbsp;focal,
           <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">recoverPose</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">recoverPose</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#rectify3Collinear-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Rect-org.opencv.core.Rect-int-">rectify3Collinear</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix3,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs3,
                 java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgpt1,
                 java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgpt3,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R12,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T12,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R13,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T13,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R3,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P3,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                 double&nbsp;alpha,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImgSize,
                 <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1,
                 <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2,
                 int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#reprojectImageTo3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">reprojectImageTo3D</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_3dImage,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#reprojectImageTo3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">reprojectImageTo3D</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_3dImage,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                  boolean&nbsp;handleMissingValues)</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#reprojectImageTo3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-int-">reprojectImageTo3D</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_3dImage,
                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                  boolean&nbsp;handleMissingValues,
                  int&nbsp;ddepth)</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#Rodrigues-org.opencv.core.Mat-org.opencv.core.Mat-">Rodrigues</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#Rodrigues-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">Rodrigues</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;jacobian)</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>static double[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#RQDecomp3x3-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">RQDecomp3x3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxR,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxQ)</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>static double[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#RQDecomp3x3-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">RQDecomp3x3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxR,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxQ,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Qx,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Qy,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Qz)</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#sampsonDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">sampsonDistance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;pt1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;pt2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F)</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#solvePnP-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-">solvePnP</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
        <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec)</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#solvePnP-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-boolean-int-">solvePnP</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
        <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
        <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
        boolean&nbsp;useExtrinsicGuess,
        int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#solvePnPRansac-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-">solvePnPRansac</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
              <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec)</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#solvePnPRansac-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-boolean-int-float-double-org.opencv.core.Mat-int-">solvePnPRansac</a></span>(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
              <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
              boolean&nbsp;useExtrinsicGuess,
              int&nbsp;iterationsCount,
              float&nbsp;reprojectionError,
              double&nbsp;confidence,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inliers,
              int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-">stereoCalibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T)</code>&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-int-">stereoCalibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
               int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-">stereoCalibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
               int&nbsp;flags,
               <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">stereoCalibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F)</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">stereoCalibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
               int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-">stereoCalibrate</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
               int&nbsp;flags,
               <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">stereoRectify</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q)</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">stereoRectify</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
             int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-double-org.opencv.core.Size-org.opencv.core.Rect-org.opencv.core.Rect-">stereoRectify</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
             int&nbsp;flags,
             double&nbsp;alpha,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImageSize,
             <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;validPixROI1,
             <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;validPixROI2)</code>&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Size-double-double-">stereoRectify</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
             int&nbsp;flags,
             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImageSize,
             double&nbsp;balance,
             double&nbsp;fov_scale)</code>&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoRectifyUncalibrated-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-">stereoRectifyUncalibrated</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imgSize,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H1,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H2)</code>&nbsp;</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#stereoRectifyUncalibrated-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-double-">stereoRectifyUncalibrated</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imgSize,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H1,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H2,
                         double&nbsp;threshold)</code>&nbsp;</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#triangulatePoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">triangulatePoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatr1,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatr2,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projPoints1,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projPoints2,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points4D)</code>&nbsp;</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#undistortImage-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">undistortImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</code>&nbsp;</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#undistortImage-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-">undistortImage</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Knew,
              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;new_size)</code>&nbsp;</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#undistortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">undistortPoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</code>&nbsp;</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#undistortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">undistortPoints</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P)</code>&nbsp;</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#validateDisparity-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">validateDisparity</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cost,
                 int&nbsp;minDisparity,
                 int&nbsp;numberOfDisparities)</code>&nbsp;</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/Calib3d.html#validateDisparity-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-">validateDisparity</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cost,
                 int&nbsp;minDisparity,
                 int&nbsp;numberOfDisparities,
                 int&nbsp;disp12MaxDisp)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CALIB_CB_ADAPTIVE_THRESH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_ADAPTIVE_THRESH</h4>
<pre>public static final&nbsp;int CALIB_CB_ADAPTIVE_THRESH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_ADAPTIVE_THRESH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CB_ASYMMETRIC_GRID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_ASYMMETRIC_GRID</h4>
<pre>public static final&nbsp;int CALIB_CB_ASYMMETRIC_GRID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_ASYMMETRIC_GRID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CB_CLUSTERING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_CLUSTERING</h4>
<pre>public static final&nbsp;int CALIB_CB_CLUSTERING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_CLUSTERING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CB_FAST_CHECK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_FAST_CHECK</h4>
<pre>public static final&nbsp;int CALIB_CB_FAST_CHECK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_FAST_CHECK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CB_FILTER_QUADS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_FILTER_QUADS</h4>
<pre>public static final&nbsp;int CALIB_CB_FILTER_QUADS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_FILTER_QUADS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CB_NORMALIZE_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_NORMALIZE_IMAGE</h4>
<pre>public static final&nbsp;int CALIB_CB_NORMALIZE_IMAGE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_NORMALIZE_IMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CB_SYMMETRIC_GRID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CB_SYMMETRIC_GRID</h4>
<pre>public static final&nbsp;int CALIB_CB_SYMMETRIC_GRID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CB_SYMMETRIC_GRID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_CHECK_COND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_CHECK_COND</h4>
<pre>public static final&nbsp;int CALIB_CHECK_COND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_CHECK_COND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_ASPECT_RATIO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_ASPECT_RATIO</h4>
<pre>public static final&nbsp;int CALIB_FIX_ASPECT_RATIO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_ASPECT_RATIO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_FOCAL_LENGTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_FOCAL_LENGTH</h4>
<pre>public static final&nbsp;int CALIB_FIX_FOCAL_LENGTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_FOCAL_LENGTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_INTRINSIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_INTRINSIC</h4>
<pre>public static final&nbsp;int CALIB_FIX_INTRINSIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_INTRINSIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_K1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_K1</h4>
<pre>public static final&nbsp;int CALIB_FIX_K1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_K1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_K2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_K2</h4>
<pre>public static final&nbsp;int CALIB_FIX_K2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_K2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_K3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_K3</h4>
<pre>public static final&nbsp;int CALIB_FIX_K3</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_K3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_K4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_K4</h4>
<pre>public static final&nbsp;int CALIB_FIX_K4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_K4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_K5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_K5</h4>
<pre>public static final&nbsp;int CALIB_FIX_K5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_K5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_K6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_K6</h4>
<pre>public static final&nbsp;int CALIB_FIX_K6</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_K6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_PRINCIPAL_POINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_PRINCIPAL_POINT</h4>
<pre>public static final&nbsp;int CALIB_FIX_PRINCIPAL_POINT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_PRINCIPAL_POINT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_S1_S2_S3_S4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_S1_S2_S3_S4</h4>
<pre>public static final&nbsp;int CALIB_FIX_S1_S2_S3_S4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_S1_S2_S3_S4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_SKEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_SKEW</h4>
<pre>public static final&nbsp;int CALIB_FIX_SKEW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_SKEW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_FIX_TAUX_TAUY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_FIX_TAUX_TAUY</h4>
<pre>public static final&nbsp;int CALIB_FIX_TAUX_TAUY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_FIX_TAUX_TAUY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_RATIONAL_MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_RATIONAL_MODEL</h4>
<pre>public static final&nbsp;int CALIB_RATIONAL_MODEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_RATIONAL_MODEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_RECOMPUTE_EXTRINSIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_RECOMPUTE_EXTRINSIC</h4>
<pre>public static final&nbsp;int CALIB_RECOMPUTE_EXTRINSIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_RECOMPUTE_EXTRINSIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_SAME_FOCAL_LENGTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_SAME_FOCAL_LENGTH</h4>
<pre>public static final&nbsp;int CALIB_SAME_FOCAL_LENGTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_SAME_FOCAL_LENGTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_THIN_PRISM_MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_THIN_PRISM_MODEL</h4>
<pre>public static final&nbsp;int CALIB_THIN_PRISM_MODEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_THIN_PRISM_MODEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_TILTED_MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_TILTED_MODEL</h4>
<pre>public static final&nbsp;int CALIB_TILTED_MODEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_TILTED_MODEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_USE_INTRINSIC_GUESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_USE_INTRINSIC_GUESS</h4>
<pre>public static final&nbsp;int CALIB_USE_INTRINSIC_GUESS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_USE_INTRINSIC_GUESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_USE_LU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_USE_LU</h4>
<pre>public static final&nbsp;int CALIB_USE_LU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_USE_LU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_ZERO_DISPARITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_ZERO_DISPARITY</h4>
<pre>public static final&nbsp;int CALIB_ZERO_DISPARITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_ZERO_DISPARITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CALIB_ZERO_TANGENT_DIST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALIB_ZERO_TANGENT_DIST</h4>
<pre>public static final&nbsp;int CALIB_ZERO_TANGENT_DIST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CALIB_ZERO_TANGENT_DIST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_DLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_DLS</h4>
<pre>public static final&nbsp;int CV_DLS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CV_DLS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_EPNP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_EPNP</h4>
<pre>public static final&nbsp;int CV_EPNP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CV_EPNP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_ITERATIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_ITERATIVE</h4>
<pre>public static final&nbsp;int CV_ITERATIVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CV_ITERATIVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_P3P">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_P3P</h4>
<pre>public static final&nbsp;int CV_P3P</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.CV_P3P">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FM_7POINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FM_7POINT</h4>
<pre>public static final&nbsp;int FM_7POINT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.FM_7POINT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FM_8POINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FM_8POINT</h4>
<pre>public static final&nbsp;int FM_8POINT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.FM_8POINT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FM_LMEDS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FM_LMEDS</h4>
<pre>public static final&nbsp;int FM_LMEDS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.FM_LMEDS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FM_RANSAC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FM_RANSAC</h4>
<pre>public static final&nbsp;int FM_RANSAC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.FM_RANSAC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LMEDS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LMEDS</h4>
<pre>public static final&nbsp;int LMEDS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.LMEDS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RANSAC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RANSAC</h4>
<pre>public static final&nbsp;int RANSAC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.RANSAC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RHO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RHO</h4>
<pre>public static final&nbsp;int RHO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.RHO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOLVEPNP_DLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SOLVEPNP_DLS</h4>
<pre>public static final&nbsp;int SOLVEPNP_DLS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.SOLVEPNP_DLS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOLVEPNP_EPNP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SOLVEPNP_EPNP</h4>
<pre>public static final&nbsp;int SOLVEPNP_EPNP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.SOLVEPNP_EPNP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOLVEPNP_ITERATIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SOLVEPNP_ITERATIVE</h4>
<pre>public static final&nbsp;int SOLVEPNP_ITERATIVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.SOLVEPNP_ITERATIVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOLVEPNP_P3P">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SOLVEPNP_P3P</h4>
<pre>public static final&nbsp;int SOLVEPNP_P3P</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.SOLVEPNP_P3P">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SOLVEPNP_UPNP">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SOLVEPNP_UPNP</h4>
<pre>public static final&nbsp;int SOLVEPNP_UPNP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.Calib3d.SOLVEPNP_UPNP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Calib3d--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Calib3d</h4>
<pre>public&nbsp;Calib3d()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="calibrate-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrate</h4>
<pre>public static&nbsp;double&nbsp;calibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
                               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs)</pre>
</li>
</ul>
<a name="calibrate-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrate</h4>
<pre>public static&nbsp;double&nbsp;calibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
                               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
                               int&nbsp;flags)</pre>
</li>
</ul>
<a name="calibrate-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrate</h4>
<pre>public static&nbsp;double&nbsp;calibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
                               <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
                               int&nbsp;flags,
                               <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
<a name="calibrateCamera-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrateCamera</h4>
<pre>public static&nbsp;double&nbsp;calibrateCamera(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs)</pre>
</li>
</ul>
<a name="calibrateCamera-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrateCamera</h4>
<pre>public static&nbsp;double&nbsp;calibrateCamera(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
                                     int&nbsp;flags)</pre>
</li>
</ul>
<a name="calibrateCamera-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-int-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrateCamera</h4>
<pre>public static&nbsp;double&nbsp;calibrateCamera(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rvecs,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;tvecs,
                                     int&nbsp;flags,
                                     <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
<a name="calibrationMatrixValues-org.opencv.core.Mat-org.opencv.core.Size-double-double-double:A-double:A-double:A-org.opencv.core.Point-double:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calibrationMatrixValues</h4>
<pre>public static&nbsp;void&nbsp;calibrationMatrixValues(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                           double&nbsp;apertureWidth,
                                           double&nbsp;apertureHeight,
                                           double[]&nbsp;fovx,
                                           double[]&nbsp;fovy,
                                           double[]&nbsp;focalLength,
                                           <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;principalPoint,
                                           double[]&nbsp;aspectRatio)</pre>
</li>
</ul>
<a name="composeRT-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>composeRT</h4>
<pre>public static&nbsp;void&nbsp;composeRT(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec3,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec3)</pre>
</li>
</ul>
<a name="composeRT-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>composeRT</h4>
<pre>public static&nbsp;void&nbsp;composeRT(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec3,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec3,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dr1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dt1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dr2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dr3dt2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dr1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dt1,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dr2,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dt3dt2)</pre>
</li>
</ul>
<a name="computeCorrespondEpilines-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>computeCorrespondEpilines</h4>
<pre>public static&nbsp;void&nbsp;computeCorrespondEpilines(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points,
                                             int&nbsp;whichImage,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lines)</pre>
</li>
</ul>
<a name="convertPointsFromHomogeneous-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertPointsFromHomogeneous</h4>
<pre>public static&nbsp;void&nbsp;convertPointsFromHomogeneous(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="convertPointsToHomogeneous-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertPointsToHomogeneous</h4>
<pre>public static&nbsp;void&nbsp;convertPointsToHomogeneous(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="correctMatches-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>correctMatches</h4>
<pre>public static&nbsp;void&nbsp;correctMatches(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;newPoints1,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;newPoints2)</pre>
</li>
</ul>
<a name="decomposeEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decomposeEssentialMat</h4>
<pre>public static&nbsp;void&nbsp;decomposeEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t)</pre>
</li>
</ul>
<a name="decomposeHomographyMat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decomposeHomographyMat</h4>
<pre>public static&nbsp;int&nbsp;decomposeHomographyMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H,
                                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;rotations,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;translations,
                                         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;normals)</pre>
</li>
</ul>
<a name="decomposeProjectionMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decomposeProjectionMatrix</h4>
<pre>public static&nbsp;void&nbsp;decomposeProjectionMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatrix,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrix,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;transVect)</pre>
</li>
</ul>
<a name="decomposeProjectionMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decomposeProjectionMatrix</h4>
<pre>public static&nbsp;void&nbsp;decomposeProjectionMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatrix,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrix,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;transVect,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrixX,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrixY,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rotMatrixZ,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eulerAngles)</pre>
</li>
</ul>
<a name="distortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>distortPoints</h4>
<pre>public static&nbsp;void&nbsp;distortPoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</pre>
</li>
</ul>
<a name="distortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>distortPoints</h4>
<pre>public static&nbsp;void&nbsp;distortPoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                 double&nbsp;alpha)</pre>
</li>
</ul>
<a name="drawChessboardCorners-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.MatOfPoint2f-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drawChessboardCorners</h4>
<pre>public static&nbsp;void&nbsp;drawChessboardCorners(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                         <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                                         <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;corners,
                                         boolean&nbsp;patternWasFound)</pre>
</li>
</ul>
<a name="estimateAffine3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateAffine3D</h4>
<pre>public static&nbsp;int&nbsp;estimateAffine3D(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;out,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inliers)</pre>
</li>
</ul>
<a name="estimateAffine3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateAffine3D</h4>
<pre>public static&nbsp;int&nbsp;estimateAffine3D(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;out,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inliers,
                                   double&nbsp;ransacThreshold,
                                   double&nbsp;confidence)</pre>
</li>
</ul>
<a name="estimateNewCameraMatrixForUndistortRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateNewCameraMatrixForUndistortRectify</h4>
<pre>public static&nbsp;void&nbsp;estimateNewCameraMatrixForUndistortRectify(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P)</pre>
</li>
</ul>
<a name="estimateNewCameraMatrixForUndistortRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Size-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateNewCameraMatrixForUndistortRectify</h4>
<pre>public static&nbsp;void&nbsp;estimateNewCameraMatrixForUndistortRectify(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;image_size,
                                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P,
                                                              double&nbsp;balance,
                                                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;new_size,
                                                              double&nbsp;fov_scale)</pre>
</li>
</ul>
<a name="filterSpeckles-org.opencv.core.Mat-double-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filterSpeckles</h4>
<pre>public static&nbsp;void&nbsp;filterSpeckles(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                  double&nbsp;newVal,
                                  int&nbsp;maxSpeckleSize,
                                  double&nbsp;maxDiff)</pre>
</li>
</ul>
<a name="filterSpeckles-org.opencv.core.Mat-double-int-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filterSpeckles</h4>
<pre>public static&nbsp;void&nbsp;filterSpeckles(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                                  double&nbsp;newVal,
                                  int&nbsp;maxSpeckleSize,
                                  double&nbsp;maxDiff,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf)</pre>
</li>
</ul>
<a name="findChessboardCorners-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.MatOfPoint2f-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findChessboardCorners</h4>
<pre>public static&nbsp;boolean&nbsp;findChessboardCorners(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                                            <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;corners)</pre>
</li>
</ul>
<a name="findChessboardCorners-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.MatOfPoint2f-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findChessboardCorners</h4>
<pre>public static&nbsp;boolean&nbsp;findChessboardCorners(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                                            <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;corners,
                                            int&nbsp;flags)</pre>
</li>
</ul>
<a name="findCirclesGrid-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findCirclesGrid</h4>
<pre>public static&nbsp;boolean&nbsp;findCirclesGrid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;centers)</pre>
</li>
</ul>
<a name="findCirclesGrid-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findCirclesGrid</h4>
<pre>public static&nbsp;boolean&nbsp;findCirclesGrid(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                                      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;patternSize,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;centers,
                                      int&nbsp;flags)</pre>
</li>
</ul>
<a name="findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findEssentialMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2)</pre>
</li>
</ul>
<a name="findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findEssentialMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                   double&nbsp;focal,
                                   <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp,
                                   int&nbsp;method,
                                   double&nbsp;prob,
                                   double&nbsp;threshold)</pre>
</li>
</ul>
<a name="findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-int-double-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findEssentialMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                   double&nbsp;focal,
                                   <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp,
                                   int&nbsp;method,
                                   double&nbsp;prob,
                                   double&nbsp;threshold,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findEssentialMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix)</pre>
</li>
</ul>
<a name="findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findEssentialMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                   int&nbsp;method,
                                   double&nbsp;prob,
                                   double&nbsp;threshold)</pre>
</li>
</ul>
<a name="findEssentialMat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-double-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findEssentialMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findEssentialMat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                   int&nbsp;method,
                                   double&nbsp;prob,
                                   double&nbsp;threshold,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="findFundamentalMat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findFundamentalMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findFundamentalMat(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points1,
                                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points2)</pre>
</li>
</ul>
<a name="findFundamentalMat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findFundamentalMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findFundamentalMat(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points1,
                                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points2,
                                     int&nbsp;method,
                                     double&nbsp;param1,
                                     double&nbsp;param2)</pre>
</li>
</ul>
<a name="findFundamentalMat-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findFundamentalMat</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findFundamentalMat(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points1,
                                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;points2,
                                     int&nbsp;method,
                                     double&nbsp;param1,
                                     double&nbsp;param2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="findHomography-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findHomography</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findHomography(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;srcPoints,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;dstPoints)</pre>
</li>
</ul>
<a name="findHomography-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findHomography</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findHomography(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;srcPoints,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;dstPoints,
                                 int&nbsp;method,
                                 double&nbsp;ransacReprojThreshold)</pre>
</li>
</ul>
<a name="findHomography-org.opencv.core.MatOfPoint2f-org.opencv.core.MatOfPoint2f-int-double-org.opencv.core.Mat-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findHomography</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;findHomography(<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;srcPoints,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;dstPoints,
                                 int&nbsp;method,
                                 double&nbsp;ransacReprojThreshold,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                 int&nbsp;maxIters,
                                 double&nbsp;confidence)</pre>
</li>
</ul>
<a name="getOptimalNewCameraMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOptimalNewCameraMatrix</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getOptimalNewCameraMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                            double&nbsp;alpha)</pre>
</li>
</ul>
<a name="getOptimalNewCameraMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-double-org.opencv.core.Size-org.opencv.core.Rect-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOptimalNewCameraMatrix</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getOptimalNewCameraMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs,
                                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                            double&nbsp;alpha,
                                            <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImgSize,
                                            <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;validPixROI,
                                            boolean&nbsp;centerPrincipalPoint)</pre>
</li>
</ul>
<a name="getValidDisparityROI-org.opencv.core.Rect-org.opencv.core.Rect-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValidDisparityROI</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;getValidDisparityROI(<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1,
                                        <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2,
                                        int&nbsp;minDisparity,
                                        int&nbsp;numberOfDisparities,
                                        int&nbsp;SADWindowSize)</pre>
</li>
</ul>
<a name="initCameraMatrix2D-java.util.List-java.util.List-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initCameraMatrix2D</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;initCameraMatrix2D(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize)</pre>
</li>
</ul>
<a name="initCameraMatrix2D-java.util.List-java.util.List-org.opencv.core.Size-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initCameraMatrix2D</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;initCameraMatrix2D(java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&gt;&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     double&nbsp;aspectRatio)</pre>
</li>
</ul>
<a name="initUndistortRectifyMap-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-int-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initUndistortRectifyMap</h4>
<pre>public static&nbsp;void&nbsp;initUndistortRectifyMap(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P,
                                           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;size,
                                           int&nbsp;m1type,
                                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;map1,
                                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;map2)</pre>
</li>
</ul>
<a name="matMulDeriv-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matMulDeriv</h4>
<pre>public static&nbsp;void&nbsp;matMulDeriv(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;A,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;B,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dABdA,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dABdB)</pre>
</li>
</ul>
<a name="projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfPoint2f-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectPoints</h4>
<pre>public static&nbsp;void&nbsp;projectPoints(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints)</pre>
</li>
</ul>
<a name="projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectPoints</h4>
<pre>public static&nbsp;void&nbsp;projectPoints(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;jacobian,
                                 double&nbsp;aspectRatio)</pre>
</li>
</ul>
<a name="projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectPoints</h4>
<pre>public static&nbsp;void&nbsp;projectPoints(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</pre>
</li>
</ul>
<a name="projectPoints-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectPoints</h4>
<pre>public static&nbsp;void&nbsp;projectPoints(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                                 <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                 double&nbsp;alpha,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;jacobian)</pre>
</li>
</ul>
<a name="recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recoverPose</h4>
<pre>public static&nbsp;int&nbsp;recoverPose(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t)</pre>
</li>
</ul>
<a name="recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recoverPose</h4>
<pre>public static&nbsp;int&nbsp;recoverPose(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t,
                              double&nbsp;focal,
                              <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp)</pre>
</li>
</ul>
<a name="recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Point-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recoverPose</h4>
<pre>public static&nbsp;int&nbsp;recoverPose(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t,
                              double&nbsp;focal,
                              <a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;pp,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recoverPose</h4>
<pre>public static&nbsp;int&nbsp;recoverPose(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t)</pre>
</li>
</ul>
<a name="recoverPose-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recoverPose</h4>
<pre>public static&nbsp;int&nbsp;recoverPose(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;t,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="rectify3Collinear-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-java.util.List-java.util.List-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Size-org.opencv.core.Rect-org.opencv.core.Rect-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rectify3Collinear</h4>
<pre>public static&nbsp;float&nbsp;rectify3Collinear(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix3,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs3,
                                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgpt1,
                                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imgpt3,
                                      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R12,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T12,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R13,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T13,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R3,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P3,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                                      double&nbsp;alpha,
                                      <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImgSize,
                                      <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi1,
                                      <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;roi2,
                                      int&nbsp;flags)</pre>
</li>
</ul>
<a name="reprojectImageTo3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reprojectImageTo3D</h4>
<pre>public static&nbsp;void&nbsp;reprojectImageTo3D(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_3dImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q)</pre>
</li>
</ul>
<a name="reprojectImageTo3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reprojectImageTo3D</h4>
<pre>public static&nbsp;void&nbsp;reprojectImageTo3D(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_3dImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                                      boolean&nbsp;handleMissingValues)</pre>
</li>
</ul>
<a name="reprojectImageTo3D-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reprojectImageTo3D</h4>
<pre>public static&nbsp;void&nbsp;reprojectImageTo3D(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_3dImage,
                                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                                      boolean&nbsp;handleMissingValues,
                                      int&nbsp;ddepth)</pre>
</li>
</ul>
<a name="Rodrigues-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Rodrigues</h4>
<pre>public static&nbsp;void&nbsp;Rodrigues(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="Rodrigues-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Rodrigues</h4>
<pre>public static&nbsp;void&nbsp;Rodrigues(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;jacobian)</pre>
</li>
</ul>
<a name="RQDecomp3x3-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RQDecomp3x3</h4>
<pre>public static&nbsp;double[]&nbsp;RQDecomp3x3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxR,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxQ)</pre>
</li>
</ul>
<a name="RQDecomp3x3-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RQDecomp3x3</h4>
<pre>public static&nbsp;double[]&nbsp;RQDecomp3x3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxR,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtxQ,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Qx,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Qy,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Qz)</pre>
</li>
</ul>
<a name="sampsonDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sampsonDistance</h4>
<pre>public static&nbsp;double&nbsp;sampsonDistance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;pt1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;pt2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F)</pre>
</li>
</ul>
<a name="solvePnP-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solvePnP</h4>
<pre>public static&nbsp;boolean&nbsp;solvePnP(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                               <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                               <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec)</pre>
</li>
</ul>
<a name="solvePnP-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solvePnP</h4>
<pre>public static&nbsp;boolean&nbsp;solvePnP(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                               <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                               <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                               boolean&nbsp;useExtrinsicGuess,
                               int&nbsp;flags)</pre>
</li>
</ul>
<a name="solvePnPRansac-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solvePnPRansac</h4>
<pre>public static&nbsp;boolean&nbsp;solvePnPRansac(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                     <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec)</pre>
</li>
</ul>
<a name="solvePnPRansac-org.opencv.core.MatOfPoint3f-org.opencv.core.MatOfPoint2f-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.Mat-org.opencv.core.Mat-boolean-int-float-double-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solvePnPRansac</h4>
<pre>public static&nbsp;boolean&nbsp;solvePnPRansac(<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core">MatOfPoint3f</a>&nbsp;objectPoints,
                                     <a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core">MatOfPoint2f</a>&nbsp;imagePoints,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix,
                                     <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;distCoeffs,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rvec,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                     boolean&nbsp;useExtrinsicGuess,
                                     int&nbsp;iterationsCount,
                                     float&nbsp;reprojectionError,
                                     double&nbsp;confidence,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inliers,
                                     int&nbsp;flags)</pre>
</li>
</ul>
<a name="stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoCalibrate</h4>
<pre>public static&nbsp;double&nbsp;stereoCalibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T)</pre>
</li>
</ul>
<a name="stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoCalibrate</h4>
<pre>public static&nbsp;double&nbsp;stereoCalibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                     int&nbsp;flags)</pre>
</li>
</ul>
<a name="stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoCalibrate</h4>
<pre>public static&nbsp;double&nbsp;stereoCalibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                     int&nbsp;flags,
                                     <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
<a name="stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoCalibrate</h4>
<pre>public static&nbsp;double&nbsp;stereoCalibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F)</pre>
</li>
</ul>
<a name="stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoCalibrate</h4>
<pre>public static&nbsp;double&nbsp;stereoCalibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                                     int&nbsp;flags)</pre>
</li>
</ul>
<a name="stereoCalibrate-java.util.List-java.util.List-java.util.List-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoCalibrate</h4>
<pre>public static&nbsp;double&nbsp;stereoCalibrate(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;objectPoints,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints1,
                                     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;imagePoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                                     <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;E,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                                     int&nbsp;flags,
                                     <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria)</pre>
</li>
</ul>
<a name="stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoRectify</h4>
<pre>public static&nbsp;void&nbsp;stereoRectify(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q)</pre>
</li>
</ul>
<a name="stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoRectify</h4>
<pre>public static&nbsp;void&nbsp;stereoRectify(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                                 int&nbsp;flags)</pre>
</li>
</ul>
<a name="stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-double-org.opencv.core.Size-org.opencv.core.Rect-org.opencv.core.Rect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoRectify</h4>
<pre>public static&nbsp;void&nbsp;stereoRectify(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cameraMatrix2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distCoeffs2,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;T,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                                 int&nbsp;flags,
                                 double&nbsp;alpha,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImageSize,
                                 <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;validPixROI1,
                                 <a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core">Rect</a>&nbsp;validPixROI2)</pre>
</li>
</ul>
<a name="stereoRectify-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Size-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoRectify</h4>
<pre>public static&nbsp;void&nbsp;stereoRectify(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D2,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imageSize,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;tvec,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Q,
                                 int&nbsp;flags,
                                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;newImageSize,
                                 double&nbsp;balance,
                                 double&nbsp;fov_scale)</pre>
</li>
</ul>
<a name="stereoRectifyUncalibrated-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoRectifyUncalibrated</h4>
<pre>public static&nbsp;boolean&nbsp;stereoRectifyUncalibrated(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imgSize,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H1,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H2)</pre>
</li>
</ul>
<a name="stereoRectifyUncalibrated-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-org.opencv.core.Mat-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stereoRectifyUncalibrated</h4>
<pre>public static&nbsp;boolean&nbsp;stereoRectifyUncalibrated(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points1,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points2,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;F,
                                                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;imgSize,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H1,
                                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;H2,
                                                double&nbsp;threshold)</pre>
</li>
</ul>
<a name="triangulatePoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>triangulatePoints</h4>
<pre>public static&nbsp;void&nbsp;triangulatePoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatr1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projMatr2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projPoints1,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;projPoints2,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;points4D)</pre>
</li>
</ul>
<a name="undistortImage-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>undistortImage</h4>
<pre>public static&nbsp;void&nbsp;undistortImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</pre>
</li>
</ul>
<a name="undistortImage-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>undistortImage</h4>
<pre>public static&nbsp;void&nbsp;undistortImage(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;Knew,
                                  <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;new_size)</pre>
</li>
</ul>
<a name="undistortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>undistortPoints</h4>
<pre>public static&nbsp;void&nbsp;undistortPoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D)</pre>
</li>
</ul>
<a name="undistortPoints-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>undistortPoints</h4>
<pre>public static&nbsp;void&nbsp;undistortPoints(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;distorted,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;undistorted,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;K,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;D,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;R,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;P)</pre>
</li>
</ul>
<a name="validateDisparity-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validateDisparity</h4>
<pre>public static&nbsp;void&nbsp;validateDisparity(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cost,
                                     int&nbsp;minDisparity,
                                     int&nbsp;numberOfDisparities)</pre>
</li>
</ul>
<a name="validateDisparity-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>validateDisparity</h4>
<pre>public static&nbsp;void&nbsp;validateDisparity(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity,
                                     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;cost,
                                     int&nbsp;minDisparity,
                                     int&nbsp;numberOfDisparities,
                                     int&nbsp;disp12MaxDisp)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/Calib3d.html" target="_top">Frames</a></li>
<li><a href="Calib3d.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
