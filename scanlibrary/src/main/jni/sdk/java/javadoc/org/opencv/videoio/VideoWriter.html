<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>VideoWriter</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VideoWriter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/VideoWriter.html" target="_top">Frames</a></li>
<li><a href="VideoWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.videoio</div>
<h2 title="Class VideoWriter" class="title">Class VideoWriter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.videoio.VideoWriter</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">VideoWriter</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter--">VideoWriter</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-double-org.opencv.core.Size-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#VideoWriter-java.lang.String-int-double-org.opencv.core.Size-boolean-">VideoWriter</a></span>(java.lang.String&nbsp;filename,
           int&nbsp;fourcc,
           double&nbsp;fps,
           <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
           boolean&nbsp;isColor)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#fourcc-char-char-char-char-">fourcc</a></span>(char&nbsp;c1,
      char&nbsp;c2,
      char&nbsp;c3,
      char&nbsp;c4)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#get-int-">get</a></span>(int&nbsp;propId)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#isOpened--">isOpened</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-double-org.opencv.core.Size-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#open-java.lang.String-int-double-org.opencv.core.Size-boolean-">open</a></span>(java.lang.String&nbsp;filename,
    int&nbsp;fourcc,
    double&nbsp;fps,
    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
    boolean&nbsp;isColor)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#release--">release</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#set-int-double-">set</a></span>(int&nbsp;propId,
   double&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/videoio/VideoWriter.html#write-org.opencv.core.Mat-">write</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VideoWriter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter()</pre>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</pre>
</li>
</ul>
<a name="VideoWriter-java.lang.String-int-double-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VideoWriter</h4>
<pre>public&nbsp;VideoWriter(java.lang.String&nbsp;filename,
                   int&nbsp;fourcc,
                   double&nbsp;fps,
                   <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                   boolean&nbsp;isColor)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="fourcc-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fourcc</h4>
<pre>public static&nbsp;int&nbsp;fourcc(char&nbsp;c1,
                         char&nbsp;c2,
                         char&nbsp;c3,
                         char&nbsp;c4)</pre>
</li>
</ul>
<a name="get-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;double&nbsp;get(int&nbsp;propId)</pre>
</li>
</ul>
<a name="isOpened--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOpened</h4>
<pre>public&nbsp;boolean&nbsp;isOpened()</pre>
</li>
</ul>
<a name="open-java.lang.String-int-double-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize)</pre>
</li>
</ul>
<a name="open-java.lang.String-int-double-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;filename,
                    int&nbsp;fourcc,
                    double&nbsp;fps,
                    <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;frameSize,
                    boolean&nbsp;isColor)</pre>
</li>
</ul>
<a name="release--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>release</h4>
<pre>public&nbsp;void&nbsp;release()</pre>
</li>
</ul>
<a name="set-int-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre>public&nbsp;boolean&nbsp;set(int&nbsp;propId,
                   double&nbsp;value)</pre>
</li>
</ul>
<a name="write-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/videoio/Videoio.html" title="class in org.opencv.videoio"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/videoio/VideoWriter.html" target="_top">Frames</a></li>
<li><a href="VideoWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
