<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>FeatureDetector</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FeatureDetector";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/Features2d.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/FeatureDetector.html" target="_top">Frames</a></li>
<li><a href="FeatureDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.features2d</div>
<h2 title="Class FeatureDetector" class="title">Class FeatureDetector</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.features2d.FeatureDetector</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FeatureDetector</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#AKAZE">AKAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#BRISK">BRISK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DENSE">DENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_AKAZE">DYNAMIC_AKAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_BRISK">DYNAMIC_BRISK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_DENSE">DYNAMIC_DENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_FAST">DYNAMIC_FAST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_GFTT">DYNAMIC_GFTT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_HARRIS">DYNAMIC_HARRIS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_MSER">DYNAMIC_MSER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_ORB">DYNAMIC_ORB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_SIFT">DYNAMIC_SIFT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_SIMPLEBLOB">DYNAMIC_SIMPLEBLOB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_STAR">DYNAMIC_STAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#DYNAMIC_SURF">DYNAMIC_SURF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#FAST">FAST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GFTT">GFTT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_AKAZE">GRID_AKAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_BRISK">GRID_BRISK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_DENSE">GRID_DENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_FAST">GRID_FAST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_GFTT">GRID_GFTT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_HARRIS">GRID_HARRIS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_MSER">GRID_MSER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_ORB">GRID_ORB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_SIFT">GRID_SIFT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_SIMPLEBLOB">GRID_SIMPLEBLOB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_STAR">GRID_STAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#GRID_SURF">GRID_SURF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#HARRIS">HARRIS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#MSER">MSER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#ORB">ORB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_AKAZE">PYRAMID_AKAZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_BRISK">PYRAMID_BRISK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_DENSE">PYRAMID_DENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_FAST">PYRAMID_FAST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_GFTT">PYRAMID_GFTT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_HARRIS">PYRAMID_HARRIS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_MSER">PYRAMID_MSER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_ORB">PYRAMID_ORB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_SIFT">PYRAMID_SIFT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_SIMPLEBLOB">PYRAMID_SIMPLEBLOB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_STAR">PYRAMID_STAR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#PYRAMID_SURF">PYRAMID_SURF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#SIFT">SIFT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#SIMPLEBLOB">SIMPLEBLOB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#STAR">STAR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#SURF">SURF</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/features2d/FeatureDetector.html" title="class in org.opencv.features2d">FeatureDetector</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#create-int-">create</a></span>(int&nbsp;detectorType)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#detect-java.util.List-java.util.List-">detect</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
      java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#detect-java.util.List-java.util.List-java.util.List-">detect</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
      java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
      <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">detect</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
      <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#empty--">empty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#read-java.lang.String-">read</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/features2d/FeatureDetector.html#write-java.lang.String-">write</a></span>(java.lang.String&nbsp;fileName)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="AKAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AKAZE</h4>
<pre>public static final&nbsp;int AKAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.AKAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BRISK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BRISK</h4>
<pre>public static final&nbsp;int BRISK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.BRISK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DENSE</h4>
<pre>public static final&nbsp;int DENSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DENSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_AKAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_AKAZE</h4>
<pre>public static final&nbsp;int DYNAMIC_AKAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_AKAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_BRISK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_BRISK</h4>
<pre>public static final&nbsp;int DYNAMIC_BRISK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_BRISK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_DENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_DENSE</h4>
<pre>public static final&nbsp;int DYNAMIC_DENSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_DENSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_FAST</h4>
<pre>public static final&nbsp;int DYNAMIC_FAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_FAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_GFTT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_GFTT</h4>
<pre>public static final&nbsp;int DYNAMIC_GFTT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_GFTT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_HARRIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_HARRIS</h4>
<pre>public static final&nbsp;int DYNAMIC_HARRIS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_HARRIS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_MSER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_MSER</h4>
<pre>public static final&nbsp;int DYNAMIC_MSER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_MSER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_ORB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_ORB</h4>
<pre>public static final&nbsp;int DYNAMIC_ORB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_ORB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_SIFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_SIFT</h4>
<pre>public static final&nbsp;int DYNAMIC_SIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_SIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_SIMPLEBLOB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_SIMPLEBLOB</h4>
<pre>public static final&nbsp;int DYNAMIC_SIMPLEBLOB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_SIMPLEBLOB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_STAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_STAR</h4>
<pre>public static final&nbsp;int DYNAMIC_STAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_STAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DYNAMIC_SURF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DYNAMIC_SURF</h4>
<pre>public static final&nbsp;int DYNAMIC_SURF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.DYNAMIC_SURF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FAST</h4>
<pre>public static final&nbsp;int FAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.FAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GFTT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GFTT</h4>
<pre>public static final&nbsp;int GFTT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GFTT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_AKAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_AKAZE</h4>
<pre>public static final&nbsp;int GRID_AKAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_AKAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_BRISK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_BRISK</h4>
<pre>public static final&nbsp;int GRID_BRISK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_BRISK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_DENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_DENSE</h4>
<pre>public static final&nbsp;int GRID_DENSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_DENSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_FAST</h4>
<pre>public static final&nbsp;int GRID_FAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_FAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_GFTT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_GFTT</h4>
<pre>public static final&nbsp;int GRID_GFTT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_GFTT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_HARRIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_HARRIS</h4>
<pre>public static final&nbsp;int GRID_HARRIS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_HARRIS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_MSER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_MSER</h4>
<pre>public static final&nbsp;int GRID_MSER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_MSER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_ORB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_ORB</h4>
<pre>public static final&nbsp;int GRID_ORB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_ORB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_SIFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_SIFT</h4>
<pre>public static final&nbsp;int GRID_SIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_SIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_SIMPLEBLOB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_SIMPLEBLOB</h4>
<pre>public static final&nbsp;int GRID_SIMPLEBLOB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_SIMPLEBLOB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_STAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_STAR</h4>
<pre>public static final&nbsp;int GRID_STAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_STAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GRID_SURF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_SURF</h4>
<pre>public static final&nbsp;int GRID_SURF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.GRID_SURF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HARRIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HARRIS</h4>
<pre>public static final&nbsp;int HARRIS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.HARRIS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MSER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MSER</h4>
<pre>public static final&nbsp;int MSER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.MSER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ORB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ORB</h4>
<pre>public static final&nbsp;int ORB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.ORB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_AKAZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_AKAZE</h4>
<pre>public static final&nbsp;int PYRAMID_AKAZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_AKAZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_BRISK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_BRISK</h4>
<pre>public static final&nbsp;int PYRAMID_BRISK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_BRISK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_DENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_DENSE</h4>
<pre>public static final&nbsp;int PYRAMID_DENSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_DENSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_FAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_FAST</h4>
<pre>public static final&nbsp;int PYRAMID_FAST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_FAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_GFTT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_GFTT</h4>
<pre>public static final&nbsp;int PYRAMID_GFTT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_GFTT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_HARRIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_HARRIS</h4>
<pre>public static final&nbsp;int PYRAMID_HARRIS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_HARRIS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_MSER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_MSER</h4>
<pre>public static final&nbsp;int PYRAMID_MSER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_MSER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_ORB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_ORB</h4>
<pre>public static final&nbsp;int PYRAMID_ORB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_ORB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_SIFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_SIFT</h4>
<pre>public static final&nbsp;int PYRAMID_SIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_SIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_SIMPLEBLOB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_SIMPLEBLOB</h4>
<pre>public static final&nbsp;int PYRAMID_SIMPLEBLOB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_SIMPLEBLOB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_STAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_STAR</h4>
<pre>public static final&nbsp;int PYRAMID_STAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_STAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PYRAMID_SURF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PYRAMID_SURF</h4>
<pre>public static final&nbsp;int PYRAMID_SURF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.PYRAMID_SURF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SIFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SIFT</h4>
<pre>public static final&nbsp;int SIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.SIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SIMPLEBLOB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SIMPLEBLOB</h4>
<pre>public static final&nbsp;int SIMPLEBLOB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.SIMPLEBLOB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STAR</h4>
<pre>public static final&nbsp;int STAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.STAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SURF">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SURF</h4>
<pre>public static final&nbsp;int SURF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.features2d.FeatureDetector.SURF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/features2d/FeatureDetector.html" title="class in org.opencv.features2d">FeatureDetector</a>&nbsp;create(int&nbsp;detectorType)</pre>
</li>
</ul>
<a name="detect-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                   java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints)</pre>
</li>
</ul>
<a name="detect-java.util.List-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;images,
                   java.util.List&lt;<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&gt;&nbsp;keypoints,
                   java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;masks)</pre>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                   <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints)</pre>
</li>
</ul>
<a name="detect-org.opencv.core.Mat-org.opencv.core.MatOfKeyPoint-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detect</h4>
<pre>public&nbsp;void&nbsp;detect(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                   <a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core">MatOfKeyPoint</a>&nbsp;keypoints,
                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
</li>
</ul>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;void&nbsp;read(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
<a name="write-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;fileName)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/features2d/DescriptorMatcher.html" title="class in org.opencv.features2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/features2d/Features2d.html" title="class in org.opencv.features2d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/features2d/FeatureDetector.html" target="_top">Frames</a></li>
<li><a href="FeatureDetector.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
