<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>ANN_MLP</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ANN_MLP";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/ANN_MLP.html" target="_top">Frames</a></li>
<li><a href="ANN_MLP.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class ANN_MLP" class="title">Class ANN_MLP</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">org.opencv.ml.StatModel</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.ANN_MLP</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ANN_MLP</span>
extends <a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#BACKPROP">BACKPROP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#GAUSSIAN">GAUSSIAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#IDENTITY">IDENTITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#NO_INPUT_SCALE">NO_INPUT_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#NO_OUTPUT_SCALE">NO_OUTPUT_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#RPROP">RPROP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#SIGMOID_SYM">SIGMOID_SYM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#UPDATE_WEIGHTS">UPDATE_WEIGHTS</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#COMPRESSED_INPUT">COMPRESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#PREPROCESSED_INPUT">PREPROCESSED_INPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#RAW_OUTPUT">RAW_OUTPUT</a>, <a href="../../../org/opencv/ml/StatModel.html#UPDATE_MODEL">UPDATE_MODEL</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#create--">create</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getBackpropMomentumScale--">getBackpropMomentumScale</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getBackpropWeightScale--">getBackpropWeightScale</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getLayerSizes--">getLayerSizes</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDW0--">getRpropDW0</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWMax--">getRpropDWMax</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWMin--">getRpropDWMin</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWMinus--">getRpropDWMinus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getRpropDWPlus--">getRpropDWPlus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getTermCriteria--">getTermCriteria</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getTrainMethod--">getTrainMethod</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#getWeights-int-">getWeights</a></span>(int&nbsp;layerIdx)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setActivationFunction-int-">setActivationFunction</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setActivationFunction-int-double-double-">setActivationFunction</a></span>(int&nbsp;type,
                     double&nbsp;param1,
                     double&nbsp;param2)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setBackpropMomentumScale-double-">setBackpropMomentumScale</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setBackpropWeightScale-double-">setBackpropWeightScale</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setLayerSizes-org.opencv.core.Mat-">setLayerSizes</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_layer_sizes)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDW0-double-">setRpropDW0</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWMax-double-">setRpropDWMax</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWMin-double-">setRpropDWMin</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWMinus-double-">setRpropDWMinus</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setRpropDWPlus-double-">setRpropDWPlus</a></span>(double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTermCriteria-org.opencv.core.TermCriteria-">setTermCriteria</a></span>(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTrainMethod-int-">setTrainMethod</a></span>(int&nbsp;method)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/ANN_MLP.html#setTrainMethod-int-double-double-">setTrainMethod</a></span>(int&nbsp;method,
              double&nbsp;param1,
              double&nbsp;param2)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.ml.StatModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.ml.<a href="../../../org/opencv/ml/StatModel.html" title="class in org.opencv.ml">StatModel</a></h3>
<code><a href="../../../org/opencv/ml/StatModel.html#empty--">empty</a>, <a href="../../../org/opencv/ml/StatModel.html#getVarCount--">getVarCount</a>, <a href="../../../org/opencv/ml/StatModel.html#isClassifier--">isClassifier</a>, <a href="../../../org/opencv/ml/StatModel.html#isTrained--">isTrained</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#predict-org.opencv.core.Mat-org.opencv.core.Mat-int-">predict</a>, <a href="../../../org/opencv/ml/StatModel.html#train-org.opencv.core.Mat-int-org.opencv.core.Mat-">train</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BACKPROP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BACKPROP</h4>
<pre>public static final&nbsp;int BACKPROP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.BACKPROP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GAUSSIAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GAUSSIAN</h4>
<pre>public static final&nbsp;int GAUSSIAN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.GAUSSIAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IDENTITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IDENTITY</h4>
<pre>public static final&nbsp;int IDENTITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.IDENTITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NO_INPUT_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NO_INPUT_SCALE</h4>
<pre>public static final&nbsp;int NO_INPUT_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.NO_INPUT_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NO_OUTPUT_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NO_OUTPUT_SCALE</h4>
<pre>public static final&nbsp;int NO_OUTPUT_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.NO_OUTPUT_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RPROP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RPROP</h4>
<pre>public static final&nbsp;int RPROP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.RPROP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SIGMOID_SYM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SIGMOID_SYM</h4>
<pre>public static final&nbsp;int SIGMOID_SYM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.SIGMOID_SYM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UPDATE_WEIGHTS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UPDATE_WEIGHTS</h4>
<pre>public static final&nbsp;int UPDATE_WEIGHTS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.ml.ANN_MLP.UPDATE_WEIGHTS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/ANN_MLP.html" title="class in org.opencv.ml">ANN_MLP</a>&nbsp;create()</pre>
</li>
</ul>
<a name="getBackpropMomentumScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackpropMomentumScale</h4>
<pre>public&nbsp;double&nbsp;getBackpropMomentumScale()</pre>
</li>
</ul>
<a name="getBackpropWeightScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackpropWeightScale</h4>
<pre>public&nbsp;double&nbsp;getBackpropWeightScale()</pre>
</li>
</ul>
<a name="getLayerSizes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayerSizes</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getLayerSizes()</pre>
</li>
</ul>
<a name="getRpropDW0--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDW0</h4>
<pre>public&nbsp;double&nbsp;getRpropDW0()</pre>
</li>
</ul>
<a name="getRpropDWMax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWMax</h4>
<pre>public&nbsp;double&nbsp;getRpropDWMax()</pre>
</li>
</ul>
<a name="getRpropDWMin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWMin</h4>
<pre>public&nbsp;double&nbsp;getRpropDWMin()</pre>
</li>
</ul>
<a name="getRpropDWMinus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWMinus</h4>
<pre>public&nbsp;double&nbsp;getRpropDWMinus()</pre>
</li>
</ul>
<a name="getRpropDWPlus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRpropDWPlus</h4>
<pre>public&nbsp;double&nbsp;getRpropDWPlus()</pre>
</li>
</ul>
<a name="getTermCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTermCriteria</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;getTermCriteria()</pre>
</li>
</ul>
<a name="getTrainMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainMethod</h4>
<pre>public&nbsp;int&nbsp;getTrainMethod()</pre>
</li>
</ul>
<a name="getWeights-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getWeights(int&nbsp;layerIdx)</pre>
</li>
</ul>
<a name="setActivationFunction-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivationFunction</h4>
<pre>public&nbsp;void&nbsp;setActivationFunction(int&nbsp;type)</pre>
</li>
</ul>
<a name="setActivationFunction-int-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivationFunction</h4>
<pre>public&nbsp;void&nbsp;setActivationFunction(int&nbsp;type,
                                  double&nbsp;param1,
                                  double&nbsp;param2)</pre>
</li>
</ul>
<a name="setBackpropMomentumScale-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackpropMomentumScale</h4>
<pre>public&nbsp;void&nbsp;setBackpropMomentumScale(double&nbsp;val)</pre>
</li>
</ul>
<a name="setBackpropWeightScale-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackpropWeightScale</h4>
<pre>public&nbsp;void&nbsp;setBackpropWeightScale(double&nbsp;val)</pre>
</li>
</ul>
<a name="setLayerSizes-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLayerSizes</h4>
<pre>public&nbsp;void&nbsp;setLayerSizes(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;_layer_sizes)</pre>
</li>
</ul>
<a name="setRpropDW0-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDW0</h4>
<pre>public&nbsp;void&nbsp;setRpropDW0(double&nbsp;val)</pre>
</li>
</ul>
<a name="setRpropDWMax-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWMax</h4>
<pre>public&nbsp;void&nbsp;setRpropDWMax(double&nbsp;val)</pre>
</li>
</ul>
<a name="setRpropDWMin-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWMin</h4>
<pre>public&nbsp;void&nbsp;setRpropDWMin(double&nbsp;val)</pre>
</li>
</ul>
<a name="setRpropDWMinus-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWMinus</h4>
<pre>public&nbsp;void&nbsp;setRpropDWMinus(double&nbsp;val)</pre>
</li>
</ul>
<a name="setRpropDWPlus-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRpropDWPlus</h4>
<pre>public&nbsp;void&nbsp;setRpropDWPlus(double&nbsp;val)</pre>
</li>
</ul>
<a name="setTermCriteria-org.opencv.core.TermCriteria-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTermCriteria</h4>
<pre>public&nbsp;void&nbsp;setTermCriteria(<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;val)</pre>
</li>
</ul>
<a name="setTrainMethod-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainMethod</h4>
<pre>public&nbsp;void&nbsp;setTrainMethod(int&nbsp;method)</pre>
</li>
</ul>
<a name="setTrainMethod-int-double-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTrainMethod</h4>
<pre>public&nbsp;void&nbsp;setTrainMethod(int&nbsp;method,
                           double&nbsp;param1,
                           double&nbsp;param2)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/ml/Boost.html" title="class in org.opencv.ml"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/ANN_MLP.html" target="_top">Frames</a></li>
<li><a href="ANN_MLP.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
