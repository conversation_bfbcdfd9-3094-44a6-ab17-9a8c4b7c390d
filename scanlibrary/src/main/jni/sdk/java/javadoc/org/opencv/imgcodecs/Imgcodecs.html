<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>Imgcodecs</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Imgcodecs";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgcodecs/Imgcodecs.html" target="_top">Frames</a></li>
<li><a href="Imgcodecs.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.imgcodecs</div>
<h2 title="Class Imgcodecs" class="title">Class Imgcodecs</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.imgcodecs.Imgcodecs</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Imgcodecs</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_CVTIMG_FLIP">CV_CVTIMG_FLIP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_CVTIMG_SWAP_RB">CV_CVTIMG_SWAP_RB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_JPEG_CHROMA_QUALITY">CV_IMWRITE_JPEG_CHROMA_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_JPEG_LUMA_QUALITY">CV_IMWRITE_JPEG_LUMA_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_JPEG_OPTIMIZE">CV_IMWRITE_JPEG_OPTIMIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_JPEG_PROGRESSIVE">CV_IMWRITE_JPEG_PROGRESSIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_JPEG_QUALITY">CV_IMWRITE_JPEG_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_JPEG_RST_INTERVAL">CV_IMWRITE_JPEG_RST_INTERVAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_BILEVEL">CV_IMWRITE_PNG_BILEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_COMPRESSION">CV_IMWRITE_PNG_COMPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_STRATEGY">CV_IMWRITE_PNG_STRATEGY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_STRATEGY_DEFAULT">CV_IMWRITE_PNG_STRATEGY_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_STRATEGY_FILTERED">CV_IMWRITE_PNG_STRATEGY_FILTERED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_STRATEGY_FIXED">CV_IMWRITE_PNG_STRATEGY_FIXED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">CV_IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PNG_STRATEGY_RLE">CV_IMWRITE_PNG_STRATEGY_RLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_PXM_BINARY">CV_IMWRITE_PXM_BINARY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_IMWRITE_WEBP_QUALITY">CV_IMWRITE_WEBP_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_LOAD_IMAGE_ANYCOLOR">CV_LOAD_IMAGE_ANYCOLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_LOAD_IMAGE_ANYDEPTH">CV_LOAD_IMAGE_ANYDEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_LOAD_IMAGE_COLOR">CV_LOAD_IMAGE_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_LOAD_IMAGE_GRAYSCALE">CV_LOAD_IMAGE_GRAYSCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#CV_LOAD_IMAGE_UNCHANGED">CV_LOAD_IMAGE_UNCHANGED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_ANYCOLOR">IMREAD_ANYCOLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_ANYDEPTH">IMREAD_ANYDEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_COLOR">IMREAD_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_GRAYSCALE">IMREAD_GRAYSCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_LOAD_GDAL">IMREAD_LOAD_GDAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_2">IMREAD_REDUCED_COLOR_2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_4">IMREAD_REDUCED_COLOR_4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_COLOR_8">IMREAD_REDUCED_COLOR_8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_2">IMREAD_REDUCED_GRAYSCALE_2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_4">IMREAD_REDUCED_GRAYSCALE_4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_REDUCED_GRAYSCALE_8">IMREAD_REDUCED_GRAYSCALE_8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMREAD_UNCHANGED">IMREAD_UNCHANGED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_CHROMA_QUALITY">IMWRITE_JPEG_CHROMA_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_LUMA_QUALITY">IMWRITE_JPEG_LUMA_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_OPTIMIZE">IMWRITE_JPEG_OPTIMIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_PROGRESSIVE">IMWRITE_JPEG_PROGRESSIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_QUALITY">IMWRITE_JPEG_QUALITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_JPEG_RST_INTERVAL">IMWRITE_JPEG_RST_INTERVAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_BILEVEL">IMWRITE_PNG_BILEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_COMPRESSION">IMWRITE_PNG_COMPRESSION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY">IMWRITE_PNG_STRATEGY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_DEFAULT">IMWRITE_PNG_STRATEGY_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_FILTERED">IMWRITE_PNG_STRATEGY_FILTERED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_FIXED">IMWRITE_PNG_STRATEGY_FIXED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PNG_STRATEGY_RLE">IMWRITE_PNG_STRATEGY_RLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_PXM_BINARY">IMWRITE_PXM_BINARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#IMWRITE_WEBP_QUALITY">IMWRITE_WEBP_QUALITY</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#Imgcodecs--">Imgcodecs</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imdecode-org.opencv.core.Mat-int-">imdecode</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
        int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-">imencode</a></span>(java.lang.String&nbsp;ext,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-org.opencv.core.MatOfInt-">imencode</a></span>(java.lang.String&nbsp;ext,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
        <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
        <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imread-java.lang.String-">imread</a></span>(java.lang.String&nbsp;filename)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imread-java.lang.String-int-">imread</a></span>(java.lang.String&nbsp;filename,
      int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imreadmulti-java.lang.String-java.util.List-">imreadmulti</a></span>(java.lang.String&nbsp;filename,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imreadmulti-java.lang.String-java.util.List-int-">imreadmulti</a></span>(java.lang.String&nbsp;filename,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
           int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imwrite-java.lang.String-org.opencv.core.Mat-">imwrite</a></span>(java.lang.String&nbsp;filename,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/imgcodecs/Imgcodecs.html#imwrite-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfInt-">imwrite</a></span>(java.lang.String&nbsp;filename,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
       <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CV_CVTIMG_FLIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_CVTIMG_FLIP</h4>
<pre>public static final&nbsp;int CV_CVTIMG_FLIP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_CVTIMG_FLIP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_CVTIMG_SWAP_RB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_CVTIMG_SWAP_RB</h4>
<pre>public static final&nbsp;int CV_CVTIMG_SWAP_RB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_CVTIMG_SWAP_RB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_JPEG_CHROMA_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_JPEG_CHROMA_QUALITY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_JPEG_CHROMA_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_JPEG_CHROMA_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_JPEG_LUMA_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_JPEG_LUMA_QUALITY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_JPEG_LUMA_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_JPEG_LUMA_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_JPEG_OPTIMIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_JPEG_OPTIMIZE</h4>
<pre>public static final&nbsp;int CV_IMWRITE_JPEG_OPTIMIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_JPEG_OPTIMIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_JPEG_PROGRESSIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_JPEG_PROGRESSIVE</h4>
<pre>public static final&nbsp;int CV_IMWRITE_JPEG_PROGRESSIVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_JPEG_PROGRESSIVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_JPEG_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_JPEG_QUALITY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_JPEG_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_JPEG_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_JPEG_RST_INTERVAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_JPEG_RST_INTERVAL</h4>
<pre>public static final&nbsp;int CV_IMWRITE_JPEG_RST_INTERVAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_JPEG_RST_INTERVAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_BILEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_BILEVEL</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_BILEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_BILEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_COMPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_COMPRESSION</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_COMPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_COMPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_STRATEGY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_STRATEGY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_STRATEGY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_STRATEGY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_STRATEGY_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_STRATEGY_DEFAULT</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_STRATEGY_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_STRATEGY_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_STRATEGY_FILTERED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_STRATEGY_FILTERED</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_STRATEGY_FILTERED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_STRATEGY_FILTERED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_STRATEGY_FIXED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_STRATEGY_FIXED</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_STRATEGY_FIXED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_STRATEGY_FIXED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PNG_STRATEGY_RLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PNG_STRATEGY_RLE</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PNG_STRATEGY_RLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PNG_STRATEGY_RLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_PXM_BINARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_PXM_BINARY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_PXM_BINARY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_PXM_BINARY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_IMWRITE_WEBP_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_IMWRITE_WEBP_QUALITY</h4>
<pre>public static final&nbsp;int CV_IMWRITE_WEBP_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_IMWRITE_WEBP_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_LOAD_IMAGE_ANYCOLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_LOAD_IMAGE_ANYCOLOR</h4>
<pre>public static final&nbsp;int CV_LOAD_IMAGE_ANYCOLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_LOAD_IMAGE_ANYCOLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_LOAD_IMAGE_ANYDEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_LOAD_IMAGE_ANYDEPTH</h4>
<pre>public static final&nbsp;int CV_LOAD_IMAGE_ANYDEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_LOAD_IMAGE_ANYDEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_LOAD_IMAGE_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_LOAD_IMAGE_COLOR</h4>
<pre>public static final&nbsp;int CV_LOAD_IMAGE_COLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_LOAD_IMAGE_COLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_LOAD_IMAGE_GRAYSCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_LOAD_IMAGE_GRAYSCALE</h4>
<pre>public static final&nbsp;int CV_LOAD_IMAGE_GRAYSCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_LOAD_IMAGE_GRAYSCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CV_LOAD_IMAGE_UNCHANGED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV_LOAD_IMAGE_UNCHANGED</h4>
<pre>public static final&nbsp;int CV_LOAD_IMAGE_UNCHANGED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.CV_LOAD_IMAGE_UNCHANGED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_ANYCOLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_ANYCOLOR</h4>
<pre>public static final&nbsp;int IMREAD_ANYCOLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYCOLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_ANYDEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_ANYDEPTH</h4>
<pre>public static final&nbsp;int IMREAD_ANYDEPTH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_ANYDEPTH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_COLOR</h4>
<pre>public static final&nbsp;int IMREAD_COLOR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_COLOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_GRAYSCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_GRAYSCALE</h4>
<pre>public static final&nbsp;int IMREAD_GRAYSCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_GRAYSCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_LOAD_GDAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_LOAD_GDAL</h4>
<pre>public static final&nbsp;int IMREAD_LOAD_GDAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_LOAD_GDAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_COLOR_2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_COLOR_2</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_COLOR_2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_COLOR_4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_COLOR_4</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_COLOR_4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_COLOR_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_COLOR_8</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_COLOR_8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_COLOR_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_GRAYSCALE_2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_GRAYSCALE_2</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_GRAYSCALE_2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_GRAYSCALE_4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_GRAYSCALE_4</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_GRAYSCALE_4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_REDUCED_GRAYSCALE_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_REDUCED_GRAYSCALE_8</h4>
<pre>public static final&nbsp;int IMREAD_REDUCED_GRAYSCALE_8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_REDUCED_GRAYSCALE_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMREAD_UNCHANGED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMREAD_UNCHANGED</h4>
<pre>public static final&nbsp;int IMREAD_UNCHANGED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMREAD_UNCHANGED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_CHROMA_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_CHROMA_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_CHROMA_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_CHROMA_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_LUMA_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_LUMA_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_LUMA_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_LUMA_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_OPTIMIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_OPTIMIZE</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_OPTIMIZE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_OPTIMIZE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_PROGRESSIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_PROGRESSIVE</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_PROGRESSIVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_PROGRESSIVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_QUALITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_JPEG_RST_INTERVAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_JPEG_RST_INTERVAL</h4>
<pre>public static final&nbsp;int IMWRITE_JPEG_RST_INTERVAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_JPEG_RST_INTERVAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_BILEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_BILEVEL</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_BILEVEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_BILEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_COMPRESSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_COMPRESSION</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_COMPRESSION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_COMPRESSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_DEFAULT</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_FILTERED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_FILTERED</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_FILTERED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FILTERED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_FIXED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_FIXED</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_FIXED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_FIXED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_HUFFMAN_ONLY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PNG_STRATEGY_RLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PNG_STRATEGY_RLE</h4>
<pre>public static final&nbsp;int IMWRITE_PNG_STRATEGY_RLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PNG_STRATEGY_RLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_PXM_BINARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMWRITE_PXM_BINARY</h4>
<pre>public static final&nbsp;int IMWRITE_PXM_BINARY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_PXM_BINARY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IMWRITE_WEBP_QUALITY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IMWRITE_WEBP_QUALITY</h4>
<pre>public static final&nbsp;int IMWRITE_WEBP_QUALITY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.imgcodecs.Imgcodecs.IMWRITE_WEBP_QUALITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Imgcodecs--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Imgcodecs</h4>
<pre>public&nbsp;Imgcodecs()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="imdecode-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imdecode</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imdecode(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;buf,
                           int&nbsp;flags)</pre>
</li>
</ul>
<a name="imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imencode</h4>
<pre>public static&nbsp;boolean&nbsp;imencode(java.lang.String&nbsp;ext,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf)</pre>
</li>
</ul>
<a name="imencode-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfByte-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imencode</h4>
<pre>public static&nbsp;boolean&nbsp;imencode(java.lang.String&nbsp;ext,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                               <a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;buf,
                               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
</li>
</ul>
<a name="imread-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imread</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imread(java.lang.String&nbsp;filename)</pre>
</li>
</ul>
<a name="imread-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imread</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;imread(java.lang.String&nbsp;filename,
                         int&nbsp;flags)</pre>
</li>
</ul>
<a name="imreadmulti-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imreadmulti</h4>
<pre>public static&nbsp;boolean&nbsp;imreadmulti(java.lang.String&nbsp;filename,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats)</pre>
</li>
</ul>
<a name="imreadmulti-java.lang.String-java.util.List-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imreadmulti</h4>
<pre>public static&nbsp;boolean&nbsp;imreadmulti(java.lang.String&nbsp;filename,
                                  java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mats,
                                  int&nbsp;flags)</pre>
</li>
</ul>
<a name="imwrite-java.lang.String-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imwrite</h4>
<pre>public static&nbsp;boolean&nbsp;imwrite(java.lang.String&nbsp;filename,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img)</pre>
</li>
</ul>
<a name="imwrite-java.lang.String-org.opencv.core.Mat-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>imwrite</h4>
<pre>public static&nbsp;boolean&nbsp;imwrite(java.lang.String&nbsp;filename,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;img,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;params)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/imgcodecs/Imgcodecs.html" target="_top">Frames</a></li>
<li><a href="Imgcodecs.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
