<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>org.opencv.core</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../org/opencv/core/package-summary.html" target="classFrame">org.opencv.core</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="Algorithm.html" title="class in org.opencv.core" target="classFrame">Algorithm</a></li>
<li><a href="Core.html" title="class in org.opencv.core" target="classFrame">Core</a></li>
<li><a href="Core.MinMaxLocResult.html" title="class in org.opencv.core" target="classFrame">Core.MinMaxLocResult</a></li>
<li><a href="CvType.html" title="class in org.opencv.core" target="classFrame">CvType</a></li>
<li><a href="DMatch.html" title="class in org.opencv.core" target="classFrame">DMatch</a></li>
<li><a href="KeyPoint.html" title="class in org.opencv.core" target="classFrame">KeyPoint</a></li>
<li><a href="Mat.html" title="class in org.opencv.core" target="classFrame">Mat</a></li>
<li><a href="MatOfByte.html" title="class in org.opencv.core" target="classFrame">MatOfByte</a></li>
<li><a href="MatOfDMatch.html" title="class in org.opencv.core" target="classFrame">MatOfDMatch</a></li>
<li><a href="MatOfDouble.html" title="class in org.opencv.core" target="classFrame">MatOfDouble</a></li>
<li><a href="MatOfFloat.html" title="class in org.opencv.core" target="classFrame">MatOfFloat</a></li>
<li><a href="MatOfFloat4.html" title="class in org.opencv.core" target="classFrame">MatOfFloat4</a></li>
<li><a href="MatOfFloat6.html" title="class in org.opencv.core" target="classFrame">MatOfFloat6</a></li>
<li><a href="MatOfInt.html" title="class in org.opencv.core" target="classFrame">MatOfInt</a></li>
<li><a href="MatOfInt4.html" title="class in org.opencv.core" target="classFrame">MatOfInt4</a></li>
<li><a href="MatOfKeyPoint.html" title="class in org.opencv.core" target="classFrame">MatOfKeyPoint</a></li>
<li><a href="MatOfPoint.html" title="class in org.opencv.core" target="classFrame">MatOfPoint</a></li>
<li><a href="MatOfPoint2f.html" title="class in org.opencv.core" target="classFrame">MatOfPoint2f</a></li>
<li><a href="MatOfPoint3.html" title="class in org.opencv.core" target="classFrame">MatOfPoint3</a></li>
<li><a href="MatOfPoint3f.html" title="class in org.opencv.core" target="classFrame">MatOfPoint3f</a></li>
<li><a href="MatOfRect.html" title="class in org.opencv.core" target="classFrame">MatOfRect</a></li>
<li><a href="Point.html" title="class in org.opencv.core" target="classFrame">Point</a></li>
<li><a href="Point3.html" title="class in org.opencv.core" target="classFrame">Point3</a></li>
<li><a href="Range.html" title="class in org.opencv.core" target="classFrame">Range</a></li>
<li><a href="Rect.html" title="class in org.opencv.core" target="classFrame">Rect</a></li>
<li><a href="RotatedRect.html" title="class in org.opencv.core" target="classFrame">RotatedRect</a></li>
<li><a href="Scalar.html" title="class in org.opencv.core" target="classFrame">Scalar</a></li>
<li><a href="Size.html" title="class in org.opencv.core" target="classFrame">Size</a></li>
<li><a href="TermCriteria.html" title="class in org.opencv.core" target="classFrame">TermCriteria</a></li>
</ul>
<h2 title="Exceptions">Exceptions</h2>
<ul title="Exceptions">
<li><a href="CvException.html" title="class in org.opencv.core" target="classFrame">CvException</a></li>
</ul>
</div>
</body>
</html>
