<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:51 MSK 2015 -->
<title>StereoSGBM</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StereoSGBM";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoSGBM.html" target="_top">Frames</a></li>
<li><a href="StereoSGBM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.calib3d</div>
<h2 title="Class StereoSGBM" class="title">Class StereoSGBM</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">org.opencv.calib3d.StereoMatcher</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.calib3d.StereoSGBM</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">StereoSGBM</span>
extends <a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_HH">MODE_HH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_SGBM">MODE_SGBM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_SGBM_3WAY">MODE_SGBM_3WAY</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.calib3d.StereoMatcher">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.calib3d.<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SCALE">DISP_SCALE</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff,
      int&nbsp;preFilterCap,
      int&nbsp;uniquenessRatio,
      int&nbsp;speckleWindowSize,
      int&nbsp;speckleRange,
      int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getMode--">getMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getP1--">getP1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getP2--">getP2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getPreFilterCap--">getPreFilterCap</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getUniquenessRatio--">getUniquenessRatio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setMode-int-">setMode</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setP1-int-">setP1</a></span>(int&nbsp;P1)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setP2-int-">setP2</a></span>(int&nbsp;P2)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setPreFilterCap-int-">setPreFilterCap</a></span>(int&nbsp;preFilterCap)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setUniquenessRatio-int-">setUniquenessRatio</a></span>(int&nbsp;uniquenessRatio)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.calib3d.StereoMatcher">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.calib3d.<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="../../../org/opencv/calib3d/StereoMatcher.html#compute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getBlockSize--">getBlockSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getDisp12MaxDiff--">getDisp12MaxDiff</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getMinDisparity--">getMinDisparity</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getNumDisparities--">getNumDisparities</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleRange--">getSpeckleRange</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleWindowSize--">getSpeckleWindowSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setBlockSize-int-">setBlockSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setDisp12MaxDiff-int-">setDisp12MaxDiff</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setMinDisparity-int-">setMinDisparity</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setNumDisparities-int-">setNumDisparities</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleRange-int-">setSpeckleRange</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleWindowSize-int-">setSpeckleWindowSize</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MODE_HH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_HH</h4>
<pre>public static final&nbsp;int MODE_HH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_HH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_SGBM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_SGBM</h4>
<pre>public static final&nbsp;int MODE_SGBM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_SGBM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_SGBM_3WAY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MODE_SGBM_3WAY</h4>
<pre>public static final&nbsp;int MODE_SGBM_3WAY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_SGBM_3WAY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="create-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize)</pre>
</li>
</ul>
<a name="create-int-int-int-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff,
                                int&nbsp;preFilterCap,
                                int&nbsp;uniquenessRatio,
                                int&nbsp;speckleWindowSize,
                                int&nbsp;speckleRange,
                                int&nbsp;mode)</pre>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;int&nbsp;getMode()</pre>
</li>
</ul>
<a name="getP1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getP1</h4>
<pre>public&nbsp;int&nbsp;getP1()</pre>
</li>
</ul>
<a name="getP2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getP2</h4>
<pre>public&nbsp;int&nbsp;getP2()</pre>
</li>
</ul>
<a name="getPreFilterCap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreFilterCap</h4>
<pre>public&nbsp;int&nbsp;getPreFilterCap()</pre>
</li>
</ul>
<a name="getUniquenessRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniquenessRatio</h4>
<pre>public&nbsp;int&nbsp;getUniquenessRatio()</pre>
</li>
</ul>
<a name="setMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(int&nbsp;mode)</pre>
</li>
</ul>
<a name="setP1-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setP1</h4>
<pre>public&nbsp;void&nbsp;setP1(int&nbsp;P1)</pre>
</li>
</ul>
<a name="setP2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setP2</h4>
<pre>public&nbsp;void&nbsp;setP2(int&nbsp;P2)</pre>
</li>
</ul>
<a name="setPreFilterCap-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreFilterCap</h4>
<pre>public&nbsp;void&nbsp;setPreFilterCap(int&nbsp;preFilterCap)</pre>
</li>
</ul>
<a name="setUniquenessRatio-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setUniquenessRatio</h4>
<pre>public&nbsp;void&nbsp;setUniquenessRatio(int&nbsp;uniquenessRatio)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoSGBM.html" target="_top">Frames</a></li>
<li><a href="StereoSGBM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
