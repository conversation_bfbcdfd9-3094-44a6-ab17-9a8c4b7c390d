<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:50 MSK 2015 -->
<title>LoaderCallbackInterface</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LoaderCallbackInterface";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/LoaderCallbackInterface.html" target="_top">Frames</a></li>
<li><a href="LoaderCallbackInterface.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.android</div>
<h2 title="Interface LoaderCallbackInterface" class="title">Interface LoaderCallbackInterface</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../org/opencv/android/BaseLoaderCallback.html" title="class in org.opencv.android">BaseLoaderCallback</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">LoaderCallbackInterface</span></pre>
<div class="block">Interface for callback object in case of asynchronous initialization of OpenCV.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#INCOMPATIBLE_MANAGER_VERSION">INCOMPATIBLE_MANAGER_VERSION</a></span></code>
<div class="block">This version of OpenCV Manager Service is incompatible with the app.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#INIT_FAILED">INIT_FAILED</a></span></code>
<div class="block">OpenCV library initialization has failed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#INSTALL_CANCELED">INSTALL_CANCELED</a></span></code>
<div class="block">OpenCV library installation has been canceled by the user.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#MARKET_ERROR">MARKET_ERROR</a></span></code>
<div class="block">Google Play Market cannot be invoked.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#SUCCESS">SUCCESS</a></span></code>
<div class="block">OpenCV initialization finished successfully.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#onManagerConnected-int-">onManagerConnected</a></span>(int&nbsp;status)</code>
<div class="block">Callback method, called after OpenCV library initialization.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/LoaderCallbackInterface.html#onPackageInstall-int-org.opencv.android.InstallCallbackInterface-">onPackageInstall</a></span>(int&nbsp;operation,
                <a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android">InstallCallbackInterface</a>&nbsp;callback)</code>
<div class="block">Callback method, called in case the package installation is needed.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="INCOMPATIBLE_MANAGER_VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INCOMPATIBLE_MANAGER_VERSION</h4>
<pre>static final&nbsp;int INCOMPATIBLE_MANAGER_VERSION</pre>
<div class="block">This version of OpenCV Manager Service is incompatible with the app. Possibly, a service update is required.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.LoaderCallbackInterface.INCOMPATIBLE_MANAGER_VERSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INIT_FAILED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INIT_FAILED</h4>
<pre>static final&nbsp;int INIT_FAILED</pre>
<div class="block">OpenCV library initialization has failed.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.LoaderCallbackInterface.INIT_FAILED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INSTALL_CANCELED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INSTALL_CANCELED</h4>
<pre>static final&nbsp;int INSTALL_CANCELED</pre>
<div class="block">OpenCV library installation has been canceled by the user.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.LoaderCallbackInterface.INSTALL_CANCELED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MARKET_ERROR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MARKET_ERROR</h4>
<pre>static final&nbsp;int MARKET_ERROR</pre>
<div class="block">Google Play Market cannot be invoked.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.LoaderCallbackInterface.MARKET_ERROR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SUCCESS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SUCCESS</h4>
<pre>static final&nbsp;int SUCCESS</pre>
<div class="block">OpenCV initialization finished successfully.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.android.LoaderCallbackInterface.SUCCESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onManagerConnected-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onManagerConnected</h4>
<pre>void&nbsp;onManagerConnected(int&nbsp;status)</pre>
<div class="block">Callback method, called after OpenCV library initialization.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - status of initialization (see initialization status constants).</dd>
</dl>
</li>
</ul>
<a name="onPackageInstall-int-org.opencv.android.InstallCallbackInterface-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onPackageInstall</h4>
<pre>void&nbsp;onPackageInstall(int&nbsp;operation,
                      <a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android">InstallCallbackInterface</a>&nbsp;callback)</pre>
<div class="block">Callback method, called in case the package installation is needed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callback</code> - answer object with approve and cancel methods and the package description.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/JavaCameraView.JavaCameraSizeAccessor.html" title="class in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/OpenCVLoader.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/LoaderCallbackInterface.html" target="_top">Frames</a></li>
<li><a href="LoaderCallbackInterface.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
