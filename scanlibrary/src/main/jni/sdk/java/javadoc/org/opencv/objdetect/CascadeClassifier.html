<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>CascadeClassifier</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CascadeClassifier";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/CascadeClassifier.html" target="_top">Frames</a></li>
<li><a href="CascadeClassifier.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class CascadeClassifier" class="title">Class CascadeClassifier</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.CascadeClassifier</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CascadeClassifier</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#CascadeClassifier--">CascadeClassifier</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#CascadeClassifier-java.lang.String-">CascadeClassifier</a></span>(java.lang.String&nbsp;filename)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#convert-java.lang.String-java.lang.String-">convert</a></span>(java.lang.String&nbsp;oldcascade,
       java.lang.String&nbsp;newcascade)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">detectMultiScale</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                double&nbsp;scaleFactor,
                int&nbsp;minNeighbors,
                int&nbsp;flags,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">detectMultiScale2</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-org.opencv.core.Size-boolean-">detectMultiScale3</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                 <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                 <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                 <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                 double&nbsp;scaleFactor,
                 int&nbsp;minNeighbors,
                 int&nbsp;flags,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                 <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize,
                 boolean&nbsp;outputRejectLevels)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#empty--">empty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#getFeatureType--">getFeatureType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#getOriginalWindowSize--">getOriginalWindowSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#isOldFormatCascade--">isOldFormatCascade</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/CascadeClassifier.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;filename)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CascadeClassifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CascadeClassifier</h4>
<pre>public&nbsp;CascadeClassifier()</pre>
</li>
</ul>
<a name="CascadeClassifier-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CascadeClassifier</h4>
<pre>public&nbsp;CascadeClassifier(java.lang.String&nbsp;filename)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="convert-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convert</h4>
<pre>public static&nbsp;boolean&nbsp;convert(java.lang.String&nbsp;oldcascade,
                              java.lang.String&nbsp;newcascade)</pre>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects)</pre>
</li>
</ul>
<a name="detectMultiScale-org.opencv.core.Mat-org.opencv.core.MatOfRect-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                             <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                             double&nbsp;scaleFactor,
                             int&nbsp;minNeighbors,
                             int&nbsp;flags,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                             <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</pre>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections)</pre>
</li>
</ul>
<a name="detectMultiScale2-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-double-int-int-org.opencv.core.Size-org.opencv.core.Size-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale2</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale2(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;numDetections,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize)</pre>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights)</pre>
</li>
</ul>
<a name="detectMultiScale3-org.opencv.core.Mat-org.opencv.core.MatOfRect-org.opencv.core.MatOfInt-org.opencv.core.MatOfDouble-double-int-int-org.opencv.core.Size-org.opencv.core.Size-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detectMultiScale3</h4>
<pre>public&nbsp;void&nbsp;detectMultiScale3(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
                              <a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core">MatOfRect</a>&nbsp;objects,
                              <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;rejectLevels,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;levelWeights,
                              double&nbsp;scaleFactor,
                              int&nbsp;minNeighbors,
                              int&nbsp;flags,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;minSize,
                              <a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;maxSize,
                              boolean&nbsp;outputRejectLevels)</pre>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
</li>
</ul>
<a name="getFeatureType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFeatureType</h4>
<pre>public&nbsp;int&nbsp;getFeatureType()</pre>
</li>
</ul>
<a name="getOriginalWindowSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginalWindowSize</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;getOriginalWindowSize()</pre>
</li>
</ul>
<a name="isOldFormatCascade--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOldFormatCascade</h4>
<pre>public&nbsp;boolean&nbsp;isOldFormatCascade()</pre>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;boolean&nbsp;load(java.lang.String&nbsp;filename)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/CascadeClassifier.html" target="_top">Frames</a></li>
<li><a href="CascadeClassifier.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
