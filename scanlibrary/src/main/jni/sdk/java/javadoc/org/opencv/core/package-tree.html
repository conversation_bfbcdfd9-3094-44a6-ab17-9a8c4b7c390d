<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>org.opencv.core Class Hierarchy</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.core Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/package-tree.html">Prev</a></li>
<li><a href="../../../org/opencv/engine/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.opencv.core</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core"><span class="typeNameLink">Algorithm</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Core.html" title="class in org.opencv.core"><span class="typeNameLink">Core</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core"><span class="typeNameLink">Core.MinMaxLocResult</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/CvType.html" title="class in org.opencv.core"><span class="typeNameLink">CvType</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/DMatch.html" title="class in org.opencv.core"><span class="typeNameLink">DMatch</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/KeyPoint.html" title="class in org.opencv.core"><span class="typeNameLink">KeyPoint</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core"><span class="typeNameLink">Mat</span></a>
<ul>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfByte.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfByte</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfDMatch.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfDMatch</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfDouble</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfFloat.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfFloat</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfFloat4.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfFloat4</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfFloat6.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfFloat6</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfInt</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfInt4.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfInt4</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfKeyPoint.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfKeyPoint</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfPoint.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfPoint</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfPoint2f.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfPoint2f</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfPoint3.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfPoint3</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfPoint3f.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfPoint3f</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/MatOfRect.html" title="class in org.opencv.core"><span class="typeNameLink">MatOfRect</span></a></li>
</ul>
</li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Point.html" title="class in org.opencv.core"><span class="typeNameLink">Point</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Point3.html" title="class in org.opencv.core"><span class="typeNameLink">Point3</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Range.html" title="class in org.opencv.core"><span class="typeNameLink">Range</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Rect.html" title="class in org.opencv.core"><span class="typeNameLink">Rect</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/RotatedRect.html" title="class in org.opencv.core"><span class="typeNameLink">RotatedRect</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core"><span class="typeNameLink">Scalar</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/Size.html" title="class in org.opencv.core"><span class="typeNameLink">Size</span></a></li>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core"><span class="typeNameLink">TermCriteria</span></a></li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">java.lang.RuntimeException
<ul>
<li type="circle">org.opencv.core.<a href="../../../org/opencv/core/CvException.html" title="class in org.opencv.core"><span class="typeNameLink">CvException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/package-tree.html">Prev</a></li>
<li><a href="../../../org/opencv/engine/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
