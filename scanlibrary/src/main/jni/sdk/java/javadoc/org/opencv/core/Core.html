<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:51 MSK 2015 -->
<title>Core</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Core";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9,"i44":9,"i45":9,"i46":9,"i47":9,"i48":9,"i49":9,"i50":9,"i51":9,"i52":9,"i53":9,"i54":9,"i55":9,"i56":9,"i57":9,"i58":9,"i59":9,"i60":9,"i61":9,"i62":9,"i63":9,"i64":9,"i65":9,"i66":9,"i67":9,"i68":9,"i69":9,"i70":9,"i71":9,"i72":9,"i73":9,"i74":9,"i75":9,"i76":9,"i77":9,"i78":9,"i79":9,"i80":9,"i81":9,"i82":9,"i83":9,"i84":9,"i85":9,"i86":9,"i87":9,"i88":9,"i89":9,"i90":9,"i91":9,"i92":9,"i93":9,"i94":9,"i95":9,"i96":9,"i97":9,"i98":9,"i99":9,"i100":9,"i101":9,"i102":9,"i103":9,"i104":9,"i105":9,"i106":9,"i107":9,"i108":9,"i109":9,"i110":9,"i111":9,"i112":9,"i113":9,"i114":9,"i115":9,"i116":9,"i117":9,"i118":9,"i119":9,"i120":9,"i121":9,"i122":9,"i123":9,"i124":9,"i125":9,"i126":9,"i127":9,"i128":9,"i129":9,"i130":9,"i131":9,"i132":9,"i133":9,"i134":9,"i135":9,"i136":9,"i137":9,"i138":9,"i139":9,"i140":9,"i141":9,"i142":9,"i143":9,"i144":9,"i145":9,"i146":9,"i147":9,"i148":9,"i149":9,"i150":9,"i151":9,"i152":9,"i153":9,"i154":9,"i155":9,"i156":9,"i157":9,"i158":9,"i159":9,"i160":9,"i161":9,"i162":9,"i163":9,"i164":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/Core.html" target="_top">Frames</a></li>
<li><a href="Core.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.core</div>
<h2 title="Class Core" class="title">Class Core</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.core.Core</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Core</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core">Core.MinMaxLocResult</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadAlign">BadAlign</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadAlphaChannel">BadAlphaChannel</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadCallBack">BadCallBack</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadCOI">BadCOI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadDataPtr">BadDataPtr</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadDepth">BadDepth</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadImageSize">BadImageSize</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadModelOrChSeq">BadModelOrChSeq</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadNumChannel1U">BadNumChannel1U</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadNumChannels">BadNumChannels</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadOffset">BadOffset</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadOrder">BadOrder</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadOrigin">BadOrigin</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadROISize">BadROISize</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadStep">BadStep</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BadTileSize">BadTileSize</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_CONSTANT">BORDER_CONSTANT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_DEFAULT">BORDER_DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_ISOLATED">BORDER_ISOLATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_REFLECT">BORDER_REFLECT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_REFLECT_101">BORDER_REFLECT_101</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_REFLECT101">BORDER_REFLECT101</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_REPLICATE">BORDER_REPLICATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_TRANSPARENT">BORDER_TRANSPARENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#BORDER_WRAP">BORDER_WRAP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#CMP_EQ">CMP_EQ</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#CMP_GE">CMP_GE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#CMP_GT">CMP_GT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#CMP_LE">CMP_LE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#CMP_LT">CMP_LT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#CMP_NE">CMP_NE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#COVAR_COLS">COVAR_COLS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#COVAR_NORMAL">COVAR_NORMAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#COVAR_ROWS">COVAR_ROWS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#COVAR_SCALE">COVAR_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#COVAR_SCRAMBLED">COVAR_SCRAMBLED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#COVAR_USE_AVG">COVAR_USE_AVG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DCT_INVERSE">DCT_INVERSE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DCT_ROWS">DCT_ROWS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DECOMP_CHOLESKY">DECOMP_CHOLESKY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DECOMP_EIG">DECOMP_EIG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DECOMP_LU">DECOMP_LU</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DECOMP_NORMAL">DECOMP_NORMAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DECOMP_QR">DECOMP_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DECOMP_SVD">DECOMP_SVD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DFT_COMPLEX_OUTPUT">DFT_COMPLEX_OUTPUT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DFT_INVERSE">DFT_INVERSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DFT_REAL_OUTPUT">DFT_REAL_OUTPUT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DFT_ROWS">DFT_ROWS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#DFT_SCALE">DFT_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FILLED">FILLED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_COMPLEX">FONT_HERSHEY_COMPLEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_COMPLEX_SMALL">FONT_HERSHEY_COMPLEX_SMALL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_DUPLEX">FONT_HERSHEY_DUPLEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_PLAIN">FONT_HERSHEY_PLAIN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_SCRIPT_COMPLEX">FONT_HERSHEY_SCRIPT_COMPLEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_SCRIPT_SIMPLEX">FONT_HERSHEY_SCRIPT_SIMPLEX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_SIMPLEX">FONT_HERSHEY_SIMPLEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_HERSHEY_TRIPLEX">FONT_HERSHEY_TRIPLEX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#FONT_ITALIC">FONT_ITALIC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#GEMM_1_T">GEMM_1_T</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#GEMM_2_T">GEMM_2_T</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#GEMM_3_T">GEMM_3_T</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#GpuApiCallError">GpuApiCallError</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#GpuNotSupported">GpuNotSupported</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#HeaderIsNull">HeaderIsNull</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#KMEANS_PP_CENTERS">KMEANS_PP_CENTERS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#KMEANS_RANDOM_CENTERS">KMEANS_RANDOM_CENTERS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#KMEANS_USE_INITIAL_LABELS">KMEANS_USE_INITIAL_LABELS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#LINE_4">LINE_4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#LINE_8">LINE_8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#LINE_AA">LINE_AA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#MaskIsTiled">MaskIsTiled</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NATIVE_LIBRARY_NAME">NATIVE_LIBRARY_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_HAMMING">NORM_HAMMING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_HAMMING2">NORM_HAMMING2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_INF">NORM_INF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_L1">NORM_L1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_L2">NORM_L2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_L2SQR">NORM_L2SQR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_MINMAX">NORM_MINMAX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_RELATIVE">NORM_RELATIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#NORM_TYPE_MASK">NORM_TYPE_MASK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#OpenCLApiCallError">OpenCLApiCallError</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#OpenCLDoubleNotSupported">OpenCLDoubleNotSupported</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#OpenCLInitError">OpenCLInitError</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#OpenCLNoAMDBlasFft">OpenCLNoAMDBlasFft</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#OpenGlApiCallError">OpenGlApiCallError</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#OpenGlNotSupported">OpenGlNotSupported</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#REDUCE_AVG">REDUCE_AVG</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#REDUCE_MAX">REDUCE_MAX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#REDUCE_MIN">REDUCE_MIN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#REDUCE_SUM">REDUCE_SUM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SORT_ASCENDING">SORT_ASCENDING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SORT_DESCENDING">SORT_DESCENDING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SORT_EVERY_COLUMN">SORT_EVERY_COLUMN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SORT_EVERY_ROW">SORT_EVERY_ROW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsAssert">StsAssert</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsAutoTrace">StsAutoTrace</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBackTrace">StsBackTrace</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadArg">StsBadArg</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadFlag">StsBadFlag</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadFunc">StsBadFunc</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadMask">StsBadMask</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadMemBlock">StsBadMemBlock</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadPoint">StsBadPoint</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsBadSize">StsBadSize</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsDivByZero">StsDivByZero</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsError">StsError</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsFilterOffsetErr">StsFilterOffsetErr</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsFilterStructContentErr">StsFilterStructContentErr</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsInplaceNotSupported">StsInplaceNotSupported</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsInternal">StsInternal</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsKernelStructContentErr">StsKernelStructContentErr</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsNoConv">StsNoConv</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsNoMem">StsNoMem</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsNotImplemented">StsNotImplemented</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsNullPtr">StsNullPtr</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsObjectNotFound">StsObjectNotFound</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsOk">StsOk</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsOutOfRange">StsOutOfRange</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsParseError">StsParseError</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsUnmatchedFormats">StsUnmatchedFormats</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsUnmatchedSizes">StsUnmatchedSizes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsUnsupportedFormat">StsUnsupportedFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#StsVecLengthErr">StsVecLengthErr</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SVD_FULL_UV">SVD_FULL_UV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SVD_MODIFY_A">SVD_MODIFY_A</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SVD_NO_UV">SVD_NO_UV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#VERSION">VERSION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#VERSION_MAJOR">VERSION_MAJOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#VERSION_MINOR">VERSION_MINOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#VERSION_REVISION">VERSION_REVISION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#VERSION_STATUS">VERSION_STATUS</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#Core--">Core</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#absdiff-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">absdiff</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#absdiff-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">absdiff</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#add-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">add</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#add-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">add</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#add-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">add</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
   int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#add-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">add</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#add-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-">add</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#add-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-int-">add</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
   int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#addWeighted-org.opencv.core.Mat-double-org.opencv.core.Mat-double-double-org.opencv.core.Mat-">addWeighted</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
           double&nbsp;alpha,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
           double&nbsp;beta,
           double&nbsp;gamma,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#addWeighted-org.opencv.core.Mat-double-org.opencv.core.Mat-double-double-org.opencv.core.Mat-int-">addWeighted</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
           double&nbsp;alpha,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
           double&nbsp;beta,
           double&nbsp;gamma,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#batchDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-">batchDistance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist,
             int&nbsp;dtype,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nidx)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#batchDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-int-int-">batchDistance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist,
             int&nbsp;dtype,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nidx,
             int&nbsp;normType,
             int&nbsp;K)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#batchDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-int-int-org.opencv.core.Mat-int-boolean-">batchDistance</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist,
             int&nbsp;dtype,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nidx,
             int&nbsp;normType,
             int&nbsp;K,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
             int&nbsp;update,
             boolean&nbsp;crosscheck)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_and-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_and</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_and-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_and</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_not-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_not</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_not-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_not</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_or-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_or</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_or-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_or</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_xor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_xor</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#bitwise_xor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">bitwise_xor</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#borderInterpolate-int-int-int-">borderInterpolate</a></span>(int&nbsp;p,
                 int&nbsp;len,
                 int&nbsp;borderType)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#calcCovarMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">calcCovarMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covar,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
               int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#calcCovarMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">calcCovarMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covar,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
               int&nbsp;flags,
               int&nbsp;ctype)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#cartToPolar-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">cartToPolar</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#cartToPolar-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">cartToPolar</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
           boolean&nbsp;angleInDegrees)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#checkRange-org.opencv.core.Mat-">checkRange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#checkRange-org.opencv.core.Mat-boolean-double-double-">checkRange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
          boolean&nbsp;quiet,
          double&nbsp;minVal,
          double&nbsp;maxVal)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#compare-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">compare</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
       int&nbsp;cmpop)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#compare-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-int-">compare</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
       int&nbsp;cmpop)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#completeSymm-org.opencv.core.Mat-">completeSymm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#completeSymm-org.opencv.core.Mat-boolean-">completeSymm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx,
            boolean&nbsp;lowerToUpper)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#convertScaleAbs-org.opencv.core.Mat-org.opencv.core.Mat-">convertScaleAbs</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#convertScaleAbs-org.opencv.core.Mat-org.opencv.core.Mat-double-double-">convertScaleAbs</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
               double&nbsp;alpha,
               double&nbsp;beta)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#copyMakeBorder-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-int-int-">copyMakeBorder</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
              int&nbsp;top,
              int&nbsp;bottom,
              int&nbsp;left,
              int&nbsp;right,
              int&nbsp;borderType)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#copyMakeBorder-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-int-int-org.opencv.core.Scalar-">copyMakeBorder</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
              int&nbsp;top,
              int&nbsp;bottom,
              int&nbsp;left,
              int&nbsp;right,
              int&nbsp;borderType,
              <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#countNonZero-org.opencv.core.Mat-">countNonZero</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#cubeRoot-float-">cubeRoot</a></span>(float&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#dct-org.opencv.core.Mat-org.opencv.core.Mat-">dct</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#dct-org.opencv.core.Mat-org.opencv.core.Mat-int-">dct</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
   int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#determinant-org.opencv.core.Mat-">determinant</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#dft-org.opencv.core.Mat-org.opencv.core.Mat-">dft</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#dft-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">dft</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
   int&nbsp;flags,
   int&nbsp;nonzeroRows)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-double-org.opencv.core.Mat-org.opencv.core.Mat-">divide</a></span>(double&nbsp;scale,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-double-org.opencv.core.Mat-org.opencv.core.Mat-int-">divide</a></span>(double&nbsp;scale,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">divide</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">divide</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      double&nbsp;scale)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">divide</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      double&nbsp;scale,
      int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">divide</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
      <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-">divide</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
      <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      double&nbsp;scale)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#divide-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-int-">divide</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
      <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      double&nbsp;scale,
      int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#eigen-org.opencv.core.Mat-org.opencv.core.Mat-">eigen</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvalues)</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#eigen-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">eigen</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvalues,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#exp-org.opencv.core.Mat-org.opencv.core.Mat-">exp</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#extractChannel-org.opencv.core.Mat-org.opencv.core.Mat-int-">extractChannel</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
              int&nbsp;coi)</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#fastAtan2-float-float-">fastAtan2</a></span>(float&nbsp;y,
         float&nbsp;x)</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#findNonZero-org.opencv.core.Mat-org.opencv.core.Mat-">findNonZero</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#flip-org.opencv.core.Mat-org.opencv.core.Mat-int-">flip</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
    int&nbsp;flipCode)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#gemm-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Mat-double-org.opencv.core.Mat-">gemm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
    double&nbsp;alpha,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src3,
    double&nbsp;beta,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#gemm-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Mat-double-org.opencv.core.Mat-int-">gemm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
    double&nbsp;alpha,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src3,
    double&nbsp;beta,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
    int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getBuildInformation--">getBuildInformation</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getCPUTickCount--">getCPUTickCount</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getNumberOfCPUs--">getNumberOfCPUs</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getNumThreads--">getNumThreads</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getOptimalDFTSize-int-">getOptimalDFTSize</a></span>(int&nbsp;vecsize)</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getThreadNum--">getThreadNum</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getTickCount--">getTickCount</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#getTickFrequency--">getTickFrequency</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#hconcat-java.util.List-org.opencv.core.Mat-">hconcat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#idct-org.opencv.core.Mat-org.opencv.core.Mat-">idct</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#idct-org.opencv.core.Mat-org.opencv.core.Mat-int-">idct</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
    int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#idft-org.opencv.core.Mat-org.opencv.core.Mat-">idft</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#idft-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">idft</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
    int&nbsp;flags,
    int&nbsp;nonzeroRows)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#inRange-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.Mat-">inRange</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;lowerb,
       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;upperb,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#insertChannel-org.opencv.core.Mat-org.opencv.core.Mat-int-">insertChannel</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             int&nbsp;coi)</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#invert-org.opencv.core.Mat-org.opencv.core.Mat-">invert</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#invert-org.opencv.core.Mat-org.opencv.core.Mat-int-">invert</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#kmeans-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.TermCriteria-int-int-">kmeans</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
      int&nbsp;K,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bestLabels,
      <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
      int&nbsp;attempts,
      int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#kmeans-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.TermCriteria-int-int-org.opencv.core.Mat-">kmeans</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
      int&nbsp;K,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bestLabels,
      <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
      int&nbsp;attempts,
      int&nbsp;flags,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;centers)</code>&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#log-org.opencv.core.Mat-org.opencv.core.Mat-">log</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#LUT-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">LUT</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lut,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#magnitude-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">magnitude</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude)</code>&nbsp;</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#Mahalanobis-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">Mahalanobis</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;v1,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;v2,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;icovar)</code>&nbsp;</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#max-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">max</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#max-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">max</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mean-org.opencv.core.Mat-">mean</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</code>&nbsp;</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mean-org.opencv.core.Mat-org.opencv.core.Mat-">mean</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#meanStdDev-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfDouble-">meanStdDev</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
          <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;mean,
          <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;stddev)</code>&nbsp;</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#meanStdDev-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfDouble-org.opencv.core.Mat-">meanStdDev</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
          <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;mean,
          <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;stddev,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#merge-java.util.List-org.opencv.core.Mat-">merge</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mv,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#min-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">min</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#min-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">min</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
   <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core">Core.MinMaxLocResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#minMaxLoc-org.opencv.core.Mat-">minMaxLoc</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</code>&nbsp;</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core">Core.MinMaxLocResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#minMaxLoc-org.opencv.core.Mat-org.opencv.core.Mat-">minMaxLoc</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mixChannels-java.util.List-java.util.List-org.opencv.core.MatOfInt-">mixChannels</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
           java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;dst,
           <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;fromTo)</code>&nbsp;</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mulSpectrums-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">mulSpectrums</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;b,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;c,
            int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mulSpectrums-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-boolean-">mulSpectrums</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;b,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;c,
            int&nbsp;flags,
            boolean&nbsp;conjB)</code>&nbsp;</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#multiply-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">multiply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#multiply-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">multiply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        double&nbsp;scale)</code>&nbsp;</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#multiply-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">multiply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        double&nbsp;scale,
        int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#multiply-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">multiply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#multiply-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-">multiply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        double&nbsp;scale)</code>&nbsp;</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#multiply-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-int-">multiply</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        double&nbsp;scale,
        int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mulTransposed-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">mulTransposed</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             boolean&nbsp;aTa)</code>&nbsp;</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mulTransposed-org.opencv.core.Mat-org.opencv.core.Mat-boolean-org.opencv.core.Mat-double-">mulTransposed</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             boolean&nbsp;aTa,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;delta,
             double&nbsp;scale)</code>&nbsp;</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#mulTransposed-org.opencv.core.Mat-org.opencv.core.Mat-boolean-org.opencv.core.Mat-double-int-">mulTransposed</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
             boolean&nbsp;aTa,
             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;delta,
             double&nbsp;scale,
             int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#norm-org.opencv.core.Mat-">norm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1)</code>&nbsp;</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#norm-org.opencv.core.Mat-int-">norm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    int&nbsp;normType)</code>&nbsp;</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#norm-org.opencv.core.Mat-int-org.opencv.core.Mat-">norm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    int&nbsp;normType,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#norm-org.opencv.core.Mat-org.opencv.core.Mat-">norm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2)</code>&nbsp;</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#norm-org.opencv.core.Mat-org.opencv.core.Mat-int-">norm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
    int&nbsp;normType)</code>&nbsp;</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#norm-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-">norm</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
    int&nbsp;normType,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#normalize-org.opencv.core.Mat-org.opencv.core.Mat-">normalize</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#normalize-org.opencv.core.Mat-org.opencv.core.Mat-double-double-int-">normalize</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
         double&nbsp;alpha,
         double&nbsp;beta,
         int&nbsp;norm_type)</code>&nbsp;</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#normalize-org.opencv.core.Mat-org.opencv.core.Mat-double-double-int-int-">normalize</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
         double&nbsp;alpha,
         double&nbsp;beta,
         int&nbsp;norm_type,
         int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#normalize-org.opencv.core.Mat-org.opencv.core.Mat-double-double-int-int-org.opencv.core.Mat-">normalize</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
         double&nbsp;alpha,
         double&nbsp;beta,
         int&nbsp;norm_type,
         int&nbsp;dtype,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#patchNaNs-org.opencv.core.Mat-">patchNaNs</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a)</code>&nbsp;</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#patchNaNs-org.opencv.core.Mat-double-">patchNaNs</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
         double&nbsp;val)</code>&nbsp;</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#PCABackProject-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">PCABackProject</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</code>&nbsp;</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#PCACompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">PCACompute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors)</code>&nbsp;</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#PCACompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">PCACompute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
          double&nbsp;retainedVariance)</code>&nbsp;</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#PCACompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">PCACompute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
          int&nbsp;maxComponents)</code>&nbsp;</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#PCAProject-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">PCAProject</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</code>&nbsp;</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#perspectiveTransform-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">perspectiveTransform</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#phase-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">phase</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle)</code>&nbsp;</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#phase-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">phase</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
     boolean&nbsp;angleInDegrees)</code>&nbsp;</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#polarToCart-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">polarToCart</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y)</code>&nbsp;</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#polarToCart-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">polarToCart</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
           boolean&nbsp;angleInDegrees)</code>&nbsp;</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#pow-org.opencv.core.Mat-double-org.opencv.core.Mat-">pow</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
   double&nbsp;power,
   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#PSNR-org.opencv.core.Mat-org.opencv.core.Mat-">PSNR</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2)</code>&nbsp;</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#randn-org.opencv.core.Mat-double-double-">randn</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
     double&nbsp;mean,
     double&nbsp;stddev)</code>&nbsp;</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#randShuffle-org.opencv.core.Mat-">randShuffle</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#randShuffle-org.opencv.core.Mat-double-">randShuffle</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
           double&nbsp;iterFactor)</code>&nbsp;</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#randu-org.opencv.core.Mat-double-double-">randu</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
     double&nbsp;low,
     double&nbsp;high)</code>&nbsp;</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#reduce-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">reduce</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      int&nbsp;dim,
      int&nbsp;rtype)</code>&nbsp;</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#reduce-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-">reduce</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
      int&nbsp;dim,
      int&nbsp;rtype,
      int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#repeat-org.opencv.core.Mat-int-int-org.opencv.core.Mat-">repeat</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
      int&nbsp;ny,
      int&nbsp;nx,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#scaleAdd-org.opencv.core.Mat-double-org.opencv.core.Mat-org.opencv.core.Mat-">scaleAdd</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        double&nbsp;alpha,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#setErrorVerbosity-boolean-">setErrorVerbosity</a></span>(boolean&nbsp;verbose)</code>&nbsp;</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#setIdentity-org.opencv.core.Mat-">setIdentity</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</code>&nbsp;</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#setIdentity-org.opencv.core.Mat-org.opencv.core.Scalar-">setIdentity</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx,
           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</code>&nbsp;</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#setNumThreads-int-">setNumThreads</a></span>(int&nbsp;nthreads)</code>&nbsp;</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#solve-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">solve</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#solve-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">solve</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
     <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
     int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#solveCubic-org.opencv.core.Mat-org.opencv.core.Mat-">solveCubic</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;coeffs,
          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;roots)</code>&nbsp;</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#solvePoly-org.opencv.core.Mat-org.opencv.core.Mat-">solvePoly</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;coeffs,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;roots)</code>&nbsp;</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#solvePoly-org.opencv.core.Mat-org.opencv.core.Mat-int-">solvePoly</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;coeffs,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;roots,
         int&nbsp;maxIters)</code>&nbsp;</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#sort-org.opencv.core.Mat-org.opencv.core.Mat-int-">sort</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
    int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#sortIdx-org.opencv.core.Mat-org.opencv.core.Mat-int-">sortIdx</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
       int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#split-org.opencv.core.Mat-java.util.List-">split</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
     java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mv)</code>&nbsp;</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#sqrt-org.opencv.core.Mat-org.opencv.core.Mat-">sqrt</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#subtract-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">subtract</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#subtract-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">subtract</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#subtract-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">subtract</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
        int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#subtract-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">subtract</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#subtract-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-">subtract</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</code>&nbsp;</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#subtract-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-int-">subtract</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
        <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
        int&nbsp;dtype)</code>&nbsp;</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#sumElems-org.opencv.core.Mat-">sumElems</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</code>&nbsp;</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SVBackSubst-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">SVBackSubst</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;w,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;u,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vt,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rhs,
           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SVDecomp-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">SVDecomp</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;w,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;u,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vt)</code>&nbsp;</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#SVDecomp-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">SVDecomp</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;w,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;u,
        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vt,
        int&nbsp;flags)</code>&nbsp;</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#trace-org.opencv.core.Mat-">trace</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</code>&nbsp;</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#transform-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">transform</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code>&nbsp;</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#transpose-org.opencv.core.Mat-org.opencv.core.Mat-">transpose</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/core/Core.html#vconcat-java.util.List-org.opencv.core.Mat-">vconcat</a></span>(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BadAlign">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadAlign</h4>
<pre>public static final&nbsp;int BadAlign</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadAlign">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadAlphaChannel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadAlphaChannel</h4>
<pre>public static final&nbsp;int BadAlphaChannel</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadAlphaChannel">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadCallBack">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadCallBack</h4>
<pre>public static final&nbsp;int BadCallBack</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadCallBack">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadCOI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadCOI</h4>
<pre>public static final&nbsp;int BadCOI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadCOI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadDataPtr">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadDataPtr</h4>
<pre>public static final&nbsp;int BadDataPtr</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadDataPtr">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadDepth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadDepth</h4>
<pre>public static final&nbsp;int BadDepth</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadDepth">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadImageSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadImageSize</h4>
<pre>public static final&nbsp;int BadImageSize</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadImageSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadModelOrChSeq">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadModelOrChSeq</h4>
<pre>public static final&nbsp;int BadModelOrChSeq</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadModelOrChSeq">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadNumChannel1U">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadNumChannel1U</h4>
<pre>public static final&nbsp;int BadNumChannel1U</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadNumChannel1U">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadNumChannels">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadNumChannels</h4>
<pre>public static final&nbsp;int BadNumChannels</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadNumChannels">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadOffset">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadOffset</h4>
<pre>public static final&nbsp;int BadOffset</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadOffset">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadOrder">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadOrder</h4>
<pre>public static final&nbsp;int BadOrder</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadOrder">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadOrigin">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadOrigin</h4>
<pre>public static final&nbsp;int BadOrigin</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadOrigin">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadROISize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadROISize</h4>
<pre>public static final&nbsp;int BadROISize</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadROISize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadStep">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadStep</h4>
<pre>public static final&nbsp;int BadStep</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadStep">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BadTileSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BadTileSize</h4>
<pre>public static final&nbsp;int BadTileSize</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BadTileSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_CONSTANT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_CONSTANT</h4>
<pre>public static final&nbsp;int BORDER_CONSTANT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_CONSTANT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_DEFAULT</h4>
<pre>public static final&nbsp;int BORDER_DEFAULT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_DEFAULT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_ISOLATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_ISOLATED</h4>
<pre>public static final&nbsp;int BORDER_ISOLATED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_ISOLATED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_REFLECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_REFLECT</h4>
<pre>public static final&nbsp;int BORDER_REFLECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_REFLECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_REFLECT_101">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_REFLECT_101</h4>
<pre>public static final&nbsp;int BORDER_REFLECT_101</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_REFLECT_101">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_REFLECT101">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_REFLECT101</h4>
<pre>public static final&nbsp;int BORDER_REFLECT101</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_REFLECT101">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_REPLICATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_REPLICATE</h4>
<pre>public static final&nbsp;int BORDER_REPLICATE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_REPLICATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_TRANSPARENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_TRANSPARENT</h4>
<pre>public static final&nbsp;int BORDER_TRANSPARENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_TRANSPARENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BORDER_WRAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BORDER_WRAP</h4>
<pre>public static final&nbsp;int BORDER_WRAP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.BORDER_WRAP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CMP_EQ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CMP_EQ</h4>
<pre>public static final&nbsp;int CMP_EQ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.CMP_EQ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CMP_GE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CMP_GE</h4>
<pre>public static final&nbsp;int CMP_GE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.CMP_GE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CMP_GT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CMP_GT</h4>
<pre>public static final&nbsp;int CMP_GT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.CMP_GT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CMP_LE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CMP_LE</h4>
<pre>public static final&nbsp;int CMP_LE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.CMP_LE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CMP_LT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CMP_LT</h4>
<pre>public static final&nbsp;int CMP_LT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.CMP_LT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CMP_NE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CMP_NE</h4>
<pre>public static final&nbsp;int CMP_NE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.CMP_NE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COVAR_COLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COVAR_COLS</h4>
<pre>public static final&nbsp;int COVAR_COLS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.COVAR_COLS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COVAR_NORMAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COVAR_NORMAL</h4>
<pre>public static final&nbsp;int COVAR_NORMAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.COVAR_NORMAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COVAR_ROWS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COVAR_ROWS</h4>
<pre>public static final&nbsp;int COVAR_ROWS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.COVAR_ROWS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COVAR_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COVAR_SCALE</h4>
<pre>public static final&nbsp;int COVAR_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.COVAR_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COVAR_SCRAMBLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COVAR_SCRAMBLED</h4>
<pre>public static final&nbsp;int COVAR_SCRAMBLED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.COVAR_SCRAMBLED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COVAR_USE_AVG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COVAR_USE_AVG</h4>
<pre>public static final&nbsp;int COVAR_USE_AVG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.COVAR_USE_AVG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DCT_INVERSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DCT_INVERSE</h4>
<pre>public static final&nbsp;int DCT_INVERSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DCT_INVERSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DCT_ROWS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DCT_ROWS</h4>
<pre>public static final&nbsp;int DCT_ROWS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DCT_ROWS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECOMP_CHOLESKY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECOMP_CHOLESKY</h4>
<pre>public static final&nbsp;int DECOMP_CHOLESKY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DECOMP_CHOLESKY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECOMP_EIG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECOMP_EIG</h4>
<pre>public static final&nbsp;int DECOMP_EIG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DECOMP_EIG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECOMP_LU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECOMP_LU</h4>
<pre>public static final&nbsp;int DECOMP_LU</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DECOMP_LU">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECOMP_NORMAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECOMP_NORMAL</h4>
<pre>public static final&nbsp;int DECOMP_NORMAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DECOMP_NORMAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECOMP_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECOMP_QR</h4>
<pre>public static final&nbsp;int DECOMP_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DECOMP_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECOMP_SVD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECOMP_SVD</h4>
<pre>public static final&nbsp;int DECOMP_SVD</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DECOMP_SVD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DFT_COMPLEX_OUTPUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DFT_COMPLEX_OUTPUT</h4>
<pre>public static final&nbsp;int DFT_COMPLEX_OUTPUT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DFT_COMPLEX_OUTPUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DFT_INVERSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DFT_INVERSE</h4>
<pre>public static final&nbsp;int DFT_INVERSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DFT_INVERSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DFT_REAL_OUTPUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DFT_REAL_OUTPUT</h4>
<pre>public static final&nbsp;int DFT_REAL_OUTPUT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DFT_REAL_OUTPUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DFT_ROWS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DFT_ROWS</h4>
<pre>public static final&nbsp;int DFT_ROWS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DFT_ROWS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DFT_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DFT_SCALE</h4>
<pre>public static final&nbsp;int DFT_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.DFT_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FILLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FILLED</h4>
<pre>public static final&nbsp;int FILLED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FILLED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_COMPLEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_COMPLEX</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_COMPLEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_COMPLEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_COMPLEX_SMALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_COMPLEX_SMALL</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_COMPLEX_SMALL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_COMPLEX_SMALL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_DUPLEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_DUPLEX</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_DUPLEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_DUPLEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_PLAIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_PLAIN</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_PLAIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_PLAIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_SCRIPT_COMPLEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_SCRIPT_COMPLEX</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_SCRIPT_COMPLEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_SCRIPT_COMPLEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_SCRIPT_SIMPLEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_SCRIPT_SIMPLEX</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_SCRIPT_SIMPLEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_SCRIPT_SIMPLEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_SIMPLEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_SIMPLEX</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_SIMPLEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_SIMPLEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_HERSHEY_TRIPLEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_HERSHEY_TRIPLEX</h4>
<pre>public static final&nbsp;int FONT_HERSHEY_TRIPLEX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_HERSHEY_TRIPLEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FONT_ITALIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FONT_ITALIC</h4>
<pre>public static final&nbsp;int FONT_ITALIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.FONT_ITALIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GEMM_1_T">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GEMM_1_T</h4>
<pre>public static final&nbsp;int GEMM_1_T</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.GEMM_1_T">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GEMM_2_T">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GEMM_2_T</h4>
<pre>public static final&nbsp;int GEMM_2_T</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.GEMM_2_T">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GEMM_3_T">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GEMM_3_T</h4>
<pre>public static final&nbsp;int GEMM_3_T</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.GEMM_3_T">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GpuApiCallError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GpuApiCallError</h4>
<pre>public static final&nbsp;int GpuApiCallError</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.GpuApiCallError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GpuNotSupported">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GpuNotSupported</h4>
<pre>public static final&nbsp;int GpuNotSupported</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.GpuNotSupported">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HeaderIsNull">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HeaderIsNull</h4>
<pre>public static final&nbsp;int HeaderIsNull</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.HeaderIsNull">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="KMEANS_PP_CENTERS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KMEANS_PP_CENTERS</h4>
<pre>public static final&nbsp;int KMEANS_PP_CENTERS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.KMEANS_PP_CENTERS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="KMEANS_RANDOM_CENTERS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KMEANS_RANDOM_CENTERS</h4>
<pre>public static final&nbsp;int KMEANS_RANDOM_CENTERS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.KMEANS_RANDOM_CENTERS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="KMEANS_USE_INITIAL_LABELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>KMEANS_USE_INITIAL_LABELS</h4>
<pre>public static final&nbsp;int KMEANS_USE_INITIAL_LABELS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.KMEANS_USE_INITIAL_LABELS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LINE_4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINE_4</h4>
<pre>public static final&nbsp;int LINE_4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.LINE_4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LINE_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINE_8</h4>
<pre>public static final&nbsp;int LINE_8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.LINE_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LINE_AA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINE_AA</h4>
<pre>public static final&nbsp;int LINE_AA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.LINE_AA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MaskIsTiled">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MaskIsTiled</h4>
<pre>public static final&nbsp;int MaskIsTiled</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.MaskIsTiled">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NATIVE_LIBRARY_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NATIVE_LIBRARY_NAME</h4>
<pre>public static final&nbsp;java.lang.String NATIVE_LIBRARY_NAME</pre>
</li>
</ul>
<a name="NORM_HAMMING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_HAMMING</h4>
<pre>public static final&nbsp;int NORM_HAMMING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_HAMMING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_HAMMING2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_HAMMING2</h4>
<pre>public static final&nbsp;int NORM_HAMMING2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_HAMMING2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_INF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_INF</h4>
<pre>public static final&nbsp;int NORM_INF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_INF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_L1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_L1</h4>
<pre>public static final&nbsp;int NORM_L1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_L1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_L2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_L2</h4>
<pre>public static final&nbsp;int NORM_L2</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_L2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_L2SQR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_L2SQR</h4>
<pre>public static final&nbsp;int NORM_L2SQR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_L2SQR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_MINMAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_MINMAX</h4>
<pre>public static final&nbsp;int NORM_MINMAX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_MINMAX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_RELATIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_RELATIVE</h4>
<pre>public static final&nbsp;int NORM_RELATIVE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_RELATIVE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="NORM_TYPE_MASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORM_TYPE_MASK</h4>
<pre>public static final&nbsp;int NORM_TYPE_MASK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.NORM_TYPE_MASK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpenCLApiCallError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenCLApiCallError</h4>
<pre>public static final&nbsp;int OpenCLApiCallError</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.OpenCLApiCallError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpenCLDoubleNotSupported">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenCLDoubleNotSupported</h4>
<pre>public static final&nbsp;int OpenCLDoubleNotSupported</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.OpenCLDoubleNotSupported">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpenCLInitError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenCLInitError</h4>
<pre>public static final&nbsp;int OpenCLInitError</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.OpenCLInitError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpenCLNoAMDBlasFft">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenCLNoAMDBlasFft</h4>
<pre>public static final&nbsp;int OpenCLNoAMDBlasFft</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.OpenCLNoAMDBlasFft">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpenGlApiCallError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenGlApiCallError</h4>
<pre>public static final&nbsp;int OpenGlApiCallError</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.OpenGlApiCallError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OpenGlNotSupported">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenGlNotSupported</h4>
<pre>public static final&nbsp;int OpenGlNotSupported</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.OpenGlNotSupported">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REDUCE_AVG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REDUCE_AVG</h4>
<pre>public static final&nbsp;int REDUCE_AVG</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.REDUCE_AVG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REDUCE_MAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REDUCE_MAX</h4>
<pre>public static final&nbsp;int REDUCE_MAX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.REDUCE_MAX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REDUCE_MIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REDUCE_MIN</h4>
<pre>public static final&nbsp;int REDUCE_MIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.REDUCE_MIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="REDUCE_SUM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REDUCE_SUM</h4>
<pre>public static final&nbsp;int REDUCE_SUM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.REDUCE_SUM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SORT_ASCENDING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_ASCENDING</h4>
<pre>public static final&nbsp;int SORT_ASCENDING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SORT_ASCENDING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SORT_DESCENDING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_DESCENDING</h4>
<pre>public static final&nbsp;int SORT_DESCENDING</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SORT_DESCENDING">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SORT_EVERY_COLUMN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_EVERY_COLUMN</h4>
<pre>public static final&nbsp;int SORT_EVERY_COLUMN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SORT_EVERY_COLUMN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SORT_EVERY_ROW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_EVERY_ROW</h4>
<pre>public static final&nbsp;int SORT_EVERY_ROW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SORT_EVERY_ROW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsAssert">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsAssert</h4>
<pre>public static final&nbsp;int StsAssert</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsAssert">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsAutoTrace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsAutoTrace</h4>
<pre>public static final&nbsp;int StsAutoTrace</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsAutoTrace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBackTrace">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBackTrace</h4>
<pre>public static final&nbsp;int StsBackTrace</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBackTrace">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadArg">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadArg</h4>
<pre>public static final&nbsp;int StsBadArg</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadArg">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadFlag">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadFlag</h4>
<pre>public static final&nbsp;int StsBadFlag</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadFlag">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadFunc">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadFunc</h4>
<pre>public static final&nbsp;int StsBadFunc</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadFunc">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadMask">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadMask</h4>
<pre>public static final&nbsp;int StsBadMask</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadMask">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadMemBlock">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadMemBlock</h4>
<pre>public static final&nbsp;int StsBadMemBlock</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadMemBlock">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadPoint">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadPoint</h4>
<pre>public static final&nbsp;int StsBadPoint</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadPoint">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsBadSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsBadSize</h4>
<pre>public static final&nbsp;int StsBadSize</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsBadSize">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsDivByZero">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsDivByZero</h4>
<pre>public static final&nbsp;int StsDivByZero</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsDivByZero">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsError</h4>
<pre>public static final&nbsp;int StsError</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsFilterOffsetErr">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsFilterOffsetErr</h4>
<pre>public static final&nbsp;int StsFilterOffsetErr</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsFilterOffsetErr">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsFilterStructContentErr">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsFilterStructContentErr</h4>
<pre>public static final&nbsp;int StsFilterStructContentErr</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsFilterStructContentErr">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsInplaceNotSupported">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsInplaceNotSupported</h4>
<pre>public static final&nbsp;int StsInplaceNotSupported</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsInplaceNotSupported">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsInternal">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsInternal</h4>
<pre>public static final&nbsp;int StsInternal</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsInternal">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsKernelStructContentErr">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsKernelStructContentErr</h4>
<pre>public static final&nbsp;int StsKernelStructContentErr</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsKernelStructContentErr">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsNoConv">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsNoConv</h4>
<pre>public static final&nbsp;int StsNoConv</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsNoConv">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsNoMem">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsNoMem</h4>
<pre>public static final&nbsp;int StsNoMem</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsNoMem">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsNotImplemented">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsNotImplemented</h4>
<pre>public static final&nbsp;int StsNotImplemented</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsNotImplemented">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsNullPtr">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsNullPtr</h4>
<pre>public static final&nbsp;int StsNullPtr</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsNullPtr">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsObjectNotFound">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsObjectNotFound</h4>
<pre>public static final&nbsp;int StsObjectNotFound</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsObjectNotFound">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsOk">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsOk</h4>
<pre>public static final&nbsp;int StsOk</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsOk">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsOutOfRange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsOutOfRange</h4>
<pre>public static final&nbsp;int StsOutOfRange</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsOutOfRange">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsParseError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsParseError</h4>
<pre>public static final&nbsp;int StsParseError</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsParseError">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsUnmatchedFormats">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsUnmatchedFormats</h4>
<pre>public static final&nbsp;int StsUnmatchedFormats</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsUnmatchedFormats">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsUnmatchedSizes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsUnmatchedSizes</h4>
<pre>public static final&nbsp;int StsUnmatchedSizes</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsUnmatchedSizes">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsUnsupportedFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsUnsupportedFormat</h4>
<pre>public static final&nbsp;int StsUnsupportedFormat</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsUnsupportedFormat">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="StsVecLengthErr">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>StsVecLengthErr</h4>
<pre>public static final&nbsp;int StsVecLengthErr</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.StsVecLengthErr">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SVD_FULL_UV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVD_FULL_UV</h4>
<pre>public static final&nbsp;int SVD_FULL_UV</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SVD_FULL_UV">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SVD_MODIFY_A">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVD_MODIFY_A</h4>
<pre>public static final&nbsp;int SVD_MODIFY_A</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SVD_MODIFY_A">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SVD_NO_UV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVD_NO_UV</h4>
<pre>public static final&nbsp;int SVD_NO_UV</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.core.Core.SVD_NO_UV">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VERSION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION</h4>
<pre>public static final&nbsp;java.lang.String VERSION</pre>
</li>
</ul>
<a name="VERSION_MAJOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_MAJOR</h4>
<pre>public static final&nbsp;int VERSION_MAJOR</pre>
</li>
</ul>
<a name="VERSION_MINOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_MINOR</h4>
<pre>public static final&nbsp;int VERSION_MINOR</pre>
</li>
</ul>
<a name="VERSION_REVISION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_REVISION</h4>
<pre>public static final&nbsp;int VERSION_REVISION</pre>
</li>
</ul>
<a name="VERSION_STATUS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VERSION_STATUS</h4>
<pre>public static final&nbsp;java.lang.String VERSION_STATUS</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Core--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Core</h4>
<pre>public&nbsp;Core()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="absdiff-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>absdiff</h4>
<pre>public static&nbsp;void&nbsp;absdiff(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="absdiff-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>absdiff</h4>
<pre>public static&nbsp;void&nbsp;absdiff(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="add-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public static&nbsp;void&nbsp;add(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="add-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public static&nbsp;void&nbsp;add(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="add-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public static&nbsp;void&nbsp;add(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                       int&nbsp;dtype)</pre>
</li>
</ul>
<a name="add-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public static&nbsp;void&nbsp;add(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="add-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public static&nbsp;void&nbsp;add(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="add-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public static&nbsp;void&nbsp;add(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                       int&nbsp;dtype)</pre>
</li>
</ul>
<a name="addWeighted-org.opencv.core.Mat-double-org.opencv.core.Mat-double-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWeighted</h4>
<pre>public static&nbsp;void&nbsp;addWeighted(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                               double&nbsp;alpha,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                               double&nbsp;beta,
                               double&nbsp;gamma,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="addWeighted-org.opencv.core.Mat-double-org.opencv.core.Mat-double-double-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWeighted</h4>
<pre>public static&nbsp;void&nbsp;addWeighted(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                               double&nbsp;alpha,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                               double&nbsp;beta,
                               double&nbsp;gamma,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               int&nbsp;dtype)</pre>
</li>
</ul>
<a name="batchDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>batchDistance</h4>
<pre>public static&nbsp;void&nbsp;batchDistance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist,
                                 int&nbsp;dtype,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nidx)</pre>
</li>
</ul>
<a name="batchDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>batchDistance</h4>
<pre>public static&nbsp;void&nbsp;batchDistance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist,
                                 int&nbsp;dtype,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nidx,
                                 int&nbsp;normType,
                                 int&nbsp;K)</pre>
</li>
</ul>
<a name="batchDistance-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-int-int-org.opencv.core.Mat-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>batchDistance</h4>
<pre>public static&nbsp;void&nbsp;batchDistance(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dist,
                                 int&nbsp;dtype,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;nidx,
                                 int&nbsp;normType,
                                 int&nbsp;K,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                                 int&nbsp;update,
                                 boolean&nbsp;crosscheck)</pre>
</li>
</ul>
<a name="bitwise_and-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_and</h4>
<pre>public static&nbsp;void&nbsp;bitwise_and(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="bitwise_and-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_and</h4>
<pre>public static&nbsp;void&nbsp;bitwise_and(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="bitwise_not-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_not</h4>
<pre>public static&nbsp;void&nbsp;bitwise_not(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="bitwise_not-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_not</h4>
<pre>public static&nbsp;void&nbsp;bitwise_not(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="bitwise_or-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_or</h4>
<pre>public static&nbsp;void&nbsp;bitwise_or(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="bitwise_or-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_or</h4>
<pre>public static&nbsp;void&nbsp;bitwise_or(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="bitwise_xor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_xor</h4>
<pre>public static&nbsp;void&nbsp;bitwise_xor(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="bitwise_xor-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bitwise_xor</h4>
<pre>public static&nbsp;void&nbsp;bitwise_xor(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="borderInterpolate-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>borderInterpolate</h4>
<pre>public static&nbsp;int&nbsp;borderInterpolate(int&nbsp;p,
                                    int&nbsp;len,
                                    int&nbsp;borderType)</pre>
</li>
</ul>
<a name="calcCovarMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcCovarMatrix</h4>
<pre>public static&nbsp;void&nbsp;calcCovarMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covar,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                                   int&nbsp;flags)</pre>
</li>
</ul>
<a name="calcCovarMatrix-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calcCovarMatrix</h4>
<pre>public static&nbsp;void&nbsp;calcCovarMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;covar,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                                   int&nbsp;flags,
                                   int&nbsp;ctype)</pre>
</li>
</ul>
<a name="cartToPolar-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cartToPolar</h4>
<pre>public static&nbsp;void&nbsp;cartToPolar(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle)</pre>
</li>
</ul>
<a name="cartToPolar-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cartToPolar</h4>
<pre>public static&nbsp;void&nbsp;cartToPolar(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
                               boolean&nbsp;angleInDegrees)</pre>
</li>
</ul>
<a name="checkRange-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkRange</h4>
<pre>public static&nbsp;boolean&nbsp;checkRange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a)</pre>
</li>
</ul>
<a name="checkRange-org.opencv.core.Mat-boolean-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkRange</h4>
<pre>public static&nbsp;boolean&nbsp;checkRange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
                                 boolean&nbsp;quiet,
                                 double&nbsp;minVal,
                                 double&nbsp;maxVal)</pre>
</li>
</ul>
<a name="compare-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compare</h4>
<pre>public static&nbsp;void&nbsp;compare(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           int&nbsp;cmpop)</pre>
</li>
</ul>
<a name="compare-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compare</h4>
<pre>public static&nbsp;void&nbsp;compare(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           int&nbsp;cmpop)</pre>
</li>
</ul>
<a name="completeSymm-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>completeSymm</h4>
<pre>public static&nbsp;void&nbsp;completeSymm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</pre>
</li>
</ul>
<a name="completeSymm-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>completeSymm</h4>
<pre>public static&nbsp;void&nbsp;completeSymm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx,
                                boolean&nbsp;lowerToUpper)</pre>
</li>
</ul>
<a name="convertScaleAbs-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertScaleAbs</h4>
<pre>public static&nbsp;void&nbsp;convertScaleAbs(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="convertScaleAbs-org.opencv.core.Mat-org.opencv.core.Mat-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertScaleAbs</h4>
<pre>public static&nbsp;void&nbsp;convertScaleAbs(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                   <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                   double&nbsp;alpha,
                                   double&nbsp;beta)</pre>
</li>
</ul>
<a name="copyMakeBorder-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyMakeBorder</h4>
<pre>public static&nbsp;void&nbsp;copyMakeBorder(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                  int&nbsp;top,
                                  int&nbsp;bottom,
                                  int&nbsp;left,
                                  int&nbsp;right,
                                  int&nbsp;borderType)</pre>
</li>
</ul>
<a name="copyMakeBorder-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-int-int-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyMakeBorder</h4>
<pre>public static&nbsp;void&nbsp;copyMakeBorder(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                  int&nbsp;top,
                                  int&nbsp;bottom,
                                  int&nbsp;left,
                                  int&nbsp;right,
                                  int&nbsp;borderType,
                                  <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;value)</pre>
</li>
</ul>
<a name="countNonZero-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>countNonZero</h4>
<pre>public static&nbsp;int&nbsp;countNonZero(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</pre>
</li>
</ul>
<a name="cubeRoot-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cubeRoot</h4>
<pre>public static&nbsp;float&nbsp;cubeRoot(float&nbsp;val)</pre>
</li>
</ul>
<a name="dct-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dct</h4>
<pre>public static&nbsp;void&nbsp;dct(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="dct-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dct</h4>
<pre>public static&nbsp;void&nbsp;dct(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                       int&nbsp;flags)</pre>
</li>
</ul>
<a name="determinant-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>determinant</h4>
<pre>public static&nbsp;double&nbsp;determinant(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</pre>
</li>
</ul>
<a name="dft-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dft</h4>
<pre>public static&nbsp;void&nbsp;dft(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="dft-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dft</h4>
<pre>public static&nbsp;void&nbsp;dft(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                       int&nbsp;flags,
                       int&nbsp;nonzeroRows)</pre>
</li>
</ul>
<a name="divide-double-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(double&nbsp;scale,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="divide-double-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(double&nbsp;scale,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          int&nbsp;dtype)</pre>
</li>
</ul>
<a name="divide-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="divide-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          double&nbsp;scale)</pre>
</li>
</ul>
<a name="divide-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          double&nbsp;scale,
                          int&nbsp;dtype)</pre>
</li>
</ul>
<a name="divide-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="divide-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          double&nbsp;scale)</pre>
</li>
</ul>
<a name="divide-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>divide</h4>
<pre>public static&nbsp;void&nbsp;divide(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          double&nbsp;scale,
                          int&nbsp;dtype)</pre>
</li>
</ul>
<a name="eigen-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eigen</h4>
<pre>public static&nbsp;boolean&nbsp;eigen(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvalues)</pre>
</li>
</ul>
<a name="eigen-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eigen</h4>
<pre>public static&nbsp;boolean&nbsp;eigen(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvalues,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors)</pre>
</li>
</ul>
<a name="exp-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exp</h4>
<pre>public static&nbsp;void&nbsp;exp(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="extractChannel-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extractChannel</h4>
<pre>public static&nbsp;void&nbsp;extractChannel(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                  int&nbsp;coi)</pre>
</li>
</ul>
<a name="fastAtan2-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fastAtan2</h4>
<pre>public static&nbsp;float&nbsp;fastAtan2(float&nbsp;y,
                              float&nbsp;x)</pre>
</li>
</ul>
<a name="findNonZero-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findNonZero</h4>
<pre>public static&nbsp;void&nbsp;findNonZero(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx)</pre>
</li>
</ul>
<a name="flip-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flip</h4>
<pre>public static&nbsp;void&nbsp;flip(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                        int&nbsp;flipCode)</pre>
</li>
</ul>
<a name="gemm-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Mat-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gemm</h4>
<pre>public static&nbsp;void&nbsp;gemm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                        double&nbsp;alpha,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src3,
                        double&nbsp;beta,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="gemm-org.opencv.core.Mat-org.opencv.core.Mat-double-org.opencv.core.Mat-double-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gemm</h4>
<pre>public static&nbsp;void&nbsp;gemm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                        double&nbsp;alpha,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src3,
                        double&nbsp;beta,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                        int&nbsp;flags)</pre>
</li>
</ul>
<a name="getBuildInformation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildInformation</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getBuildInformation()</pre>
</li>
</ul>
<a name="getCPUTickCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCPUTickCount</h4>
<pre>public static&nbsp;long&nbsp;getCPUTickCount()</pre>
</li>
</ul>
<a name="getNumberOfCPUs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumberOfCPUs</h4>
<pre>public static&nbsp;int&nbsp;getNumberOfCPUs()</pre>
</li>
</ul>
<a name="getNumThreads--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumThreads</h4>
<pre>public static&nbsp;int&nbsp;getNumThreads()</pre>
</li>
</ul>
<a name="getOptimalDFTSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOptimalDFTSize</h4>
<pre>public static&nbsp;int&nbsp;getOptimalDFTSize(int&nbsp;vecsize)</pre>
</li>
</ul>
<a name="getThreadNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThreadNum</h4>
<pre>public static&nbsp;int&nbsp;getThreadNum()</pre>
</li>
</ul>
<a name="getTickCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTickCount</h4>
<pre>public static&nbsp;long&nbsp;getTickCount()</pre>
</li>
</ul>
<a name="getTickFrequency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTickFrequency</h4>
<pre>public static&nbsp;double&nbsp;getTickFrequency()</pre>
</li>
</ul>
<a name="hconcat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hconcat</h4>
<pre>public static&nbsp;void&nbsp;hconcat(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="idct-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>idct</h4>
<pre>public static&nbsp;void&nbsp;idct(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="idct-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>idct</h4>
<pre>public static&nbsp;void&nbsp;idct(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                        int&nbsp;flags)</pre>
</li>
</ul>
<a name="idft-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>idft</h4>
<pre>public static&nbsp;void&nbsp;idft(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="idft-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>idft</h4>
<pre>public static&nbsp;void&nbsp;idft(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                        int&nbsp;flags,
                        int&nbsp;nonzeroRows)</pre>
</li>
</ul>
<a name="inRange-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inRange</h4>
<pre>public static&nbsp;void&nbsp;inRange(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;lowerb,
                           <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;upperb,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="insertChannel-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertChannel</h4>
<pre>public static&nbsp;void&nbsp;insertChannel(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 int&nbsp;coi)</pre>
</li>
</ul>
<a name="invert-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invert</h4>
<pre>public static&nbsp;double&nbsp;invert(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="invert-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invert</h4>
<pre>public static&nbsp;double&nbsp;invert(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            int&nbsp;flags)</pre>
</li>
</ul>
<a name="kmeans-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.TermCriteria-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>kmeans</h4>
<pre>public static&nbsp;double&nbsp;kmeans(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                            int&nbsp;K,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bestLabels,
                            <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                            int&nbsp;attempts,
                            int&nbsp;flags)</pre>
</li>
</ul>
<a name="kmeans-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.TermCriteria-int-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>kmeans</h4>
<pre>public static&nbsp;double&nbsp;kmeans(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                            int&nbsp;K,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;bestLabels,
                            <a href="../../../org/opencv/core/TermCriteria.html" title="class in org.opencv.core">TermCriteria</a>&nbsp;criteria,
                            int&nbsp;attempts,
                            int&nbsp;flags,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;centers)</pre>
</li>
</ul>
<a name="log-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>log</h4>
<pre>public static&nbsp;void&nbsp;log(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="LUT-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LUT</h4>
<pre>public static&nbsp;void&nbsp;LUT(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;lut,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="magnitude-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>magnitude</h4>
<pre>public static&nbsp;void&nbsp;magnitude(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude)</pre>
</li>
</ul>
<a name="Mahalanobis-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Mahalanobis</h4>
<pre>public static&nbsp;double&nbsp;Mahalanobis(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;v1,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;v2,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;icovar)</pre>
</li>
</ul>
<a name="max-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>max</h4>
<pre>public static&nbsp;void&nbsp;max(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="max-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>max</h4>
<pre>public static&nbsp;void&nbsp;max(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="mean-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mean</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</pre>
</li>
</ul>
<a name="mean-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mean</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;mean(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="meanStdDev-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfDouble-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>meanStdDev</h4>
<pre>public static&nbsp;void&nbsp;meanStdDev(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;mean,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;stddev)</pre>
</li>
</ul>
<a name="meanStdDev-org.opencv.core.Mat-org.opencv.core.MatOfDouble-org.opencv.core.MatOfDouble-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>meanStdDev</h4>
<pre>public static&nbsp;void&nbsp;meanStdDev(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;mean,
                              <a href="../../../org/opencv/core/MatOfDouble.html" title="class in org.opencv.core">MatOfDouble</a>&nbsp;stddev,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="merge-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>merge</h4>
<pre>public static&nbsp;void&nbsp;merge(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mv,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="min-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>min</h4>
<pre>public static&nbsp;void&nbsp;min(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="min-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>min</h4>
<pre>public static&nbsp;void&nbsp;min(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                       <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="minMaxLoc-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minMaxLoc</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core">Core.MinMaxLocResult</a>&nbsp;minMaxLoc(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</pre>
</li>
</ul>
<a name="minMaxLoc-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minMaxLoc</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core">Core.MinMaxLocResult</a>&nbsp;minMaxLoc(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="mixChannels-java.util.List-java.util.List-org.opencv.core.MatOfInt-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mixChannels</h4>
<pre>public static&nbsp;void&nbsp;mixChannels(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
                               java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;dst,
                               <a href="../../../org/opencv/core/MatOfInt.html" title="class in org.opencv.core">MatOfInt</a>&nbsp;fromTo)</pre>
</li>
</ul>
<a name="mulSpectrums-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mulSpectrums</h4>
<pre>public static&nbsp;void&nbsp;mulSpectrums(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;b,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;c,
                                int&nbsp;flags)</pre>
</li>
</ul>
<a name="mulSpectrums-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mulSpectrums</h4>
<pre>public static&nbsp;void&nbsp;mulSpectrums(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;b,
                                <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;c,
                                int&nbsp;flags,
                                boolean&nbsp;conjB)</pre>
</li>
</ul>
<a name="multiply-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multiply</h4>
<pre>public static&nbsp;void&nbsp;multiply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="multiply-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multiply</h4>
<pre>public static&nbsp;void&nbsp;multiply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            double&nbsp;scale)</pre>
</li>
</ul>
<a name="multiply-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multiply</h4>
<pre>public static&nbsp;void&nbsp;multiply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            double&nbsp;scale,
                            int&nbsp;dtype)</pre>
</li>
</ul>
<a name="multiply-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multiply</h4>
<pre>public static&nbsp;void&nbsp;multiply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="multiply-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multiply</h4>
<pre>public static&nbsp;void&nbsp;multiply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            double&nbsp;scale)</pre>
</li>
</ul>
<a name="multiply-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multiply</h4>
<pre>public static&nbsp;void&nbsp;multiply(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            double&nbsp;scale,
                            int&nbsp;dtype)</pre>
</li>
</ul>
<a name="mulTransposed-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mulTransposed</h4>
<pre>public static&nbsp;void&nbsp;mulTransposed(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 boolean&nbsp;aTa)</pre>
</li>
</ul>
<a name="mulTransposed-org.opencv.core.Mat-org.opencv.core.Mat-boolean-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mulTransposed</h4>
<pre>public static&nbsp;void&nbsp;mulTransposed(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 boolean&nbsp;aTa,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;delta,
                                 double&nbsp;scale)</pre>
</li>
</ul>
<a name="mulTransposed-org.opencv.core.Mat-org.opencv.core.Mat-boolean-org.opencv.core.Mat-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mulTransposed</h4>
<pre>public static&nbsp;void&nbsp;mulTransposed(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                 boolean&nbsp;aTa,
                                 <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;delta,
                                 double&nbsp;scale,
                                 int&nbsp;dtype)</pre>
</li>
</ul>
<a name="norm-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>norm</h4>
<pre>public static&nbsp;double&nbsp;norm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1)</pre>
</li>
</ul>
<a name="norm-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>norm</h4>
<pre>public static&nbsp;double&nbsp;norm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          int&nbsp;normType)</pre>
</li>
</ul>
<a name="norm-org.opencv.core.Mat-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>norm</h4>
<pre>public static&nbsp;double&nbsp;norm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          int&nbsp;normType,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="norm-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>norm</h4>
<pre>public static&nbsp;double&nbsp;norm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2)</pre>
</li>
</ul>
<a name="norm-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>norm</h4>
<pre>public static&nbsp;double&nbsp;norm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          int&nbsp;normType)</pre>
</li>
</ul>
<a name="norm-org.opencv.core.Mat-org.opencv.core.Mat-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>norm</h4>
<pre>public static&nbsp;double&nbsp;norm(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                          int&nbsp;normType,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="normalize-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalize</h4>
<pre>public static&nbsp;void&nbsp;normalize(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="normalize-org.opencv.core.Mat-org.opencv.core.Mat-double-double-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalize</h4>
<pre>public static&nbsp;void&nbsp;normalize(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                             double&nbsp;alpha,
                             double&nbsp;beta,
                             int&nbsp;norm_type)</pre>
</li>
</ul>
<a name="normalize-org.opencv.core.Mat-org.opencv.core.Mat-double-double-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalize</h4>
<pre>public static&nbsp;void&nbsp;normalize(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                             double&nbsp;alpha,
                             double&nbsp;beta,
                             int&nbsp;norm_type,
                             int&nbsp;dtype)</pre>
</li>
</ul>
<a name="normalize-org.opencv.core.Mat-org.opencv.core.Mat-double-double-int-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalize</h4>
<pre>public static&nbsp;void&nbsp;normalize(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                             double&nbsp;alpha,
                             double&nbsp;beta,
                             int&nbsp;norm_type,
                             int&nbsp;dtype,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="patchNaNs-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>patchNaNs</h4>
<pre>public static&nbsp;void&nbsp;patchNaNs(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a)</pre>
</li>
</ul>
<a name="patchNaNs-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>patchNaNs</h4>
<pre>public static&nbsp;void&nbsp;patchNaNs(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;a,
                             double&nbsp;val)</pre>
</li>
</ul>
<a name="PCABackProject-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PCABackProject</h4>
<pre>public static&nbsp;void&nbsp;PCABackProject(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
                                  <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</pre>
</li>
</ul>
<a name="PCACompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PCACompute</h4>
<pre>public static&nbsp;void&nbsp;PCACompute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors)</pre>
</li>
</ul>
<a name="PCACompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PCACompute</h4>
<pre>public static&nbsp;void&nbsp;PCACompute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
                              double&nbsp;retainedVariance)</pre>
</li>
</ul>
<a name="PCACompute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PCACompute</h4>
<pre>public static&nbsp;void&nbsp;PCACompute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
                              int&nbsp;maxComponents)</pre>
</li>
</ul>
<a name="PCAProject-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PCAProject</h4>
<pre>public static&nbsp;void&nbsp;PCAProject(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;data,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mean,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;eigenvectors,
                              <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</pre>
</li>
</ul>
<a name="perspectiveTransform-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>perspectiveTransform</h4>
<pre>public static&nbsp;void&nbsp;perspectiveTransform(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="phase-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>phase</h4>
<pre>public static&nbsp;void&nbsp;phase(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle)</pre>
</li>
</ul>
<a name="phase-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>phase</h4>
<pre>public static&nbsp;void&nbsp;phase(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
                         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
                         boolean&nbsp;angleInDegrees)</pre>
</li>
</ul>
<a name="polarToCart-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>polarToCart</h4>
<pre>public static&nbsp;void&nbsp;polarToCart(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y)</pre>
</li>
</ul>
<a name="polarToCart-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>polarToCart</h4>
<pre>public static&nbsp;void&nbsp;polarToCart(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;magnitude,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;angle,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;x,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;y,
                               boolean&nbsp;angleInDegrees)</pre>
</li>
</ul>
<a name="pow-org.opencv.core.Mat-double-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pow</h4>
<pre>public static&nbsp;void&nbsp;pow(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                       double&nbsp;power,
                       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="PSNR-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PSNR</h4>
<pre>public static&nbsp;double&nbsp;PSNR(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2)</pre>
</li>
</ul>
<a name="randn-org.opencv.core.Mat-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randn</h4>
<pre>public static&nbsp;void&nbsp;randn(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         double&nbsp;mean,
                         double&nbsp;stddev)</pre>
</li>
</ul>
<a name="randShuffle-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randShuffle</h4>
<pre>public static&nbsp;void&nbsp;randShuffle(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="randShuffle-org.opencv.core.Mat-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randShuffle</h4>
<pre>public static&nbsp;void&nbsp;randShuffle(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                               double&nbsp;iterFactor)</pre>
</li>
</ul>
<a name="randu-org.opencv.core.Mat-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randu</h4>
<pre>public static&nbsp;void&nbsp;randu(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                         double&nbsp;low,
                         double&nbsp;high)</pre>
</li>
</ul>
<a name="reduce-org.opencv.core.Mat-org.opencv.core.Mat-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reduce</h4>
<pre>public static&nbsp;void&nbsp;reduce(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          int&nbsp;dim,
                          int&nbsp;rtype)</pre>
</li>
</ul>
<a name="reduce-org.opencv.core.Mat-org.opencv.core.Mat-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reduce</h4>
<pre>public static&nbsp;void&nbsp;reduce(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                          int&nbsp;dim,
                          int&nbsp;rtype,
                          int&nbsp;dtype)</pre>
</li>
</ul>
<a name="repeat-org.opencv.core.Mat-int-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>repeat</h4>
<pre>public static&nbsp;void&nbsp;repeat(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                          int&nbsp;ny,
                          int&nbsp;nx,
                          <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="scaleAdd-org.opencv.core.Mat-double-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scaleAdd</h4>
<pre>public static&nbsp;void&nbsp;scaleAdd(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            double&nbsp;alpha,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="setErrorVerbosity-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setErrorVerbosity</h4>
<pre>public static&nbsp;void&nbsp;setErrorVerbosity(boolean&nbsp;verbose)</pre>
</li>
</ul>
<a name="setIdentity-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIdentity</h4>
<pre>public static&nbsp;void&nbsp;setIdentity(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</pre>
</li>
</ul>
<a name="setIdentity-org.opencv.core.Mat-org.opencv.core.Scalar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIdentity</h4>
<pre>public static&nbsp;void&nbsp;setIdentity(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx,
                               <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;s)</pre>
</li>
</ul>
<a name="setNumThreads-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumThreads</h4>
<pre>public static&nbsp;void&nbsp;setNumThreads(int&nbsp;nthreads)</pre>
</li>
</ul>
<a name="solve-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solve</h4>
<pre>public static&nbsp;boolean&nbsp;solve(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="solve-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solve</h4>
<pre>public static&nbsp;boolean&nbsp;solve(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            int&nbsp;flags)</pre>
</li>
</ul>
<a name="solveCubic-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solveCubic</h4>
<pre>public static&nbsp;int&nbsp;solveCubic(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;coeffs,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;roots)</pre>
</li>
</ul>
<a name="solvePoly-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solvePoly</h4>
<pre>public static&nbsp;double&nbsp;solvePoly(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;coeffs,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;roots)</pre>
</li>
</ul>
<a name="solvePoly-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>solvePoly</h4>
<pre>public static&nbsp;double&nbsp;solvePoly(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;coeffs,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;roots,
                               int&nbsp;maxIters)</pre>
</li>
</ul>
<a name="sort-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sort</h4>
<pre>public static&nbsp;void&nbsp;sort(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                        int&nbsp;flags)</pre>
</li>
</ul>
<a name="sortIdx-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortIdx</h4>
<pre>public static&nbsp;void&nbsp;sortIdx(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                           int&nbsp;flags)</pre>
</li>
</ul>
<a name="split-org.opencv.core.Mat-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>split</h4>
<pre>public static&nbsp;void&nbsp;split(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m,
                         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;mv)</pre>
</li>
</ul>
<a name="sqrt-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sqrt</h4>
<pre>public static&nbsp;void&nbsp;sqrt(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                        <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="subtract-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subtract</h4>
<pre>public static&nbsp;void&nbsp;subtract(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="subtract-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subtract</h4>
<pre>public static&nbsp;void&nbsp;subtract(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="subtract-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subtract</h4>
<pre>public static&nbsp;void&nbsp;subtract(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                            int&nbsp;dtype)</pre>
</li>
</ul>
<a name="subtract-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subtract</h4>
<pre>public static&nbsp;void&nbsp;subtract(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="subtract-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subtract</h4>
<pre>public static&nbsp;void&nbsp;subtract(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask)</pre>
</li>
</ul>
<a name="subtract-org.opencv.core.Mat-org.opencv.core.Scalar-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subtract</h4>
<pre>public static&nbsp;void&nbsp;subtract(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src1,
                            <a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;src2,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
                            int&nbsp;dtype)</pre>
</li>
</ul>
<a name="sumElems-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sumElems</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;sumElems(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src)</pre>
</li>
</ul>
<a name="SVBackSubst-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVBackSubst</h4>
<pre>public static&nbsp;void&nbsp;SVBackSubst(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;w,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;u,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vt,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;rhs,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="SVDecomp-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVDecomp</h4>
<pre>public static&nbsp;void&nbsp;SVDecomp(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;w,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;u,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vt)</pre>
</li>
</ul>
<a name="SVDecomp-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVDecomp</h4>
<pre>public static&nbsp;void&nbsp;SVDecomp(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;w,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;u,
                            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vt,
                            int&nbsp;flags)</pre>
</li>
</ul>
<a name="trace-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>trace</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Scalar.html" title="class in org.opencv.core">Scalar</a>&nbsp;trace(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mtx)</pre>
</li>
</ul>
<a name="transform-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>transform</h4>
<pre>public static&nbsp;void&nbsp;transform(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</pre>
</li>
</ul>
<a name="transpose-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>transpose</h4>
<pre>public static&nbsp;void&nbsp;transpose(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
                             <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
<a name="vconcat-java.util.List-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>vconcat</h4>
<pre>public static&nbsp;void&nbsp;vconcat(java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;src,
                           <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/core/Core.MinMaxLocResult.html" title="class in org.opencv.core"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/core/Core.html" target="_top">Frames</a></li>
<li><a href="Core.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
