<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_65) on Fri Dec 18 18:19:52 MSK 2015 -->
<title>BackgroundSubtractorMOG2</title>
<meta name="date" content="2015-12-18">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BackgroundSubtractorMOG2";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">OpenCV 3.1.0</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/BackgroundSubtractorMOG2.html" target="_top">Frames</a></li>
<li><a href="BackgroundSubtractorMOG2.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.video</div>
<h2 title="Class BackgroundSubtractorMOG2" class="title">Class BackgroundSubtractorMOG2</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">org.opencv.video.BackgroundSubtractor</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.video.BackgroundSubtractorMOG2</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BackgroundSubtractorMOG2</span>
extends <a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getBackgroundRatio--">getBackgroundRatio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getComplexityReductionThreshold--">getComplexityReductionThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getDetectShadows--">getDetectShadows</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getHistory--">getHistory</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getNMixtures--">getNMixtures</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getShadowThreshold--">getShadowThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getShadowValue--">getShadowValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarInit--">getVarInit</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarMax--">getVarMax</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarMin--">getVarMin</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarThreshold--">getVarThreshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#getVarThresholdGen--">getVarThresholdGen</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setBackgroundRatio-double-">setBackgroundRatio</a></span>(double&nbsp;ratio)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setComplexityReductionThreshold-double-">setComplexityReductionThreshold</a></span>(double&nbsp;ct)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setDetectShadows-boolean-">setDetectShadows</a></span>(boolean&nbsp;detectShadows)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setHistory-int-">setHistory</a></span>(int&nbsp;history)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setNMixtures-int-">setNMixtures</a></span>(int&nbsp;nmixtures)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setShadowThreshold-double-">setShadowThreshold</a></span>(double&nbsp;threshold)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setShadowValue-int-">setShadowValue</a></span>(int&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarInit-double-">setVarInit</a></span>(double&nbsp;varInit)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarMax-double-">setVarMax</a></span>(double&nbsp;varMax)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarMin-double-">setVarMin</a></span>(double&nbsp;varMin)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarThreshold-double-">setVarThreshold</a></span>(double&nbsp;varThreshold)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/video/BackgroundSubtractorMOG2.html#setVarThresholdGen-double-">setVarThresholdGen</a></span>(double&nbsp;varThresholdGen)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.video.BackgroundSubtractor">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.video.<a href="../../../org/opencv/video/BackgroundSubtractor.html" title="class in org.opencv.video">BackgroundSubtractor</a></h3>
<code><a href="../../../org/opencv/video/BackgroundSubtractor.html#apply-org.opencv.core.Mat-org.opencv.core.Mat-">apply</a>, <a href="../../../org/opencv/video/BackgroundSubtractor.html#apply-org.opencv.core.Mat-org.opencv.core.Mat-double-">apply</a>, <a href="../../../org/opencv/video/BackgroundSubtractor.html#getBackgroundImage-org.opencv.core.Mat-">getBackgroundImage</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBackgroundRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackgroundRatio</h4>
<pre>public&nbsp;double&nbsp;getBackgroundRatio()</pre>
</li>
</ul>
<a name="getComplexityReductionThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComplexityReductionThreshold</h4>
<pre>public&nbsp;double&nbsp;getComplexityReductionThreshold()</pre>
</li>
</ul>
<a name="getDetectShadows--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDetectShadows</h4>
<pre>public&nbsp;boolean&nbsp;getDetectShadows()</pre>
</li>
</ul>
<a name="getHistory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHistory</h4>
<pre>public&nbsp;int&nbsp;getHistory()</pre>
</li>
</ul>
<a name="getNMixtures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNMixtures</h4>
<pre>public&nbsp;int&nbsp;getNMixtures()</pre>
</li>
</ul>
<a name="getShadowThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShadowThreshold</h4>
<pre>public&nbsp;double&nbsp;getShadowThreshold()</pre>
</li>
</ul>
<a name="getShadowValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShadowValue</h4>
<pre>public&nbsp;int&nbsp;getShadowValue()</pre>
</li>
</ul>
<a name="getVarInit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarInit</h4>
<pre>public&nbsp;double&nbsp;getVarInit()</pre>
</li>
</ul>
<a name="getVarMax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarMax</h4>
<pre>public&nbsp;double&nbsp;getVarMax()</pre>
</li>
</ul>
<a name="getVarMin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarMin</h4>
<pre>public&nbsp;double&nbsp;getVarMin()</pre>
</li>
</ul>
<a name="getVarThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarThreshold</h4>
<pre>public&nbsp;double&nbsp;getVarThreshold()</pre>
</li>
</ul>
<a name="getVarThresholdGen--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarThresholdGen</h4>
<pre>public&nbsp;double&nbsp;getVarThresholdGen()</pre>
</li>
</ul>
<a name="setBackgroundRatio-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackgroundRatio</h4>
<pre>public&nbsp;void&nbsp;setBackgroundRatio(double&nbsp;ratio)</pre>
</li>
</ul>
<a name="setComplexityReductionThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComplexityReductionThreshold</h4>
<pre>public&nbsp;void&nbsp;setComplexityReductionThreshold(double&nbsp;ct)</pre>
</li>
</ul>
<a name="setDetectShadows-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDetectShadows</h4>
<pre>public&nbsp;void&nbsp;setDetectShadows(boolean&nbsp;detectShadows)</pre>
</li>
</ul>
<a name="setHistory-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHistory</h4>
<pre>public&nbsp;void&nbsp;setHistory(int&nbsp;history)</pre>
</li>
</ul>
<a name="setNMixtures-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNMixtures</h4>
<pre>public&nbsp;void&nbsp;setNMixtures(int&nbsp;nmixtures)</pre>
</li>
</ul>
<a name="setShadowThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShadowThreshold</h4>
<pre>public&nbsp;void&nbsp;setShadowThreshold(double&nbsp;threshold)</pre>
</li>
</ul>
<a name="setShadowValue-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShadowValue</h4>
<pre>public&nbsp;void&nbsp;setShadowValue(int&nbsp;value)</pre>
</li>
</ul>
<a name="setVarInit-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarInit</h4>
<pre>public&nbsp;void&nbsp;setVarInit(double&nbsp;varInit)</pre>
</li>
</ul>
<a name="setVarMax-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarMax</h4>
<pre>public&nbsp;void&nbsp;setVarMax(double&nbsp;varMax)</pre>
</li>
</ul>
<a name="setVarMin-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarMin</h4>
<pre>public&nbsp;void&nbsp;setVarMin(double&nbsp;varMin)</pre>
</li>
</ul>
<a name="setVarThreshold-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVarThreshold</h4>
<pre>public&nbsp;void&nbsp;setVarThreshold(double&nbsp;varThreshold)</pre>
</li>
</ul>
<a name="setVarThresholdGen-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVarThresholdGen</h4>
<pre>public&nbsp;void&nbsp;setVarThresholdGen(double&nbsp;varThresholdGen)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href="http://docs.opencv.org">OpenCV 3.1.0 Documentation</a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/video/BackgroundSubtractorKNN.html" title="class in org.opencv.video"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/video/DenseOpticalFlow.html" title="class in org.opencv.video"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/video/BackgroundSubtractorMOG2.html" target="_top">Frames</a></li>
<li><a href="BackgroundSubtractorMOG2.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
