apply plugin: 'com.android.library'

android {
    compileSdkVersion 33
    buildToolsVersion "29.0.3"

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        ndk
                {
                    moduleName "Scanner"
                }
    }
    sourceSets.main
            {
                jni.srcDirs = []
                jniLibs.srcDir 'src/main/libs'
            }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
}
