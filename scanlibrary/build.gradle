apply plugin: 'com.android.library'

android {
    namespace 'com.scanlibrary'
    compileSdk 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        ndk
                {
                    moduleName "Scanner"
                }
    }
    sourceSets.main
            {
                jni.srcDirs = []
                jniLibs.srcDir 'src/main/libs'
            }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
}
