// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
        maven { url "https://maven.google.com" }
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
        gradlePluginPortal()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.3'
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:0.14.0'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
        maven { url "https://maven.google.com" }
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
        gradlePluginPortal()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}