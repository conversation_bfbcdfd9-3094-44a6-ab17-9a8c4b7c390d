// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url "https://maven.google.com" }
        gradlePluginPortal()
        flatDir {
            dirs 'libs'
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:0.14.0'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url "https://maven.google.com" }
        gradlePluginPortal()
        flatDir {
            dirs 'libs'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}