<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="pdf.free.camscanner.docscanner">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true"
        android:exported="true"/>
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false"
        android:exported="true"/>


    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <uses-permission android:name="com.android.vending.BILLING" />

    <application
        android:name=".MyApp"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.CamScanner"
        android:usesCleartextTraffic="true">


        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="android:resource"
                android:exported="true"/>
        </provider>

        <activity android:name="pdf.free.camscanner.docscanner.activity.BaseActivity" />
        <activity
            android:name=".activity.SplashActivity"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.ScannerActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:theme="@style/uCropStyle"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.IDCardPreviewActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.CropDocumentActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.CurrentFilterActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.SavedDocumentActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.DocumentEditorActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.SavedEditDocumentActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.SavedDocumentPreviewActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.NoteActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.ImageToTextActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.GroupDocumentActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.PDFViewerActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.QRGenerateActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.QRReaderActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>
        <activity
            android:name="pdf.free.camscanner.docscanner.activity.PrivacyPolicyActivity"
            android:screenOrientation="portrait"
            android:exported="true"/>

        <meta-data
            android:name="com.google.android.gms.vision.DEPENDENCIES"
            android:value="ocr"
            android:exported="true"/>
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/admob_app_id"
            android:exported="true"/>

    </application>

</manifest>