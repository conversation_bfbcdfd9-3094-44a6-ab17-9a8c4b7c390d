<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.CamScanner" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimary</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/colorPrimary</item>
        <item name="colorSecondaryVariant">@color/colorPrimary</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:itemBackground">@color/bg_color</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:statusBarColor" tools:targetApi="l">@color/colorPrimary</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="ThemeWithRoundShape">
        <item name="android:windowBackground">@drawable/dialog_bg</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowMinWidthMajor">80%</item>
        <item name="windowActionBar">false</item>
    </style>

    <style name="CustomTabLayout">
        <item name="tabTextAppearance">@style/TabTextAppeareance</item>
    </style>

    <style name="TabTextAppeareance">
        <item name="android:textSize">@dimen/_12sdp</item>
        <item name="android:fontFamily">@font/inter_medium</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="IconImageStyle">
        <item name="android:textColor">@color/unselected_txt_color</item>
    </style>

    <style name="uCropStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:textColor">@color/black</item>
    </style>
</resources>