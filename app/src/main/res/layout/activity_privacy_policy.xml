<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="5dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_29sdp"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_2sdp"
            android:padding="@dimen/_7sdp"
            android:src="@drawable/ic_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/iv_back"
            android:fontFamily="@font/inter_medium"
            android:layout_marginLeft="@dimen/_8sdp"
            android:text="@string/privacy_policy"
            android:textColor="@color/black"
            android:textSize="@dimen/_16sdp" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/_10sdp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:layout_height="50dp">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
