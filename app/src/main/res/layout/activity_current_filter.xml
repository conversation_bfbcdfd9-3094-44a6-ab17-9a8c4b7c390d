<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:elevation="5dp"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_29sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@+id/iv_back"
                android:layout_marginLeft="@dimen/_5sdp"
                android:fontFamily="@font/inter_medium"
                android:text="@string/currentFilter"
                android:textColor="@color/black"
                android:textSize="@dimen/_15sdp" />

            <ImageView
                android:id="@+id/iv_done"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/ly_bottom"
                android:layout_centerInParent="true"
                android:layout_margin="@dimen/_15sdp">

                <com.github.chrisbanes.photoview.PhotoView
                    android:id="@+id/iv_preview_filter"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ly_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/toolbar_color"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/_15sdp"
                android:paddingBottom="@dimen/_15sdp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/iv_original"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_32sdp"
                        android:background="@drawable/filter_selection_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:onClick="onClick"
                        android:text="O"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_18sdp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/original"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_10sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/iv_color"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_32sdp"
                        android:background="@drawable/filter_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:onClick="onClick"
                        android:text="C"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18sdp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/color"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_10sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/iv_sharp_black"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_32sdp"
                        android:background="@drawable/filter_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:onClick="onClick"
                        android:text="S"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18sdp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/sharpBlack"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_10sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/iv_ocv_black"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_32sdp"
                        android:background="@drawable/filter_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:onClick="onClick"
                        android:text="B"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18sdp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/ocvBlack"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_10sdp" />

                </LinearLayout>

            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
