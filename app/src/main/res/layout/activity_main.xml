<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/drawer_ly"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <include
        layout="@layout/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/bg_color1">

        <include
            android:id="@+id/ly_header"
            layout="@layout/nav_header_main" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/ly_header">

            <ListView
                android:id="@+id/lv_drawer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:choiceMode="singleChoice"
                android:divider="@color/transparent"
                android:dividerHeight="0dp"
                android:scrollbars="none" />
        </LinearLayout>
    </RelativeLayout>
</androidx.drawerlayout.widget.DrawerLayout>
