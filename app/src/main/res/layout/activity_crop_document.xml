<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            android:elevation="5dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/_5sdp"
                android:layout_toEndOf="@+id/iv_back"
                android:fontFamily="@font/inter_medium"
                android:text="@string/cropDocument"
                android:textColor="@color/black"
                android:textSize="@dimen/_15sdp" />

            <ImageView
                android:id="@+id/iv_add_new_scan"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:layout_toLeftOf="@+id/iv_edit"
                android:onClick="onClick"
                android:src="@drawable/ic_add_new_scan"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/iv_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:layout_toLeftOf="@+id/iv_done"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_edit" />

            <ImageView
                android:id="@+id/iv_done"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_check_mark" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@id/ll_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/ly_bottom">


                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_full_crop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom|right|center_vertical|center_horizontal|center|end"
                        android:layout_marginRight="@dimen/_5sdp"
                        android:layout_marginBottom="@dimen/_5sdp"
                        android:elevation="5dp"
                        android:onClick="onClick"
                        android:src="@drawable/ic_full_screen" />

                    <me.pqpo.smartcropperlib.view.CropImageView
                        android:id="@+id/iv_preview_crop"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:padding="@dimen/_12sdp"

                        app:civAutoScanEnable="true"
                        app:civLineColor="@color/light_txt_color"
                        app:civMagnifierCrossColor="@color/light_txt_color"
                        app:civPointColor="@color/light_txt_color"
                        app:civPointFillColor="@color/light_txt_color"
                        app:civShowEdgeMidPoint="true"
                        app:civShowGuideLine="true" />
                </FrameLayout>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ly_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/toolbar_color"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/_12sdp"
                android:paddingBottom="@dimen/_15sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:paddingBottom="@dimen/_5sdp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/brightness"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_10sdp" />

                    <SeekBar
                        android:id="@+id/seekBarBrightness"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:max="100"
                        android:maxHeight="2dp"
                        android:progress="20"
                        android:progressDrawable="@drawable/seekbar_opacity"
                        android:thumb="@drawable/ic_thumb" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_filter_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:background="@color/toolbar_color"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/_15sdp"
                    android:paddingBottom="@dimen/_15sdp">


                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/iv_original"
                            android:layout_width="@dimen/_30sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:background="@drawable/filter_selection_bg"
                            android:fontFamily="@font/inter_medium"
                            android:gravity="center"
                            android:onClick="onClick"
                            android:text="O"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_18sdp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/original"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_10sdp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/iv_color"
                            android:layout_width="@dimen/_30sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:background="@drawable/filter_bg"
                            android:fontFamily="@font/inter_medium"
                            android:gravity="center"
                            android:onClick="onClick"
                            android:text="C"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18sdp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/color"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_10sdp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/iv_sharp_black"
                            android:layout_width="@dimen/_30sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:background="@drawable/filter_bg"
                            android:fontFamily="@font/inter_medium"
                            android:gravity="center"
                            android:onClick="onClick"
                            android:text="S"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18sdp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/sharpBlack"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_10sdp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/iv_ocv_black"
                            android:layout_width="@dimen/_30sdp"
                            android:layout_height="@dimen/_32sdp"
                            android:background="@drawable/filter_bg"
                            android:fontFamily="@font/inter_medium"
                            android:gravity="center"
                            android:onClick="onClick"
                            android:text="B"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18sdp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_5sdp"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/ocvBlack"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_10sdp" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <LinearLayout
                        android:id="@+id/ly_rotate_doc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:onClick="onClick"
                        android:orientation="vertical"
                        android:padding="@dimen/_5sdp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_rotate_doc" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/_3sdp"
                            android:fontFamily="@font/inter_regular"
                            android:text="@string/rotateDoc"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ly_current_filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_7sdp"
                        android:onClick="onClick"
                        android:orientation="vertical"
                        android:padding="@dimen/_5sdp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_current_filter" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="@dimen/_3sdp"
                            android:fontFamily="@font/inter_regular"
                            android:text="@string/currentFilter"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12sdp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/iv_retake"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/_8sdp"
                        android:layout_marginRight="@dimen/_5sdp"
                        android:layout_weight="1"
                        android:background="@drawable/white_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:onClick="onClick"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/retake"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_13sdp" />

                    <TextView
                        android:id="@+id/iv_Rotate_Doc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/_5sdp"
                        android:layout_marginRight="@dimen/_8sdp"
                        android:layout_weight="1"
                        android:background="@drawable/white_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:onClick="onClick"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/rotateDoc"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_13sdp" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>

</LinearLayout>
