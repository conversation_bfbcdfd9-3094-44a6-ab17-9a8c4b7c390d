<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:id="@+id/fl_camera"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:elevation="5dp"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_29sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:padding="@dimen/_7sdp"
                android:tint="@color/black"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@+id/iv_back"
                android:fontFamily="@font/inter_medium"
                android:text="QR Reader"
                android:layout_marginLeft="@dimen/_8sdp"
                android:textColor="@color/black"
                android:textSize="@dimen/_15sdp" />
        </RelativeLayout>
    </RelativeLayout>
</LinearLayout>
