<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_2sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/createNewFolder"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_15sdp" />

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close_black_24dp"
                        android:tint="@color/txt_color" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_18sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="@drawable/et_bg"
                    android:paddingTop="@dimen/_11sdp"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"

                    android:paddingBottom="@dimen/_11sdp">

                    <EditText
                        android:id="@+id/et_folder_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                       android:background="@color/transparent"
                        android:cursorVisible="true"
                        android:paddingLeft="@dimen/_5sdp"
                        android:digits="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_ 0123456789"
                        android:focusable="true"
                        android:fontFamily="@font/inter_medium"
                        android:hint="@string/enterFolderName"
                        android:imeOptions="actionDone"
                        android:inputType="text"
                        android:maxLength="50"
                        android:textColor="#000000"
                        android:textColorHint="@color/unselected_txt_color"
                        android:textSize="@dimen/_12sdp" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:layout_marginBottom="@dimen/_12sdp"
                    android:gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_create"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_30sdp"
                        android:background="@drawable/done_btn_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/create"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_13sdp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
