<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="5dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_2sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">

                <include
                    android:id="@+id/admob_native_container"
                    layout="@layout/admob_native_medium"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_200sdp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_15sdp"
                        android:fontFamily="@font/inter_medium"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/delete"
                        android:layout_centerHorizontal="true"
                        android:gravity="center"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_15sdp" />

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close_black_24dp"
                        android:tint="@color/txt_color" />
                </RelativeLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/areUSure"
                    android:textColor="@color/unselected_txt_color"
                    android:textSize="@dimen/_13sdp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_7sdp"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:layout_marginEnd="@dimen/_7sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="@string/deleteThisDoc"
                    android:textColor="@color/unselected_txt_color"
                    android:textSize="@dimen/_13sdp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                android:layout_marginBottom="@dimen/_12sdp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_delete"
                    android:layout_width="@dimen/_110sdp"
                    android:layout_height="@dimen/_30sdp"
                    android:background="@drawable/done_btn_bg"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_13sdp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
