<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="5dp"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_28sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/imageToText"
                android:textColor="@color/black"
                android:layout_marginLeft="@dimen/_8sdp"
                android:layout_toRightOf="@+id/iv_back"
                android:textSize="@dimen/_15sdp" />

            <ImageView
                android:id="@+id/iv_rescan_img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_5sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:tint="@color/black"
                android:src="@drawable/ic_rescan" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/_10sdp"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_preview_img"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/_10sdp" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_38sdp"
            android:background="@drawable/rounded_toolbar">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:layout_toStartOf="@+id/iv_share_txt"
                android:fontFamily="@font/inter_medium"
                android:singleLine="true"
                android:text="@string/app_name"
                android:textColor="@color/white"
                android:textSize="@dimen/_14sdp" />

            <ImageView
                android:id="@+id/iv_share_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_8sdp"
                android:layout_toStartOf="@+id/iv_copy_txt"
                android:onClick="onClick"
                android:src="@drawable/ic_share_app"
                android:tint="@color/white" />

            <ImageView
                android:id="@+id/iv_copy_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_8sdp"
                android:onClick="onClick"
                android:padding="@dimen/_4sdp"
                android:src="@drawable/ic_content_copy_black_24dp"
                android:tint="@color/white" />
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/bg_color"
            android:padding="@dimen/_7sdp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_ocr_txt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_3sdp"
                    android:gravity="start"
                    android:textAlignment="textStart"
                    android:textColor="@color/txt_color"
                    android:textSize="@dimen/_13sdp" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:layout_height="50dp">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
