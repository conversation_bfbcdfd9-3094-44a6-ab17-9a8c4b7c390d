<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="5dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_15sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_15sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_2sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/native_banner_ad_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_5sdp"
                    android:orientation="vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close" />
                </RelativeLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_exit_logo" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_bold"
                        android:text="@string/saveChanges"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_17sdp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_6sdp"
                        android:fontFamily="@font/inter_medium"
                        android:singleLine="true"
                        android:text="@string/changesBeLost"
                        android:textColor="@color/unselected_txt_color"
                        android:textSize="@dimen/_13sdp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:singleLine="true"
                        android:text="@string/saveChangesBeforeLost"
                        android:textColor="@color/unselected_txt_color"
                        android:textSize="@dimen/_13sdp" />

                    <TextView
                        android:id="@+id/iv_exit"
                        android:layout_width="@dimen/_100sdp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_25sdp"
                        android:textSize="@dimen/_15sdp"
                        android:text="@string/exit"
                        android:textColor="@color/white"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:paddingBottom="@dimen/_5sdp"
                        android:paddingTop="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:background="@drawable/round_shape" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
