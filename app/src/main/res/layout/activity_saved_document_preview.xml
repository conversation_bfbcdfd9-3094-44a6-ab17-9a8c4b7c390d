<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="5dp"
            android:background="@color/fg_color">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_29sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/iv_back"
                android:layout_centerVertical="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/savedDocument"
                android:layout_marginLeft="@dimen/_8sdp"
                android:textColor="@color/txt_color"
                android:textSize="@dimen/_15sdp" />

            <ImageView
                android:id="@+id/iv_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_6sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_home" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center">

            <pdf.free.camscanner.docscanner.document_view.ViewPagerFixed
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:layout_margin="@dimen/_15sdp" />

            <TextView
                android:id="@+id/tv_page"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/_10sdp"
                android:background="@drawable/page_indicator_bg"
                android:fontFamily="@font/inter_medium"
                android:paddingStart="@dimen/_12sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_12sdp"
                android:paddingBottom="@dimen/_5sdp"
                android:text="1/2"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />
        </RelativeLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_saved_tools"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_10sdp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:layout_height="50dp">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
