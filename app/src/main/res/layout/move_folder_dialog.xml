<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_2sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/moveFolder"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_15sdp" />

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close_black_24dp"
                        android:tint="@color/txt_color" />
                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/ll_new_folder_add"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_gravity="center"
                        android:tint="@color/colorPrimary"
                        android:src="@drawable/ic_folder_plus_icon"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/createNewFolder"
                        android:textSize="15dp"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:layout_marginLeft="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:textColor="@color/colorPrimary"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:orientation="vertical">

                    <RadioGroup
                        android:id="@+id/radio_group"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_12sdp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_move"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_30sdp"
                        android:background="@drawable/done_btn_bg"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/move"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_13sdp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
