<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:elevation="5dp"
                android:background="@color/fg_color">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_28sdp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_2sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_7sdp"
                    android:src="@drawable/ic_back"
                    android:tint="@color/black" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/editorDocument"
                    android:layout_marginLeft="@dimen/_8sdp"
                    android:layout_toRightOf="@+id/iv_back"
                    android:textColor="@color/txt_color"
                    android:textSize="@dimen/_15sdp" />

                <ImageView
                    android:id="@+id/iv_done"
                    android:layout_width="@dimen/_26sdp"
                    android:layout_height="@dimen/_26sdp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_6sdp"
                    android:src="@drawable/ic_check_mark"
                    android:tint="@color/black" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_main"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center">

               <!-- <com.takwolf.android.aspectratio.AspectRatioLayout
                    android:id="@+id/ly_aspectratio"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true">-->

                    <ja.burhanrashid52.photoeditor.PhotoEditorView
                        android:id="@+id/iv_editImg"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true">

                        <com.xiaopo.flying.sticker.StickerView
                            android:id="@+id/stickerView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            app:showBorder="true"
                            app:showIcons="true" />
                    </ja.burhanrashid52.photoeditor.PhotoEditorView>

                    <ImageView
                        android:id="@+id/iv_overlayImg"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:alpha="0.3"
                        android:scaleType="fitXY"
                        android:visibility="gone" />
<!--                </com.takwolf.android.aspectratio.AspectRatioLayout>-->
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ly_edit_tools"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:orientation="vertical"
            android:paddingTop="@dimen/_10sdp"
            android:paddingBottom="@dimen/_10sdp"
            android:visibility="visible">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_edit_tools"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_4sdp" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_color_filter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp">

            <ImageView
                android:id="@+id/iv_close_filter"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/colorFilter"
                android:textColor="@color/white"
                android:textSize="@dimen/_13sdp" />

            <ImageView
                android:id="@+id/iv_apply_filter"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_color_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_4sdp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_adjust"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:paddingTop="@dimen/_5sdp"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp">

            <ImageView
                android:id="@+id/iv_close_adjust"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/adjust"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_adjust"
                android:layout_width="@dimen/_25sdp"
                android:layout_height="@dimen/_25sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_18sdp"
                android:fontFamily="@font/inter_medium"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="@dimen/_11sdp" />

            <SeekBar
                android:id="@+id/sb_adjust"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_toStartOf="@+id/tv_progress"
                android:max="255"
                android:maxHeight="2dp"
                android:paddingTop="@dimen/_8sdp"
                android:paddingBottom="@dimen/_8sdp"
                android:progress="128"
                android:progressDrawable="@drawable/seekbar_opacity"
                android:thumb="@drawable/ic_thumb" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/_8sdp">


            <LinearLayout
                android:id="@+id/llBrightness"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_brightness"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:src="@drawable/ic_brightness_selection" />

                <TextView
                    android:id="@+id/txtBrightness"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/brightness"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llContrast"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_contrast"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:src="@drawable/ic_contrast" />

                <TextView
                    android:id="@+id/txtContrast"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/contrast"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llSaturation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_saturation"
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:src="@drawable/ic_saturation" />

                <TextView
                    android:id="@+id/txtSaturation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/saturation"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llExposure"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_exposure"
                    android:layout_width="@dimen/_19sdp"
                    android:layout_height="@dimen/_19sdp"
                    android:src="@drawable/ic_exposure" />

                <TextView
                    android:id="@+id/txtExposure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/exposure"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_opacity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"

        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp">

            <ImageView
                android:id="@+id/iv_close_opacity"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/Opacity"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_opacity"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/_12sdp"
            android:paddingEnd="@dimen/_12sdp">

            <SeekBar
                android:id="@+id/sb_opacity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:background="@color/transparent"
                android:max="255"
                android:maxHeight="2dp"
                android:paddingTop="@dimen/_9sdp"
                android:paddingBottom="@dimen/_9sdp"
                android:progress="255"
                android:progressDrawable="@drawable/seekbar_opacity"
                android:thumb="@drawable/ic_thumb" />
        </RelativeLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_highlight"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:paddingTop="@dimen/_5sdp"
        android:paddingBottom="@dimen/_5sdp"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp">

            <ImageView
                android:id="@+id/iv_close_highlight"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/highlight"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_highlight"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_brush_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_18sdp"
                android:fontFamily="@font/inter_medium"
                android:text="@string/size"
                android:textColor="@color/white"
                android:textSize="@dimen/_11sdp" />

            <SeekBar
                android:id="@+id/sb_highlight_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_7sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_toEndOf="@+id/tv_brush_size"
                android:max="100"
                android:maxHeight="2dp"
                android:paddingTop="@dimen/_8sdp"
                android:paddingBottom="@dimen/_8sdp"
                android:progress="25"
                android:progressDrawable="@drawable/seekbar_opacity"
                android:thumb="@drawable/ic_thumb" />

            <SeekBar
                android:id="@+id/sb_eraser_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_7sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_toEndOf="@+id/tv_brush_size"
                android:max="100"
                android:maxHeight="2dp"
                android:paddingTop="@dimen/_8sdp"
                android:paddingBottom="@dimen/_8sdp"
                android:progress="25"
                android:progressDrawable="@drawable/seekbar_opacity"
                android:thumb="@drawable/ic_thumb"
                android:visibility="gone" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/llHighLight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical"
                android:padding="@dimen/_4sdp">

                <ImageView
                    android:id="@+id/iv_highlight"
                    android:layout_width="@dimen/_18sdp"
                    android:layout_height="@dimen/_18sdp"
                    android:src="@drawable/ic_highlight" />

                <TextView
                    android:id="@+id/txtHighlight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/highlight"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llEraser"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical"
                android:padding="@dimen/_4sdp">

                <ImageView
                    android:id="@+id/iv_erase"
                    android:layout_width="@dimen/_18sdp"
                    android:layout_height="@dimen/_18sdp"
                    android:src="@drawable/bic_erase" />

                <TextView
                    android:id="@+id/txtEraser"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/eraser"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical"
                android:padding="@dimen/_4sdp">

                <ImageView
                    android:id="@+id/iv_color"
                    android:layout_width="@dimen/_24sdp"
                    android:layout_height="@dimen/_24sdp"
                    android:src="@drawable/bic_color" />

                <TextView
                    android:id="@+id/txtColor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_signature"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        android:background="@color/fg_color"
        android:visibility="gone">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/rl_bottom">

            <com.kyanogen.signatureview.SignatureView
                android:id="@+id/signature_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:backgroundColor="#00000000"
                app:enableSignature="true"
                app:penColor="@color/txt_color"
                app:penSize="5dp" />


            <RelativeLayout
                android:id="@+id/rl_signature_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:visibility="gone">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_signature"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_4sdp" />

                <TextView
                    android:id="@+id/tv_no_signature"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginBottom="@dimen/_7sdp"
                    android:fontFamily="@font/inter_medium"
                    android:padding="@dimen/_10sdp"
                    android:singleLine="true"
                    android:text="@string/noSignFound"
                    android:textColor="@color/txt_color"
                    android:textSize="@dimen/_14sdp" />
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_clear_signature"
                android:layout_width="@dimen/_72sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_margin="@dimen/_15sdp"
                android:background="@drawable/clear_btn_bg"
                android:fontFamily="@font/inter_medium"
                android:gravity="center"
                android:onClick="onClick"
                android:text="@string/clear"
                android:textColor="@color/black"
                android:textSize="@dimen/_13sdp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_bottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp"
            android:layout_above="@+id/ly_seek_view"
            android:background="@color/colorPrimary">

            <ImageView
                android:id="@+id/iv_close_signature"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/signature"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_signature"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ly_seek_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/ly_btn"
            android:background="@color/colorPrimary"
            android:orientation="vertical"
            android:visibility="visible">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_pen_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_18sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/size"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_11sdp" />

                <SeekBar
                    android:id="@+id/sb_pen_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_7sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:layout_toEndOf="@+id/tv_pen_size"
                    android:max="50"
                    android:maxHeight="2dp"
                    android:paddingTop="@dimen/_8sdp"
                    android:paddingBottom="@dimen/_8sdp"
                    android:progress="20"
                    android:progressDrawable="@drawable/seekbar_opacity"
                    android:thumb="@drawable/ic_thumb" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_7sdp"
                android:layout_marginBottom="@dimen/_10sdp">

                <TextView
                    android:id="@+id/tv_pen_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_18sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_11sdp" />

                <com.divyanshu.colorseekbar.ColorSeekBar
                    android:id="@+id/sb_pen_color"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_9sdp"
                    android:layout_marginEnd="@dimen/_13sdp"
                    android:layout_toEndOf="@+id/tv_pen_color"
                    android:paddingTop="@dimen/_5sdp"
                    android:paddingBottom="@dimen/_5sdp"
                    app:colorSeeds="@array/colors" />
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ly_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@color/colorPrimary"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/_5sdp">

            <LinearLayout
                android:id="@+id/llCreateSig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_create_signature"
                    android:layout_width="@dimen/_25sdp"
                    android:layout_height="@dimen/_25sdp"
                    android:padding="@dimen/_4sdp"
                    android:src="@drawable/ic_create_sig" />

                <TextView
                    android:id="@+id/txtCreateSig"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/createSig"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llSavedSig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/_15sdp"
                android:gravity="center"
                android:onClick="onClick"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_saved_signature"
                    android:layout_width="@dimen/_24sdp"
                    android:layout_height="@dimen/_24sdp"
                    android:padding="@dimen/_4sdp"
                    android:src="@drawable/ic_saved_sig" />

                <TextView
                    android:id="@+id/txtSavedSig"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/saved_sig"
                    android:textColor="@color/light_bg_color"
                    android:textSize="@dimen/_10sdp" />
            </LinearLayout>
        </LinearLayout>


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_watermark"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        android:background="@color/fg_color"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/rl_bottom2"
            android:background="@color/bg_color">

            <EditText
                android:id="@+id/et_watermark_txt"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_110sdp"
                android:layout_marginStart="@dimen/_12sdp"
                android:layout_marginTop="@dimen/_20sdp"
                android:layout_marginEnd="@dimen/_12sdp"
                android:background="@drawable/white_bg"
                android:cursorVisible="true"
                android:focusable="true"
                android:fontFamily="@font/inter_medium"
                android:gravity="top|left|center_vertical|center_horizontal|center|start"
                android:hint="@string/enterText"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:padding="@dimen/_10sdp"
                android:textColor="@color/txt_color"
                android:textColorHint="@color/unselected_txt_color"
                android:textSize="@dimen/_15sdp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/fg_color"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_watermark_font"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_margin="@dimen/_4sdp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/rl_watermark_color"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_13sdp"
                    android:layout_marginBottom="@dimen/_8sdp"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_watermark_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/_18sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/color"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_11sdp" />

                    <com.divyanshu.colorseekbar.ColorSeekBar
                        android:id="@+id/sb_watermark_color"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_13sdp"
                        android:layout_toEndOf="@+id/tv_watermark_color"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        app:colorSeeds="@array/colors" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_watermark_opacity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="@dimen/_5sdp"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_watermark_opacity"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/_18sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/size"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_11sdp" />

                    <SeekBar
                        android:id="@+id/sb_watermark_opacity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_7sdp"
                        android:layout_marginEnd="@dimen/_10sdp"
                        android:layout_toEndOf="@+id/tv_watermark_opacity"
                        android:max="255"
                        android:maxHeight="2dp"
                        android:paddingTop="@dimen/_8sdp"
                        android:paddingBottom="@dimen/_8sdp"
                        android:progress="255"
                        android:progressDrawable="@drawable/watermark_seekbar_opacity"
                        android:thumb="@drawable/ic_thumb" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_bottom2"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp"
            android:layout_above="@+id/ly_btn2"
            android:background="@color/colorPrimary">

            <ImageView
                android:id="@+id/iv_close_watermark"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/watermark"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_watermark"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ly_btn2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/colorPrimary"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/_6sdp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_watermark_font"
                    android:layout_width="@dimen/_24sdp"
                    android:layout_height="@dimen/_24sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_4sdp"
                    android:src="@drawable/ic_txt_font" />

                <TextView
                    android:id="@+id/txtFont"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/font"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_15sdp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_watermark_color"
                    android:layout_width="@dimen/_27sdp"
                    android:layout_height="@dimen/_27sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_4sdp"
                    android:src="@drawable/bic_color" />

                <TextView
                    android:id="@+id/txtWatermarkColor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_15sdp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_watermark_opacity"
                    android:layout_width="@dimen/_24sdp"
                    android:layout_height="@dimen/_24sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_4sdp"
                    android:src="@drawable/ic_brightness" />

                <TextView
                    android:id="@+id/txtOpacity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/Opacity"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>


        </LinearLayout>


    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ly_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/fg_color"
        android:orientation="vertical"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp"
            android:background="@color/colorPrimary">

            <ImageView
                android:id="@+id/iv_close_txt"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_close"
                android:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/text"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_txt"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_check_mark"
                android:tint="@color/white" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:paddingBottom="@dimen/_5sdp"
            android:background="@color/colorPrimary"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_font"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_margin="@dimen/_4sdp"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/rl_txt_color"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_13sdp"
                android:layout_marginBottom="@dimen/_8sdp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_txt_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_18sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_11sdp" />

                <com.divyanshu.colorseekbar.ColorSeekBar
                    android:id="@+id/sb_txt_color"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_13sdp"
                    android:layout_toEndOf="@+id/tv_txt_color"
                    android:paddingTop="@dimen/_5sdp"
                    android:paddingBottom="@dimen/_5sdp"
                    app:colorSeeds="@array/colors" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ly_alignment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_7sdp"
                android:layout_marginBottom="@dimen/_2sdp"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/iv_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:src="@drawable/ic_bold" />

                <ImageView
                    android:id="@+id/iv_italic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:src="@drawable/tic_italic" />

                <ImageView
                    android:id="@+id/iv_align_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:src="@drawable/tic_left" />

                <ImageView
                    android:id="@+id/iv_align_center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:src="@drawable/tic_center" />

                <ImageView
                    android:id="@+id/iv_align_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:src="@drawable/tic_right" />
            </LinearLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:gravity="center"
            android:paddingBottom="@dimen/_8sdp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_txt_font"
                    android:layout_width="@dimen/_28sdp"
                    android:layout_height="@dimen/_28sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_6sdp"
                    android:src="@drawable/ic_txt_font" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/font"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_15sdp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_txt_color"
                    android:layout_width="@dimen/_28sdp"
                    android:layout_height="@dimen/_28sdp"
                    android:onClick="onClick"
                    android:layout_gravity="center"
                    android:padding="@dimen/_4sdp"
                    android:src="@drawable/bic_color" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/color"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_15sdp"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_txt_alignment"
                    android:layout_width="@dimen/_28sdp"
                    android:layout_height="@dimen/_28sdp"
                    android:onClick="onClick"
                    android:padding="@dimen/_6sdp"
                    android:src="@drawable/ic_txt_alignment" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/alignment"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>



        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_overlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:paddingTop="@dimen/_5sdp"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_overlay_opacity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_18sdp"
                android:fontFamily="@font/inter_medium"
                android:text="@string/Opacity"
                android:textColor="@color/white"
                android:textSize="@dimen/_11sdp" />

            <SeekBar
                android:id="@+id/sb_overlay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_7sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_toEndOf="@+id/tv_overlay_opacity"
                android:max="100"
                android:maxHeight="2dp"
                android:paddingTop="@dimen/_8sdp"
                android:paddingBottom="@dimen/_6sdp"
                android:progress="100"
                android:progressDrawable="@drawable/seekbar_opacity"
                android:thumb="@drawable/ic_thumb" />
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp">

            <ImageView
                android:id="@+id/iv_close_overlay"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:tint="@color/white"
                android:src="@drawable/ic_close" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/overlay"
                android:textColor="@color/white"
                android:textSize="@dimen/_13sdp" />

            <ImageView
                android:id="@+id/iv_apply_overlay"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:tint="@color/white"
                android:src="@drawable/ic_check_mark" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_overlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_4sdp" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_color_effect"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:paddingTop="@dimen/_5sdp"
        android:paddingBottom="@dimen/_5sdp"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_35sdp">

            <ImageView
                android:id="@+id/iv_close_effect"
                android:layout_width="@dimen/_24sdp"
                android:layout_height="@dimen/_24sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:tint="@color/white"
                android:src="@drawable/ic_close" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/colorEffect"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <ImageView
                android:id="@+id/iv_apply_effect"
                android:layout_width="@dimen/_26sdp"
                android:layout_height="@dimen/_26sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_7sdp"
                android:onClick="onClick"
                android:padding="@dimen/_6sdp"
                android:tint="@color/white"
                android:src="@drawable/ic_check_mark" />


        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_color_effect"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_4sdp" />
    </LinearLayout>
</RelativeLayout>
