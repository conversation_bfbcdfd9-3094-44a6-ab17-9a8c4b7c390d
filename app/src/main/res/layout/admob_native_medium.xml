<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ad_unit"
    android:layout_width="match_parent"
    android:layout_height="300dp"
    android:background="@drawable/outline"
    android:orientation="vertical">

    <com.google.android.gms.ads.nativead.NativeAdView
        android:id="@+id/native_ad_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/top_parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp">

                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/icon"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="8dp"
                    app:riv_corner_radius="5dp" />

                <LinearLayout
                    android:id="@+id/ad_choices_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentRight="true"
                    android:gravity="end"
                    android:orientation="horizontal" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/ad_choices_container"
                    android:layout_toLeftOf="@+id/ad_choices_container"
                    android:layout_toEndOf="@+id/icon"
                    android:layout_toRightOf="@+id/icon"
                    android:orientation="vertical"
                    android:paddingLeft="6dp"
                    android:paddingRight="6dp">

                    <TextView
                        android:id="@+id/primary"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"

                        android:lines="1"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/secondary"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"

                        android:maxLines="2"
                        android:textColor="@color/text_second"
                        android:textSize="12sp" />
                </LinearLayout>
            </RelativeLayout>

            <com.google.android.gms.ads.nativead.MediaView
                android:id="@+id/media_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/cta_parent"
                android:layout_below="@+id/top_parent"
                android:layout_marginLeft="3dp"
                android:layout_marginRight="6dp"
                android:gravity="center" />

            <LinearLayout
                android:id="@+id/cta_parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="8dp"
                android:orientation="vertical"
                android:padding="8dp">

                <LinearLayout
                    android:id="@+id/third_line"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tertiary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"

                        android:maxLines="2"
                        android:textColor="@color/white"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp">

                    <TextView
                        android:id="@+id/cta"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:background="@drawable/ad_button_circle"
                        android:gravity="center"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:textAllCaps="true"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:background="#40000000"
            android:paddingStart="5dp"
            android:paddingLeft="3dp"
            android:paddingRight="3dp"
            android:text="Ad"
            android:textColor="#ffffff"
            android:textSize="12sp" />
    </com.google.android.gms.ads.nativead.NativeAdView>
</LinearLayout>
