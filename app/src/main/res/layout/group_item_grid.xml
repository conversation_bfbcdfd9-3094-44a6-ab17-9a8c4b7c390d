<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="@dimen/_130sdp"
        android:layout_margin="@dimen/_6sdp"
        app:cardBackgroundColor="@color/fg_color"
        app:cardCornerRadius="@dimen/_6sdp">

        <RelativeLayout
            android:id="@+id/rl_group"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <pdf.free.camscanner.docscanner.document_view.roundshapeImageview
                android:id="@+id/iv_group_first_img"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop" />

            <pdf.free.camscanner.docscanner.document_view.roundshapeImageview
                android:id="@+id/iv_group_folder_img"
                android:layout_width="@dimen/_45sdp"
                android:layout_height="@dimen/_45sdp"
                android:layout_centerInParent="true"
                android:src="@color/white" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@drawable/bottom_rounded_shape">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/_7sdp"
                    android:layout_toStartOf="@+id/iv_group_more"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/_4sdp"
                    android:paddingBottom="@dimen/_4sdp">

                    <TextView
                        android:id="@+id/tv_group_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:singleLine="true"
                        android:text="Meeting Notes"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_12sdp" />

                    <TextView
                        android:id="@+id/tv_group_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_1sdp"
                        android:fontFamily="@font/inter_medium"
                        android:maxLength="11"
                        android:singleLine="true"
                        android:text="2020-08-14  10.03 PM"
                        android:textColor="@color/unselected_txt_color"
                        android:textSize="@dimen/_10sdp" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/iv_group_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:paddingStart="@dimen/_8sdp"
                    android:paddingTop="@dimen/_10sdp"
                    android:paddingEnd="@dimen/_8sdp"
                    android:paddingBottom="@dimen/_10sdp"
                    android:src="@drawable/ic_more" />
            </RelativeLayout>
        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>
