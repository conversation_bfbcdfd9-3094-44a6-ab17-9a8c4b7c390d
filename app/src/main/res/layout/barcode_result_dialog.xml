<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">

                <include
                    android:id="@+id/admob_native_container"
                    layout="@layout/admob_native_medium"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:layout_marginRight="@dimen/_5sdp"
                    android:layout_height="@dimen/_200sdp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:fontFamily="@font/inter_medium"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/qrCodeResult"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14sdp" />

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close_black_24dp"
                        android:tint="@color/black" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_result"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_15sdp"
                    android:layout_marginTop="@dimen/_8sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/qrReader"
                    android:textAlignment="textStart"
                    android:textColor="@color/unselected_txt_color"
                    android:textSize="@dimen/_13sdp" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_3sdp"
                android:layout_marginEnd="@dimen/_3sdp"
                android:layout_marginBottom="@dimen/_3sdp">

                <TextView
                    android:id="@+id/tv_share"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@+id/tv_search"
                    android:fontFamily="@font/inter_medium"
                    android:padding="@dimen/_11sdp"
                    android:text="@string/share"
                    android:textColor="@color/light_txt_color"
                    android:textSize="@dimen/_13sdp" />

                <TextView
                    android:id="@+id/tv_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:fontFamily="@font/inter_medium"
                    android:padding="@dimen/_11sdp"
                    android:text="@string/search"
                    android:textColor="@color/light_txt_color"
                    android:textSize="@dimen/_13sdp" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
