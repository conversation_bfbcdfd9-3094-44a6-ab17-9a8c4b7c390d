<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_main"
    android:layout_width="@dimen/_80sdp"
    android:layout_height="@dimen/_25sdp"
    android:layout_centerInParent="true"
    android:layout_marginStart="@dimen/_6sdp"
    android:layout_marginTop="@dimen/_10sdp"
    android:layout_marginEnd="@dimen/_6sdp"
    android:layout_marginBottom="@dimen/_4sdp"
    android:background="@drawable/unselected_font_bg">

    <TextView
        android:id="@+id/tv_fontStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:singleLine="true"
        android:text="Sample"
        android:textColor="@color/txt_color"
        android:textSize="@dimen/_12sdp" />
</RelativeLayout>
