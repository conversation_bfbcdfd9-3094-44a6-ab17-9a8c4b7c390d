<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:elevation="5dp"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/fg_color">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_29sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back"
                app:tint="@color/selected_txt_color" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:layout_toStartOf="@+id/iv_create_pdf"
                android:layout_toEndOf="@+id/iv_back"
                android:fontFamily="@font/inter_medium"
                android:singleLine="true"
                android:text="@string/app_name"
                android:textColor="@color/txt_color"
                android:textSize="@dimen/_15sdp" />

            <ImageView
                android:id="@+id/iv_create_pdf"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/iv_doc_more"
                android:onClick="onClick"
                android:padding="@dimen/_4sdp"
                android:src="@drawable/ic_create_pdf" />

            <ImageView
                android:id="@+id/iv_doc_more"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:onClick="onClick"
                android:padding="@dimen/_4sdp"
                android:src="@drawable/ic_more" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_6sdp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_group_doc"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/_3sdp" />

            <LinearLayout
                android:id="@+id/ly_doc_camera"
                android:layout_width="@dimen/_50sdp"
                android:layout_height="@dimen/_45sdp"
                android:layout_alignParentEnd="true"
                android:layout_marginBottom="@dimen/_10sdp"
                android:layout_alignParentBottom="true"
                android:background="@drawable/left_rounded_bg"
                android:gravity="center"
                android:onClick="onClick"
                android:elevation="5dp"
                android:visibility="gone"
                android:paddingLeft="@dimen/_8sdp">

                <ImageView
                    android:id="@+id/iv_doc_camera"
                    android:layout_width="@dimen/_24sdp"
                    android:layout_height="@dimen/_24sdp"
                    android:src="@drawable/ic_camera" />

            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
