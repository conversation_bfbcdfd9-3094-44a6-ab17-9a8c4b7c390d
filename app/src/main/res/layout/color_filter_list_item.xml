<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_7sdp"
    android:layout_marginTop="@dimen/_10sdp"
    android:layout_marginEnd="@dimen/_7sdp"
    android:layout_marginBottom="@dimen/_4sdp"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ly_img"
        android:layout_width="@dimen/_45sdp"
        android:layout_height="@dimen/_45sdp"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/_1sdp">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/_5sdp">

            <ImageView
                android:id="@+id/iv_filter_view"
                android:layout_width="@dimen/_45sdp"
                android:layout_height="@dimen/_45sdp"
                android:scaleType="centerCrop" />
        </androidx.cardview.widget.CardView>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_filter_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6sdp"
        android:fontFamily="@font/inter_medium"
        android:singleLine="true"
        android:text="Original"
        android:textColor="@color/white"
        android:textSize="@dimen/_9sdp" />
</LinearLayout>
