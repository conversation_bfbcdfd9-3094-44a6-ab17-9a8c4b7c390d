<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="5dp"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_29sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:onClick="onClick"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/_5sdp"
                android:layout_toEndOf="@+id/iv_back"
                android:fontFamily="@font/inter_medium"
                android:text="@string/savedDocument"
                android:textColor="@color/black"
                android:textSize="@dimen/_15sdp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/ly_bottom"
                android:layout_centerInParent="true"
                android:layout_margin="@dimen/_15sdp">

                <ImageView
                    android:id="@+id/iv_preview_saved"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true" />
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ly_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/toolbar_color"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/_10sdp"
                android:paddingBottom="@dimen/_10sdp">

                <LinearLayout
                    android:id="@+id/llRetake"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_retake"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_retake" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/retake"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_11sdp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llEdit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onClick"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:tint="@color/white"
                        android:src="@drawable/ic_edit" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/edit"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_11sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="onClick"
                    android:id="@+id/llRotate"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_rotate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_rotate_doc" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/rotate"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_11sdp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="onClick"
                    android:id="@+id/llDelete"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_delete"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"

                        android:src="@drawable/ic_delete" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/delete"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_11sdp" />

                </LinearLayout>
            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
