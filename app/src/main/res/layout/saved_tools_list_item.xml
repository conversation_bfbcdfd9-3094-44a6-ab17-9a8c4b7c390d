<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:layout_marginTop="@dimen/_5sdp"
    android:layout_marginBottom="@dimen/_5sdp"
    android:layout_marginLeft="@dimen/_10sdp"
    android:layout_marginRight="@dimen/_10sdp"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_toolIcon"
        android:layout_width="@dimen/_22sdp"
        android:layout_height="@dimen/_22sdp"
        android:padding="@dimen/_2sdp" />

    <TextView
        android:id="@+id/txtIconName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5sdp"
        android:textSize="@dimen/_8sdp"
        android:textColor="@color/white"/>
</LinearLayout>
