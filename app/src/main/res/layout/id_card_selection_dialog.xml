<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_20sdp"
    android:layout_marginTop="@dimen/_5sdp"
    android:layout_marginEnd="@dimen/_20sdp"
    android:orientation="vertical"
    app:cardCornerRadius="@dimen/_5sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_color1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_30sdp"
                    android:background="@drawable/rounded_toolbar_5dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:fontFamily="@font/inter_medium"
                        android:padding="@dimen/_5sdp"
                        android:text="ID Card"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_13sdp" />

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close_black_24dp"
                        android:tint="@color/white" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_12sdp"
                        android:layout_marginEnd="@dimen/_12sdp"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">


                            <TextView
                                android:id="@+id/txtTitle1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/inter_medium"
                                android:text="Single side ID Card"
                                android:textColor="@color/selected_txt_color"
                                android:textSize="@dimen/_12sdp" />

                            <TextView
                                android:id="@+id/tv_select1"
                                android:layout_width="@dimen/_40sdp"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="@dimen/_5sdp"
                                android:layout_toRightOf="@+id/txtTitle1"
                                android:background="@drawable/select_btn_bg"
                                android:gravity="center"
                                android:padding="@dimen/_2sdp"
                                android:text="Select"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_10sdp" />
                        </RelativeLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/txtTitle1"
                            android:layout_gravity="start"
                            android:layout_marginEnd="@dimen/_6sdp"
                            android:text="@string/single_id_card_txt"
                            android:textColor="@color/unselected_txt_color"
                            android:textSize="@dimen/_11sdp" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_12sdp"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:layout_marginEnd="@dimen/_12sdp"
                        android:layout_marginBottom="@dimen/_5sdp"
                        android:orientation="vertical">


                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txtTitle2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/inter_medium"
                                android:text="Two side ID Card"
                                android:textColor="@color/selected_txt_color"
                                android:textSize="@dimen/_12sdp" />

                            <TextView
                                android:id="@+id/tv_select2"
                                android:layout_width="@dimen/_40sdp"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginLeft="@dimen/_5sdp"
                                android:layout_toRightOf="@+id/txtTitle2"
                                android:background="@drawable/select_btn_bg"
                                android:gravity="center"
                                android:padding="@dimen/_2sdp"
                                android:text="Select"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_10sdp" />

                        </RelativeLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start"

                            android:layout_marginEnd="@dimen/_6sdp"
                            android:text="@string/two_id_card_txt"
                            android:textColor="@color/unselected_txt_color"
                            android:textSize="@dimen/_11sdp" />

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
