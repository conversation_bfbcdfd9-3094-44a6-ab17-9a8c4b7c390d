<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_2sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">

                <include
                    android:id="@+id/admob_native_container"
                    layout="@layout/admob_native_medium"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginRight="@dimen/_5sdp"
                    android:layout_height="@dimen/_200sdp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/inter_medium"
                        android:padding="@dimen/_5sdp"
                        android:singleLine="true"
                        android:text="@string/inputText"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_15sdp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_15sdp"
                    android:layout_marginTop="@dimen/_18sdp"
                    android:layout_marginEnd="@dimen/_15sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="@drawable/et_bg"
                    android:paddingTop="@dimen/_11sdp"
                    android:paddingBottom="@dimen/_11sdp">

                    <EditText
                        android:id="@+id/et_input_txt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:background="@color/transparent"
                        android:cursorVisible="true"
                        android:focusable="true"
                        android:fontFamily="@font/inter_medium"
                        android:hint="@string/enterText"
                        android:imeOptions="actionDone"
                        android:inputType="text"
                        android:textColor="#000000"
                        android:textColorHint="@color/unselected_txt_color"
                        android:textSize="@dimen/_12sdp" />
                </RelativeLayout>
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_3sdp"
                android:layout_marginEnd="@dimen/_3sdp"
                android:layout_marginBottom="@dimen/_4sdp">

                <TextView
                    android:id="@+id/tv_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@+id/tv_done"
                    android:fontFamily="@font/inter_medium"
                    android:padding="@dimen/_11sdp"
                    android:text="@string/cancel"
                    android:textColor="@color/light_txt_color"
                    android:textSize="@dimen/_13sdp" />

                <TextView
                    android:id="@+id/tv_done"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:fontFamily="@font/inter_medium"
                    android:padding="@dimen/_11sdp"
                    android:text="@string/ok"
                    android:textColor="@color/light_txt_color"
                    android:textSize="@dimen/_13sdp" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
