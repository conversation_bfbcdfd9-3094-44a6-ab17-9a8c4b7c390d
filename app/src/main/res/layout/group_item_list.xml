<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_5sdp"
    android:layout_marginEnd="@dimen/_5sdp"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_7sdp"
        app:cardBackgroundColor="@color/fg_color"
        app:cardCornerRadius="@dimen/_6sdp">

        <RelativeLayout
            android:id="@+id/rl_group"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/_1sdp"
            android:paddingStart="@dimen/_10sdp"
            android:paddingTop="@dimen/_9sdp"
            android:paddingEnd="@dimen/_3sdp"
            android:paddingBottom="@dimen/_9sdp">

            <RelativeLayout
                android:id="@+id/ll_top"
                android:layout_width="@dimen/_45sdp"
                android:layout_height="@dimen/_45sdp"
                android:layout_alignParentLeft="true"
                android:orientation="horizontal">

                <pdf.free.camscanner.docscanner.document_view.roundshapeImageview
                    android:id="@+id/iv_group_first_img"
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop"
                    android:src="@color/white" />

                <pdf.free.camscanner.docscanner.document_view.roundshapeImageview
                    android:id="@+id/iv_group_folder_img"
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    android:padding="@dimen/_10sdp"
                    android:layout_centerVertical="true"
                    android:src="@color/white" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_12sdp"
                android:layout_marginEnd="@dimen/_7sdp"
                android:layout_toStartOf="@+id/iv_group_more"
                android:layout_toEndOf="@+id/ll_top"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_group_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:singleLine="true"
                    android:text="Meeting Notes"
                    android:textColor="@color/txt_color"
                    android:textSize="@dimen/_13sdp" />

                <TextView
                    android:id="@+id/tv_group_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:fontFamily="@font/inter_medium"
                    android:singleLine="true"
                    android:text="2020-08-14  10:03 PM"
                    android:textColor="@color/unselected_txt_color"
                    android:textSize="@dimen/_10sdp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_group_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_more" />
        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>
