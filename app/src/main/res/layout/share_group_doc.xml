<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:background="@color/dialog_bg_color"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:background="@color/dialog_bg_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_2sdp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical">

                <include
                    android:id="@+id/admob_native_container"
                    layout="@layout/admob_native_medium"
                    android:layout_width="match_parent"
                    android:layout_marginLeft="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginRight="@dimen/_5sdp"
                    android:layout_height="@dimen/_200sdp"
                    android:visibility="gone" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_15sdp"
                        android:fontFamily="@font/inter_medium"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/share"
                        android:textColor="@color/txt_color"
                        android:textSize="@dimen/_15sdp" />

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/_7sdp"
                        android:padding="@dimen/_4sdp"
                        android:src="@drawable/ic_close_black_24dp"
                        android:tint="@color/txt_color" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_15sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_15sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/rl_share_img"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_25sdp">

                        <ImageView
                            android:id="@+id/iv_img"
                            android:layout_width="@dimen/_23sdp"
                            android:layout_height="@dimen/_23sdp"
                            android:padding="@dimen/_1sdp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/ic_save_as_gallery" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="@dimen/_15sdp"
                            android:layout_toEndOf="@+id/iv_img"
                            android:fontFamily="@font/inter_medium"
                            android:singleLine="true"
                            android:text="@string/saveAsImage"
                            android:textColor="@color/txt_color"
                            android:textSize="@dimen/_13sdp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rl_share_pdf"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_25sdp">

                        <ImageView
                            android:id="@+id/iv_pdf"
                            android:padding="@dimen/_1sdp"
                            android:layout_width="@dimen/_23sdp"
                            android:layout_height="@dimen/_23sdp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/ic_save_as_pdf" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="@dimen/_15sdp"
                            android:layout_toEndOf="@+id/iv_pdf"
                            android:fontFamily="@font/inter_medium"
                            android:singleLine="true"
                            android:text="@string/shareAsPdf"
                            android:textColor="@color/txt_color"
                            android:textSize="@dimen/_13sdp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rl_share_pdf_pswrd"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_40sdp">

                        <ImageView
                            android:id="@+id/iv_pdf_pswrd"
                            android:layout_width="@dimen/_23sdp"
                            android:layout_height="@dimen/_23sdp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/ic_lock" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="@dimen/_15sdp"
                            android:layout_toEndOf="@+id/iv_pdf_pswrd"
                            android:fontFamily="@font/inter_medium"
                            android:singleLine="true"
                            android:text="@string/sharePdfPass"
                            android:textColor="@color/txt_color"
                            android:textSize="@dimen/_13sdp" />
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
