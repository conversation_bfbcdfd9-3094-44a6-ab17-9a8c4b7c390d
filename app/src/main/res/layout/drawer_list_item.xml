<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_item"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_height="wrap_content"
        android:padding="@dimen/_11sdp">

        <ImageView
            android:id="@+id/iv_item_icon"
            android:layout_width="@dimen/_14sdp"
            android:layout_height="@dimen/_14sdp"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_share_app" />

        <TextView
            android:id="@+id/tv_item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_15sdp"
            android:layout_toEndOf="@+id/iv_item_icon"
            android:fontFamily="@font/inter_medium"
            android:text="Share App"
            android:textColor="@color/txt_color"
            android:textSize="@dimen/_13sdp" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switchNightMode"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_30sdp"
            android:visibility="gone"
            android:layout_alignParentRight="true"/>
    </RelativeLayout>
</LinearLayout>
