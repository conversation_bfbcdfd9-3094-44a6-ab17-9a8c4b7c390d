<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:layout_gravity="center_vertical"
        android:background="#000000">

        <Button
            android:id="@+id/original"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:text="@string/original"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/magicColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:singleLine="true"
            android:text="@string/auto"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/grayMode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:singleLine="true"
            android:text="@string/gray"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/BWMode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:singleLine="true"
            android:text="@string/b_and_w"
            android:textColor="@android:color/white" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/topBar"
        android:layout_gravity="center"
        android:layout_margin="@dimen/scanPadding">

        <ImageView
            android:id="@+id/scannedImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/bottom_view"
            android:layout_gravity="center"
            android:layout_margin="@dimen/scanPadding" />

        <RelativeLayout
            android:id="@+id/bottom_view"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_alignParentBottom="true">

            <Button
                android:id="@+id/doneButton"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal"
                android:text="@string/done"
                android:textColor="@android:color/white" />
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>
