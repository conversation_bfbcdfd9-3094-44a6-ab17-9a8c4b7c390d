<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="5dp"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_28sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_medium"
                android:text="@string/note"
                android:textColor="@color/black"
                android:textSize="@dimen/_15sdp" />

            <ImageView
                android:id="@+id/iv_save_note"
                android:layout_width="@dimen/_28sdp"
                android:layout_height="@dimen/_28sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_6sdp"
                android:padding="@dimen/_6sdp"
                android:src="@drawable/ic_save_note"
                android:tint="@color/black" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <jp.wasabeef.richeditor.RichEditor
                android:id="@+id/editor"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:paddingTop="@dimen/_10sdp"
            android:paddingBottom="@dimen/_10sdp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ly_undo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_undo"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_undo" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/undo"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_redo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_redo"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_redo" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/redo"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_bold"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_bold_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/bold"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_italic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_italic"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_italic" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/italic"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_subscript"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_subscript"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_subscript" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/subscript"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_superscript"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_superscript"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_superscript" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/superscript"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_strikethrough"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_strikethrough"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_strek" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/strikethrough"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_underline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">


                    <ImageView
                        android:id="@+id/iv_underline"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_underline" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/underline"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_headline1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_heading1"
                        android:layout_gravity="center"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_headline1" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/headline1"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_headline2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_heading2"
                        android:layout_gravity="center"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_headline2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/headline2"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_heading3"
                        android:layout_gravity="center"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_headline3" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/headline3"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_headline4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_heading4"
                        android:layout_gravity="center"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_headline4" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/headline4"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ly_headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_heading5"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_headline5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/headline5"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_headline6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_heading6"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_headline6" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/headline6"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_red_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_txt_color"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_gravity="center"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_red_text" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/redText"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ly_indent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_indent"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_indent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/indent"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_outdent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_outdent"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_outdent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/outdent"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_align_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_align_left"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_left_text" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/leftText"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_align_center"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_center" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/center"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ly_right_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_align_right"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_right_text" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/rightText"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_insert_bullets"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_insert_bullets"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_line_dot" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/lineDot"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_numbers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_insert_numbers"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_number" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/number"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/_10sdp"
                    android:layout_marginRight="@dimen/_10sdp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_gravity="center"
                        android:id="@+id/iv_insert_checkbox"
                        android:layout_width="@dimen/_15sdp"
                        android:layout_height="@dimen/_15sdp"
                        android:src="@drawable/ic_check_box" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/_8sdp"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center"
                        android:text="@string/checkMark"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_9sdp" />
                </LinearLayout>

            </LinearLayout>
        </HorizontalScrollView>
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
