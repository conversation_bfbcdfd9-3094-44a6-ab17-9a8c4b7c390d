<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/bg_color1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="5dp"
            android:background="@color/fg_color">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_29sdp"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_2sdp"
                android:padding="@dimen/_7sdp"
                android:src="@drawable/ic_back"
                android:tint="@color/selected_txt_color" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:layout_toStartOf="@+id/tv_page"
                android:layout_toEndOf="@+id/iv_back"
                android:fontFamily="@font/inter_medium"
                android:singleLine="true"
                android:text="@string/app_name"
                android:textColor="@color/txt_color"
                android:textSize="@dimen/_15sdp" />

            <TextView
                android:id="@+id/tv_page"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_12sdp"
                android:fontFamily="@font/inter_medium"
                android:singleLine="true"
                android:text="1 / 2"
                android:textColor="@color/txt_color"
                android:textSize="@dimen/_14sdp" />
        </RelativeLayout>

        <com.github.barteksc.pdfviewer.PDFView
            android:id="@+id/pdfView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:layout_marginBottom="@dimen/_10sdp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:layout_height="50dp">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id" />
    </RelativeLayout>
</LinearLayout>
