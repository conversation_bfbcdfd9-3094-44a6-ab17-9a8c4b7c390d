package com.takwolf.android.aspectratio;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

public class AspectRatioLayout extends FrameLayout {
    public AspectRatioLayout(Context context) {
        super(context);
    }
    
    public AspectRatioLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    
    public AspectRatioLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    
    public void setAspectRatio(float ratio) {
        // Stub implementation
    }

    public void setAspectRatio(float width, float height) {
        // Stub implementation
    }
}
