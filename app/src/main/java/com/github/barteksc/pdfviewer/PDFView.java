package com.github.barteksc.pdfviewer;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import java.io.File;
import com.github.barteksc.pdfviewer.listener.OnLoadCompleteListener;
import com.github.barteksc.pdfviewer.listener.OnPageChangeListener;
import com.github.barteksc.pdfviewer.listener.OnPageErrorListener;
import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle;

public class PDFView extends View {
    public PDFView(Context context) {
        super(context);
    }
    
    public PDFView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    
    public PDFView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    
    public Configurator fromFile(File file) {
        return new Configurator();
    }

    public Configurator fromUri(android.net.Uri uri) {
        return new Configurator();
    }

    public com.shockwave.pdfium.PdfDocument.Meta getDocumentMeta() {
        return new com.shockwave.pdfium.PdfDocument.Meta();
    }

    public java.util.List<com.shockwave.pdfium.PdfDocument.Bookmark> getTableOfContents() {
        return new java.util.ArrayList<>();
    }
    
    public class Configurator {
        public Configurator defaultPage(int page) {
            return this;
        }
        
        public Configurator onPageChange(OnPageChangeListener listener) {
            return this;
        }
        
        public Configurator enableAnnotationRendering(boolean enable) {
            return this;
        }
        
        public Configurator onLoad(OnLoadCompleteListener listener) {
            return this;
        }
        
        public Configurator scrollHandle(DefaultScrollHandle handle) {
            return this;
        }
        
        public Configurator spacing(int spacing) {
            return this;
        }
        
        public Configurator onPageError(OnPageErrorListener listener) {
            return this;
        }
        
        public void load() {
            // Stub implementation
        }
    }
}
