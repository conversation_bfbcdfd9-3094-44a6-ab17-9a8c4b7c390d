package com.kyanogen.signatureview;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.view.View;

public class SignatureView extends View {
    public SignatureView(Context context) {
        super(context);
    }
    
    public SignatureView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    
    public SignatureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    
    public void clearCanvas() {
        // Stub implementation
    }
    
    public Bitmap getSignatureBitmap() {
        // Return a simple bitmap
        return Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888);
    }
    
    public boolean isBitmapEmpty() {
        return true;
    }
    
    public void setPenSize(float size) {
        // Stub implementation
    }
    
    public void setPenColor(int color) {
        // Stub implementation
    }
}
