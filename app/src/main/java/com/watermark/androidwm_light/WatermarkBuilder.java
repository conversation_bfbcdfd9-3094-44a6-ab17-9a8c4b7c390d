package com.watermark.androidwm_light;

import android.content.Context;
import android.graphics.Bitmap;
import android.widget.ImageView;
import com.watermark.androidwm_light.bean.WatermarkText;

public class WatermarkBuilder {
    public WatermarkBuilder(Context context) {
        // Stub implementation
    }

    public static WatermarkBuilder create(Context context, Object source) {
        return new WatermarkBuilder(context);
    }

    public WatermarkBuilder loadWatermarkText(WatermarkText watermarkText) {
        return this;
    }

    public WatermarkBuilder setTileMode(boolean tileMode) {
        return this;
    }

    public WatermarkResult getWatermark() {
        return new WatermarkResult();
    }

    public static class WatermarkResult {
        public void setToImageView(Object imageView) {
            // Stub implementation
        }
    }
}
