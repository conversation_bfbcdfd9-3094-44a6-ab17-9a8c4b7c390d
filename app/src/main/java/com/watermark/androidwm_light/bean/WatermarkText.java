package com.watermark.androidwm_light.bean;

public class WatermarkText {
    public WatermarkText(String text) {
        // Stub implementation
    }
    
    public WatermarkText setPositionX(double x) {
        return this;
    }
    
    public WatermarkText setPositionY(double y) {
        return this;
    }
    
    public WatermarkText setRotation(double rotation) {
        return this;
    }
    
    public WatermarkText setTextColor(int color) {
        return this;
    }
    
    public WatermarkText setTextSize(double size) {
        return this;
    }
    
    public WatermarkText setTextAlpha(int alpha) {
        return this;
    }

    public WatermarkText setTextFont(int font) {
        return this;
    }
}
