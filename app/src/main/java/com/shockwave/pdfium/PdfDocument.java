package com.shockwave.pdfium;

import java.util.List;

public class PdfDocument {
    public static class Bookmark {
        public String getTitle() {
            return "";
        }

        public boolean hasChildren() {
            return false;
        }

        public List<Bookmark> getChildren() {
            return null;
        }

        public long getPageIdx() {
            return 0;
        }
    }

    public static class Meta {
        public String getTitle() {
            return "";
        }

        public String getAuthor() {
            return "";
        }

        public String getSubject() {
            return "";
        }

        public String getKeywords() {
            return "";
        }

        public String getCreator() {
            return "";
        }

        public String getProducer() {
            return "";
        }

        public String getCreationDate() {
            return "";
        }

        public String getModDate() {
            return "";
        }
    }
}
